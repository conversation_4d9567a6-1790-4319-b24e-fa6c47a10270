#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 528482304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=11832, tid=2412
#
# JRE version:  (21.0.7+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+9-b631.52, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://github.com': 

Host: AMD Ryzen 7 7840HS w/ Radeon 780M Graphics     , 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sat Jul  5 17:03:55 2025 India Standard Time elapsed time: 2.032805 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x0000027946728d50):  JavaThread "Unknown thread" [_thread_in_vm, id=2412, stack(0x0000009be5200000,0x0000009be5300000) (1024K)]

Stack: [0x0000009be5200000,0x0000009be5300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5fb9]
V  [jvm.dll+0x8c4253]
V  [jvm.dll+0x8c67ae]
V  [jvm.dll+0x8c6e93]
V  [jvm.dll+0x289166]
V  [jvm.dll+0x6e2835]
V  [jvm.dll+0x6d62ba]
V  [jvm.dll+0x36376b]
V  [jvm.dll+0x36b336]
V  [jvm.dll+0x3bd6a6]
V  [jvm.dll+0x3bd978]
V  [jvm.dll+0x335edc]
V  [jvm.dll+0x336bcb]
V  [jvm.dll+0x88b6b9]
V  [jvm.dll+0x3ca878]
V  [jvm.dll+0x8747c8]
V  [jvm.dll+0x45f23e]
V  [jvm.dll+0x460f21]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffe15acb148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000279467cf6c0 WorkerThread "GC Thread#0"                     [id=5012, stack(0x0000009be5300000,0x0000009be5400000) (1024K)]
  0x00000279467e00f0 ConcurrentGCThread "G1 Main Marker"            [id=39280, stack(0x0000009be5400000,0x0000009be5500000) (1024K)]
  0x00000279467e0bf0 WorkerThread "G1 Conc#0"                       [id=3044, stack(0x0000009be5500000,0x0000009be5600000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe151b8f97]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe15b3fab8] Heap_lock - owner thread: 0x0000027946728d50

Heap address: 0x000000060b400000, size: 8012 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x000000060b400000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000002795af10000,0x000002795bec0000] _byte_map_base: 0x0000027957eb6000

Marking Bits: (CMBitMap*) 0x00000279467cfdc0
 Bits: [0x000002795bec0000, 0x0000027963bf0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.010 Loaded shared library C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff63fb70000 - 0x00007ff63fb7a000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\java.exe
0x00007ffee1f20000 - 0x00007ffee2185000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffee0d40000 - 0x00007ffee0e09000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffedf340000 - 0x00007ffedf728000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffedf070000 - 0x00007ffedf1bb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffecc2c0000 - 0x00007ffecc2d8000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\jli.dll
0x00007ffee1450000 - 0x00007ffee161a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffedf730000 - 0x00007ffedf757000 	C:\WINDOWS\System32\win32u.dll
0x00007ffedfcc0000 - 0x00007ffedfceb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffedfb80000 - 0x00007ffedfcb7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffedfa40000 - 0x00007ffedfae3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe17990000 - 0x00007ffe179ab000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\VCRUNTIME140.dll
0x00007ffebf1d0000 - 0x00007ffebf46a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffee1b60000 - 0x00007ffee1c09000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffee1350000 - 0x00007ffee1380000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe15dc0000 - 0x00007ffe15dcc000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\vcruntime140_1.dll
0x00007ffe9b3f0000 - 0x00007ffe9b47d000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\msvcp140.dll
0x00007ffe14e70000 - 0x00007ffe15c31000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\server\jvm.dll
0x00007ffee1390000 - 0x00007ffee1443000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffedfe30000 - 0x00007ffedfed6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffee0800000 - 0x00007ffee0915000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffee1ad0000 - 0x00007ffee1b44000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffeddc70000 - 0x00007ffeddcce000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffed2880000 - 0x00007ffed28b5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffed7710000 - 0x00007ffed771b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffeddc50000 - 0x00007ffeddc64000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffeddf50000 - 0x00007ffeddf6b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe15db0000 - 0x00007ffe15dba000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\jimage.dll
0x00007ffedbed0000 - 0x00007ffedc111000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffee09b0000 - 0x00007ffee0d35000 	C:\WINDOWS\System32\combase.dll
0x00007ffee1c10000 - 0x00007ffee1cf1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffeb9760000 - 0x00007ffeb9799000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffedf8e0000 - 0x00007ffedf979000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe15d90000 - 0x00007ffe15db0000 	C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Users\<USER>\.jbr\jbr_jcef-21.0.7-windows-x64-b631.52\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://github.com': 
java_class_path (initial): C:/Program Files/Android/Android Studio4/plugins/vcs-git/lib/git4idea-rt.jar;C:/Program Files/Android/Android Studio4/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 528482304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8401190912                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8401190912                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\openlogic-openjdk-jre-8u382-b05-windows-32
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0;C:\windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Windows;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\HP\OMEN-Broadcast\Common;C:\Program Files\Git\cmd;C:\Program Files\HP\HP One Agent;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0;%SYSTEMROOT%\System32\OpenSSH;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps
USERNAME=karan
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13200K (0% of 32807804K total physical memory with 1048796K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 3 days 1:26 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 116 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ss, avx512_ifma
Processor Information for the first 16 processors :
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801

Memory: 4k page, system-wide physical 32038M (1024M free)
TotalPageFile size 130342M (AvailPageFile size 305M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 71M, peak: 575M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b631.52) for windows-amd64 JRE (21.0.7+9-b631.52), built on 2025-04-30 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
