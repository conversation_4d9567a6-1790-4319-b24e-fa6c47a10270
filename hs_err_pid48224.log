#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 8257536 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3618), pid=48224, tid=31704
#
# JRE version:  (17.0.14+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.14+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @C:\Users\<USER>\AndroidStudioProjects\NoteActiveNewApplication\app\build\20250629_9267749246359249571.compiler.options

Host: AMD Ryzen 7 7840HS w/ Radeon 780M Graphics     , 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sun Jun 29 13:05:25 2025 India Standard Time elapsed time: 1.046640 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000017a7a8748d0):  JavaThread "Unknown thread" [_thread_in_vm, id=31704, stack(0x000000568eb00000,0x000000568ec00000)]

Stack: [0x000000568eb00000,0x000000568ec00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x681069]
V  [jvm.dll+0x838cca]
V  [jvm.dll+0x83a78e]
V  [jvm.dll+0x83adf3]
V  [jvm.dll+0x24826f]
V  [jvm.dll+0x67dd99]
V  [jvm.dll+0x67286a]
V  [jvm.dll+0x30826b]
V  [jvm.dll+0x30f756]
V  [jvm.dll+0x35fae3]
V  [jvm.dll+0x35fd0f]
V  [jvm.dll+0x2df21c]
V  [jvm.dll+0x2e0174]
V  [jvm.dll+0x80a48b]
V  [jvm.dll+0x36d841]
V  [jvm.dll+0x7e8db5]
V  [jvm.dll+0x3f154f]
V  [jvm.dll+0x3f30c1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd275f8f18, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000017a7a923550 GCTaskThread "GC Thread#0" [stack: 0x000000568ec00000,0x000000568ed00000] [id=6052]
  0x0000017a7e8e8930 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000568ed00000,0x000000568ee00000] [id=6484]
  0x0000017a7a92c700 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000568ee00000,0x000000568ef00000] [id=8116]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd26daf957]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000017a7a870ba0] Heap_lock - owner thread: 0x0000017a7a8748d0

Heap address: 0x000000060b400000, size: 8012 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x000000060b400000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd2719a2b9]

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.006 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7c5f70000 - 0x00007ff7c5f7e000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\java.exe
0x00007ffde7920000 - 0x00007ffde7b85000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffde5dc0000 - 0x00007ffde5e89000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffde4c80000 - 0x00007ffde5068000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffde53c0000 - 0x00007ffde550b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffddb8d0000 - 0x00007ffddb8eb000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\VCRUNTIME140.dll
0x00007ffddd1d0000 - 0x00007ffddd1e7000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jli.dll
0x00007ffde5e90000 - 0x00007ffde605a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffde5510000 - 0x00007ffde5537000 	C:\WINDOWS\System32\win32u.dll
0x00007ffdc89b0000 - 0x00007ffdc8c4a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffde56c0000 - 0x00007ffde56eb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffde6520000 - 0x00007ffde65c9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffde5070000 - 0x00007ffde51a7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffde51b0000 - 0x00007ffde5253000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffde7180000 - 0x00007ffde71b0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffddd9b0000 - 0x00007ffddd9bc000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\vcruntime140_1.dll
0x00007ffdcb0d0000 - 0x00007ffdcb15d000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\msvcp140.dll
0x00007ffd26ac0000 - 0x00007ffd2772c000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\server\jvm.dll
0x00007ffde6200000 - 0x00007ffde62b3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffde7750000 - 0x00007ffde77f6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffde6da0000 - 0x00007ffde6eb5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffde6ec0000 - 0x00007ffde6f34000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffde3660000 - 0x00007ffde36be000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffdd7780000 - 0x00007ffdd77b5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffddd340000 - 0x00007ffddd34b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffde3640000 - 0x00007ffde3654000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffde3930000 - 0x00007ffde394b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffddd1c0000 - 0x00007ffddd1ca000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jimage.dll
0x00007ffde1a50000 - 0x00007ffde1c91000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffde5910000 - 0x00007ffde5c95000 	C:\WINDOWS\System32\combase.dll
0x00007ffde5700000 - 0x00007ffde57e1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffdc6f60000 - 0x00007ffdc6f99000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffde5320000 - 0x00007ffde53b9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffdcc100000 - 0x00007ffdcc125000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.14\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Users\<USER>\.jdks\corretto-17.0.14\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @C:\Users\<USER>\AndroidStudioProjects\NoteActiveNewApplication\app\build\20250629_9267749246359249571.compiler.options
java_class_path (initial): C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\2.0.21\79346ed53db48b18312a472602eb5c057070c54d\kotlin-compiler-embeddable-2.0.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\2.0.21\c9b044380ad41f89aa89aa896c2d32a8c0b2129d\kotlin-script-runtime-2.0.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\2.0.21\c9e933b23287de9b5a17e2116b4657bb91aea72c\kotlin-daemon-embeddable-2.0.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.6.4\2c997cd1c0ef33f3e751d3831929aeff1390cb30\kotlinx-coroutines-core-jvm-1.6.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 528482304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8401190912                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8401190912                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\openlogic-openjdk-jre-8u382-b05-windows-32
PATH=C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Windows;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Program Files\HP\OMEN-Broadcast\Common;C:\Program Files\Git\cmd;C:\Program Files\HP\HP One Agent;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
USERNAME=karan
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 5 days 0:12 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 116 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv
Processor Information for all 16 processors :
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801

Memory: 4k page, system-wide physical 32038M (6388M free)
TotalPageFile size 130342M (AvailPageFile size 15M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 576M, peak: 584M

vm_info: OpenJDK 64-Bit Server VM (17.0.14+7-LTS) for windows-amd64 JRE (17.0.14+7-LTS), built on Jan  7 2025 20:14:24 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
