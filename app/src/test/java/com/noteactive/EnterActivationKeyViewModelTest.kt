package com.noteactive

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.noteactive.app.auth.data.dto.ActivationKeyResponseDto
import com.noteactive.app.auth.data.impl.FakeAuthRepository
import com.noteactive.app.auth.domain.usecase.GetActivationKeyUseCase
import com.noteactive.app.auth.domain.usecase.GetAdvertisingIdUseCase
import com.noteactive.app.auth.domain.usecase.LocalActivationKeyUseCase
import com.noteactive.app.auth.presentation.viewmodel.EnterActivationKeyViewModel
import com.noteactive.app.common.GenericResponseModel
import com.noteactive.app.common.UiState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.Assert.*

import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test


@OptIn(ExperimentalCoroutinesApi::class)
class EnterActivationKeyViewModelTest {

    @get:Rule
    val instantExecutorRule = InstantTaskExecutorRule()

    private lateinit var viewModel: EnterActivationKeyViewModel
    private lateinit var getUserDataUseCase: GetActivationKeyUseCase
    private lateinit var getAdvertisingIdUseCase: GetAdvertisingIdUseCase
    private lateinit var localActivationKeyUseCase: LocalActivationKeyUseCase
    private val testDispatcher = StandardTestDispatcher()


    private val activationKeyResponseDto = ActivationKeyResponseDto(
        id = "155",
        firstName = "Hemant1",
        lastName = "Prajapati",
        emailAdd = "<EMAIL>",
        contactNo = "*********",
        companyName = "NoteActiv1",
        activitationKey = "hpdemo",
        address = "USA",
        androidId = "829c6810-2819-4f8f-8157-54d90618cd66",
        serverUrl = "https://demo.noteactive.com/index.php?route=services/",
        UserDate = "14-Oct-2024",
        facilities = "47,61,56,57,50,51,67,68,69,63,64,65,66,52,55,62,72,75,74,159,160,164,165,166,174,167,168,162,169,161,170,173,171,172,163,78,49,181,182",
        customerId = "hemantcustomerid-U",
        assetId = "HASSETID-U",
        support_server_id = "0",
        version = "",
        version_v2 = "",
        isUpdated = "0",
        dateUpdated = "0000-00-00 00:00:00",
        oktaEnable = "0",
        isAllCustomerData = "0"
    )
    private val mockResponse =
        GenericResponseModel(message = "", payload = activationKeyResponseDto, success = true)

    private lateinit var fakeAuthRepository: FakeAuthRepository

    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        fakeAuthRepository = FakeAuthRepository()

        getUserDataUseCase = GetActivationKeyUseCase(fakeAuthRepository)
        getAdvertisingIdUseCase = GetAdvertisingIdUseCase(fakeAuthRepository)
        localActivationKeyUseCase = LocalActivationKeyUseCase(fakeAuthRepository)

        viewModel = EnterActivationKeyViewModel(
            getUserDataUseCase,
            getAdvertisingIdUseCase,
            localActivationKeyUseCase
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `onTextChanged updates text correctly`() {
        val newText = "new activation key"
        viewModel.onTextChanged(newText)
        assertEquals(newText, viewModel.text.value)
    }

    @Test
    fun `isTextValid returns true when text is not empty and length is not greater than two charecter`() {
        viewModel.onTextChanged("valid key")
        assertTrue(viewModel.isTextValid())
    }

    @Test
    fun `isTextValid returns false when text length is less than two`() {
        viewModel.onTextChanged("a")
        assertFalse(viewModel.isTextValid())
    }

    @Test
    fun `isTextValid returns false when text is empty`() {
        viewModel.onTextChanged("")
        assertFalse(viewModel.isTextValid())
    }

    @Test
    fun `fetchUserData success sets userData and uiState to Success`() = runTest {

        fakeAuthRepository.activationResponse = mockResponse
        viewModel.fetchUserData()
        advanceUntilIdle() // Wait for coroutine to complete

        assertEquals(mockResponse, viewModel.userData.value)
        assertEquals(UiState.Success, viewModel.uiState.value)
    }

    @Test
    fun `fetchUserData network error sets uiState to Error`() = runTest {
        fakeAuthRepository.throwError = true

        viewModel.fetchUserData()
        advanceUntilIdle()

        assertEquals(UiState.Error("Network error: Network error"), viewModel.uiState.value)
    }

    @Test
    fun `fetchAdvertisingId success sets advertisingId`() = runTest {
        val expectedAdvertisingId = "test-ad-id"
        fakeAuthRepository.advertisingId =
            expectedAdvertisingId

        viewModel.fetchAdvertisingId()
        advanceUntilIdle()

        assertEquals(expectedAdvertisingId, viewModel.advertisingId.value)
    }

    @Test
    fun `fetchAdvertisingId failure does not update advertisingId`() = runTest {
        fakeAuthRepository.throwErrorInAdvertisingId = true
        viewModel.fetchAdvertisingId()
        advanceUntilIdle()

        assertNull(viewModel.advertisingId.value)
    }
}
