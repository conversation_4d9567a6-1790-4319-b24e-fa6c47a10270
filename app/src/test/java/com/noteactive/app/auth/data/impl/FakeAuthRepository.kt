package com.noteactive.app.auth.data.impl

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

import java.io.IOException

class FakeAuthRepository : AuthRepository {
    var activationResponse: GenericResponseModel<ActivationKeyResponseDto>? = null
    var throwError: Boolean = false
    var advertisingId: String = ""
    var throwErrorInAdvertisingId: Boolean = false

    override suspend fun postActivationKey(
        deviceUsername: String,
        deviceToken: String,
        latitude: Double,
        activationKey: String,
        phoneDeviceId: String,
        tag: String,
        deviceUniqueId: String,
        isAndroid: Int,
        longitude: Double
    ): GenericResponseModel<ActivationKeyResponseDto>? {
        if (throwError) throw IOException("Network error")
        return activationResponse
    }

    override suspend fun getAdvertisingId(): String {
        if (throwErrorInAdvertisingId)
        {
            throw IOException("Cannot Find Advertising ID")
        }
        return advertisingId
    }

    override fun getActivationUserData(): Flow<List<ActivationKeyResponse>> {

        return flowOf()
    }
}
