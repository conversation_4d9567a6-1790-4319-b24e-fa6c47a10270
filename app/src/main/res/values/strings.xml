<resources>
    <string name="app_name">NoteActiveApp</string>
    <string name="login_card_header_title">Login</string>
    <string name="login_screen_title">Login</string>
    <string name="login_screen_facility_header">Facility</string>
    <string name="login_screen_select_facility_hint">Select Facility</string>
    <string name="login_screen_select_pin_hint">Enter Pin</string>
    <string name="login_screen_pin_header">Pin</string>
    <string name="enter_activation_key_hint">Enter Key</string>
    <string name="enter_activation_key_button_title">Continue</string>
    <string name="select_date_title">Select Date</string>
    <string name="select_time_title">Select Time</string>
    <string name="time_picker_close_button_title">Close</string>
    <string name="time_picker_update_button_title">Update</string>
    <string name="close">Close</string>
    <string name="activate">Activate</string>
    <string name="activation_details_screen_title">Activation Details</string>
    <string name="test_dialog_title">Want to mark the form as final?</string>
    <string name="test_dialog_subtitle">After marking form as final you cannot edit the form anymore.</string>
    <string name="login_select_customer">Select Customer</string>
    <string name="submit">Submit</string>
    <string name="cancel">Cancel</string>
    <string name="verify">Verify</string>
    <string name="login_error_select_customer_first">Please select customer</string>
    <string name="select_active_notes_screen_title">Select Active Notes</string>
    <string name="save">Save</string>
    <string name="update_date_time_toolbar_title"><![CDATA[Update date & time]]></string>
    <string name="select_time_textfield_title">Select time</string>
    <string name="select_date_textfield_title">Select date</string>
    <string name="comment_textfield_title">Comment</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="name">Name</string>
    <string name="gender">Gender</string>
    <string name="dob">DOB</string>
    <string name="id">Id</string>
    <string name="booking_id">Booking Id</string>
    <string name="ccn">CCN</string>
    <string name="demo">Demo</string>
    <string name="location">Location</string>
    <string name="add">Add</string>
    <string name="notes">Notes</string>
    <string name="activation_key_invalid_error">Please enter valid key</string>
</resources>