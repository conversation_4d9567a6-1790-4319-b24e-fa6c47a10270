<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M8,0L32,0A8,8 0,0 1,40 8L40,32A8,8 0,0 1,32 40L8,40A8,8 0,0 1,0 32L0,8A8,8 0,0 1,8 0z"
      android:fillColor="#F0F2F4"/>
  <path
      android:pathData="M22,17H22.01"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#889AAA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,23.998L15,19.998C15.456,19.559 15.973,19.328 16.5,19.328C17.027,19.328 17.544,19.559 18,19.998L23,24.998"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#889AAA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M21,22.998L22,21.998C22.456,21.559 22.973,21.328 23.5,21.328C24.027,21.328 24.544,21.559 25,21.998L27,23.998"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#889AAA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M22,13H13C11.895,13 11,13.895 11,15V27C11,28.105 11.895,29 13,29H25C26.105,29 27,28.105 27,27V18"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#889AAA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M25,13L29,13"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#889AAA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M27,11L27,15"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#889AAA"
      android:strokeLineCap="round"/>
</vector>
