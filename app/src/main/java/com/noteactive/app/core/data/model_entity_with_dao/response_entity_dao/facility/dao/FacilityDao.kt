package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao

import androidx.room.Dao
import androidx.room.Query
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.Facility
import com.noteactive.app.core.util.base.BaseDao

@Dao
interface FacilityDao : BaseDao<Facility> {

    @Query("DELETE FROM facility")
    suspend fun deleteAll()

    @Query("SELECT facilitiesId,facility FROM facility where customerKey =:customerKey")
    fun getAllFacility(customerKey: String): List<Facility>

}