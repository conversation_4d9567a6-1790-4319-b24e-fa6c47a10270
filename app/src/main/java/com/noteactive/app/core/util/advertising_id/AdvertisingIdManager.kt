package com.noteactive.app.core.util.advertising_id

import android.content.Context
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object AdvertisingIdManager {

    private var advertisingId: String? = null

    suspend fun getAdvertisingId(context: Context): String {
        advertisingId?.let { return it }
        return withContext(Dispatchers.IO) {
            try {
                val info = AdvertisingIdClient.getAdvertisingIdInfo(context)
                advertisingId = info.id
                advertisingId ?: ""
            } catch (e: Exception) {
                e.printStackTrace()
                ""
            }
        }
    }
}

