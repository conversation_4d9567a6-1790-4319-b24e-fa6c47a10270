package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.mapper

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.CustomerInfo
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.CustomerItemUi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

fun CustomerInfo.toDomain(): CustomerItemUi = CustomerItemUi(
    customerName = companyName,
    customerKey = customerKey,
    isSelected = false
)

fun List<CustomerInfo>.toDomainList(): List<CustomerItemUi> {
    return map { it.toDomain() }
}
fun Flow<List<CustomerInfo>>.toDomainList(): Flow<List<CustomerItemUi>> {
    return map { customerList ->
        customerList.map { it.toDomain() }
    }
}