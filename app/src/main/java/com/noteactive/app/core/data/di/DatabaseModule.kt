package com.noteactive.app.core.data.di

import android.content.Context
import androidx.room.Room
import com.noteactive.app.core.data.db.AppDatabase
import com.noteactive.app.features.login.data.model.dao.activation_key_dao.ActivationKeyResponseDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.ApiUrlDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.CustomerInfoDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.FacilityDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location.LocationDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.dao.ApiSyncDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.colors.ColorDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.gate_ways.GatewaysDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role.UserRoleDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.sensor.SensorDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            AppDatabase::class.java,
            "app_database"
        ).build()
    }

    @Provides
    fun provideActivationDaoDao(db: AppDatabase): ActivationKeyResponseDao {
        return db.activationKeyResponseDao()
    }

    @Provides
    fun provideFacilityDao(db: AppDatabase): FacilityDao {
        return db.facilityDao()
    }

    @Provides
    fun provideFacilityApiResponseDao(db: AppDatabase) = db.customerInfoDao()

    @Provides
    fun provideApiUrl(db: AppDatabase) = db.apiUrlDao()

    @Provides
    fun provideUsers(db: AppDatabase): UserDao = db.userDao()

    @Provides
    fun provideColor(db: AppDatabase): ColorDao = db.colorDao()

    @Provides
    fun provideUserRoleDao(db: AppDatabase): UserRoleDao = db.userRoleDao()

    @Provides
    fun provideApiSyncDao(db: AppDatabase) = db.apiSyncDao()

    @Provides
    fun provideGatewaysDao(db: AppDatabase): GatewaysDao = db.gatewaysDao()

    @Provides
    fun locationDao(db: AppDatabase) = db.locationDao()

    @Provides
    fun sensorDao(db: AppDatabase) = db.sensorDao()
}
