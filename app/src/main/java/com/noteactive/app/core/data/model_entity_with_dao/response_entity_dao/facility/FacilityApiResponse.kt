package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility

import androidx.room.Entity
import androidx.room.PrimaryKey

data class FacilityApiResponse(
    val apiurls: List<ApiUrl>?,
    val facilities: List<Facility>?,
    val companyName: String,
    val customerKey: String,
    val settingData: SettingData?, // Changed from String? to proper model
    val activecustomerId: Int?,
    val clientInfoNotes: ClientInfoNotes? // Changed from List<String> to proper model
)

@Entity(tableName = "customer_info")
data class CustomerInfo(
    @PrimaryKey
    val customerKey: String,
    val companyName: String,
    val settingData: SettingData?, // Save JSON string or map it to SettingData
    val activecustomerId: Int?,
    val clientInfoNotes: ClientInfoNotes?, // Save JSON string
    val isSelected: Boolean = false
)