package com.noteactive.app.core.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.IntSize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

fun checkInternetConnection(context: Context): Boolean {
    val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    val networkCapabilities = connectivityManager.activeNetwork ?: return false
    val activeNetwork =
        connectivityManager.getNetworkCapabilities(networkCapabilities) ?: return false
    return when {
        activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
        activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
        activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
        else -> false
    }
}





fun formatDate(millis: Long): String {
    val formatter = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
    return formatter.format(Date(millis))
}


fun Offset.clampToSize(size: IntSize): Offset {
    return Offset(
        x = x.coerceIn(0f, size.width.toFloat()),
        y = y.coerceIn(0f, size.height.toFloat())
    )
}

@Composable
fun rememberGenericFilePicker(
    mimeType: String = "*/*",
    multiple: Boolean = false,
    onFilesPicked: (List<Uri>) -> Unit
): () -> Unit {
    // Single file picker
    val singlePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { onFilesPicked(listOf(it)) }
    }

    // Multiple file picker
    val multiplePicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents()
    ) { uris: List<Uri> ->
        onFilesPicked(uris)
    }

    // Return appropriate lambda
    return {
        if (multiple) {
            multiplePicker.launch(mimeType)
        } else {
            singlePicker.launch(mimeType)
        }
    }
}
