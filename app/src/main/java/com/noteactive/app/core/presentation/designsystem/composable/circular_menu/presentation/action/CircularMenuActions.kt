package com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.action

sealed class CircularMenuViewModelAction {
    object OnSOSClicked : CircularMenuViewModelAction()
    object OnCameraClicked : CircularMenuViewModelAction()
    object OnHomeClicked : CircularMenuViewModelAction()
    object OnBarcodeClicked : CircularMenuViewModelAction()
    object OnIcon1Clicked : CircularMenuViewModelAction()
    object OnCloseClicked : CircularMenuViewModelAction()
}

sealed class CircularMenuNavigation {
    object NavigateToSOS : CircularMenuNavigation()
    object NavigateToCamera : CircularMenuNavigation()
    object NavigateToHome : CircularMenuNavigation()
    object NavigateToBarcode : CircularMenuNavigation()
    object NavigateToIcon1 : CircularMenuNavigation()
    object NavigateBack : CircularMenuNavigation()
}
