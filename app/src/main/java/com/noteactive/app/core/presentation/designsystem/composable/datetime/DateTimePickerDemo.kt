package com.noteactive.app.core.presentation.designsystem.composable.datetime

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.text.SimpleDateFormat
import java.util.*

/**
 * Demo Screen for Date and Time Picker Components
 * 
 * This screen demonstrates how to use the DateTimePickerDialog,
 * CircularTimePicker, and CalendarDatePicker components.
 * 
 * Usage Examples:
 * 1. Full DateTime Picker Dialog with start/end selection
 * 2. Simple Date Picker Dialog
 * 3. Standalone Time Picker
 * 4. Standalone Calendar Picker
 */

@Preview
@RequiresApi(Build.VERSION_CODES.O)
@Composable
fun DateTimePickerDemoScreen() {
    var showDateTimePicker by remember { mutableStateOf(false) }
    var showSimpleDatePicker by remember { mutableStateOf(false) }
    var showTimePickerOnly by remember { mutableStateOf(false) }
    var showCalendarOnly by remember { mutableStateOf(false) }
    
    var dateTimeSelection by remember { mutableStateOf(DateTimeSelection()) }
    var selectedDate by remember { mutableStateOf(Date()) }
    var selectedTime by remember { mutableStateOf(TimeData.now()) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Date & Time Picker Demo",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
        
        Divider()
        
        // Full DateTime Picker Demo
        DateTimePickerSection(
            title = "Full DateTime Picker",
            description = "Complete date and time picker with start/end selection",
            currentSelection = dateTimeSelection,
            onShowPicker = { showDateTimePicker = true }
        )
        
        Divider()
        
        // Simple Date Picker Demo
        SimpleDatePickerSection(
            title = "Simple Date Picker",
            description = "Calendar-only date selection",
            selectedDate = selectedDate,
            onShowPicker = { showSimpleDatePicker = true }
        )
        
        Divider()
        
        // Time Picker Only Demo
        TimePickerSection(
            title = "Time Picker Only",
            description = "Circular time picker component",
            selectedTime = selectedTime,
            onShowPicker = { showTimePickerOnly = true }
        )
        
        Divider()
        
        // Calendar Only Demo
        CalendarPickerSection(
            title = "Calendar Only",
            description = "Calendar date picker component",
            selectedDate = selectedDate,
            onShowPicker = { showCalendarOnly = true }
        )
    }
    
    // Dialog instances
    DateTimePickerDialog(
        isVisible = showDateTimePicker,
        initialSelection = dateTimeSelection,
        onDismiss = { showDateTimePicker = false },
        onConfirm = { selection ->
            dateTimeSelection = selection
            showDateTimePicker = false
        }
    )
    
    SimpleDatePickerDialog(
        isVisible = showSimpleDatePicker,
        selectedDate = selectedDate,
        onDateSelected = { date ->
            selectedDate = date
        },
        onDismiss = { showSimpleDatePicker = false }
    )
    
    if (showTimePickerOnly) {
        TimePickerOnlyDialog(
            selectedTime = selectedTime,
            onTimeSelected = { time ->
                selectedTime = time
                showTimePickerOnly = false
            },
            onDismiss = { showTimePickerOnly = false }
        )
    }
    
    if (showCalendarOnly) {
        CalendarOnlyDialog(
            selectedDate = selectedDate,
            onDateSelected = { date ->
                selectedDate = date
                showCalendarOnly = false
            },
            onDismiss = { showCalendarOnly = false }
        )
    }
}
@RequiresApi(Build.VERSION_CODES.O)
@Composable
private fun DateTimePickerSection(
    title: String,
    description: String,
    currentSelection: DateTimeSelection,
    onShowPicker: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
            
            Text(
                text = description,
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Display current selection
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Start:",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "${SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(currentSelection.startDate)} ${String.format("%02d:%02d", currentSelection.startTime.hour, currentSelection.startTime.minute)}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

                Column {
                    Text(
                        text = "End:",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "${SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(currentSelection.endDate)} ${String.format("%02d:%02d", currentSelection.endTime.hour, currentSelection.endTime.minute)}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = onShowPicker,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF9800))
            ) {
                Text("Select Date & Time", color = Color.White)
            }
        }
    }
}
@RequiresApi(Build.VERSION_CODES.O)
@Composable
private fun SimpleDatePickerSection(
    title: String,
    description: String,
    selectedDate: Date,
    onShowPicker: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
            
            Text(
                text = description,
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "Selected: ${SimpleDateFormat("EEEE, MMM dd, yyyy", Locale.getDefault()).format(selectedDate)}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = onShowPicker,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
            ) {
                Text("Select Date", color = Color.White)
            }
        }
    }
}
@RequiresApi(Build.VERSION_CODES.O)
@Composable
private fun TimePickerSection(
    title: String,
    description: String,
    selectedTime: TimeData,
    onShowPicker: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
            
            Text(
                text = description,
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "Selected: ${String.format("%02d:%02d", selectedTime.hour, selectedTime.minute)}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = onShowPicker,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
            ) {
                Text("Select Time", color = Color.White)
            }
        }
    }
}

@RequiresApi(Build.VERSION_CODES.O)
@Composable
private fun CalendarPickerSection(
    title: String,
    description: String,
    selectedDate: Date,
    onShowPicker: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
            
            Text(
                text = description,
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "Selected: ${SimpleDateFormat("EEEE, MMM dd, yyyy", Locale.getDefault()).format(selectedDate)}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = onShowPicker,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF9C27B0))
            ) {
                Text("Select Date", color = Color.White)
            }
        }
    }
}

@Composable
private fun TimePickerOnlyDialog(
    selectedTime: TimeData,
    onTimeSelected: (TimeData) -> Unit,
    onDismiss: () -> Unit
) {
    androidx.compose.ui.window.Dialog(
        onDismissRequest = onDismiss
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Time",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                CircularTimePicker(
                    selectedTime = selectedTime,
                    onTimeSelected = onTimeSelected,
                    modifier = Modifier.padding(16.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel", color = Color.Gray)
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
                    ) {
                        Text("OK", color = Color.White)
                    }
                }
            }
        }
    }
}

@Composable
private fun CalendarOnlyDialog(
    selectedDate: Date,
    onDateSelected: (Date) -> Unit,
    onDismiss: () -> Unit
) {
    androidx.compose.ui.window.Dialog(
        onDismissRequest = onDismiss
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Select Date",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                CalendarDatePicker(
                    selectedDate = selectedDate,
                    onDateSelected = onDateSelected
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel", color = Color.Gray)
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF9C27B0))
                    ) {
                        Text("OK", color = Color.White)
                    }
                }
            }
        }
    }
}
