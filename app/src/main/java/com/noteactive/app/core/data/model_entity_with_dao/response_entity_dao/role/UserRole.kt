package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_role")
data class UserRoleEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0L,
    val userGroupId: Int,
    val name: String,
    val customerKey: String,
    val enableFormOpen: Int? = null,
    val enableMarkFinal: Int? = null
)
