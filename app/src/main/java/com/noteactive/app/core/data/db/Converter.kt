package com.noteactive.app.core.data.db

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.ClientInfoNotes
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.SettingData

class Converters {

    @TypeConverter
    fun fromStringList(value: List<String>?): String? {
        return value?.joinToString(separator = ",")
    }

    @TypeConverter
    fun toStringList(value: String?): List<String>? {
        return value?.split(",")?.map { it.trim() }
    }

    @TypeConverter
    fun fromJson(value: String): SettingData {
        return Gson().fromJson(value, SettingData::class.java)
    }

    @TypeConverter
    fun toJson(settingData: SettingData): String {
        return Gson().toJson(settingData)
    }

    @TypeConverter
    fun fromJsonToClientInfoNotes(value: String): ClientInfoNotes {
        return Gson().fromJson(value, ClientInfoNotes::class.java)
    }

    @TypeConverter
    fun toJsonFromClientInfoNotes(settingData: ClientInfoNotes): String {
        return Gson().toJson(settingData)
    }

}
