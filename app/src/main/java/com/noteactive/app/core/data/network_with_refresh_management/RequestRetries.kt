package com.noteactive.app.core.data.network_with_refresh_management

import com.noteactive.app.core.util.preferences.AppPreferences
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.Request
import javax.inject.Inject

class RequestRetries @Inject constructor(
    private val tokenManager: AppPreferences
) {
    private val lock = Mutex()
    private var isRefreshing = false
    private val pendingRequests = mutableListOf<CompletableDeferred<Request?>>()

    suspend fun retryAfterRefresh(request: Request, refresh: suspend () -> Boolean): Request? {
        val deferred = CompletableDeferred<Request?>()

        lock.withLock {
            pendingRequests.add(deferred)
            if (!isRefreshing) {
                isRefreshing = true

                val refreshed = refresh()
                if (refreshed) {
                    val newToken = tokenManager.accessToken
                    pendingRequests.forEach {
                        it.complete(
                            request.newBuilder()
                                .header("Authorization", "Bearer $newToken")
                                .build()
                        )
                    }
                } else {
                    tokenManager.clearAccessToken()
                    pendingRequests.forEach { it.complete(null) }
                }
                pendingRequests.clear()
                isRefreshing = false
            }
        }

        return deferred.await()
    }
}
