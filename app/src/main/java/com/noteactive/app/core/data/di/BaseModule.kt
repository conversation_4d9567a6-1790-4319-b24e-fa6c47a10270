package com.noteactive.app.core.data.di

import android.content.Context
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.noteactive.app.core.data.repositoryImpl.BaseRepositoryImpl
import com.noteactive.app.core.domain.repository.BaseRepository
import com.noteactive.app.core.util.preferences.AppPreferences
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

/*Provide Location And Advertisement Id*/

@Module
@InstallIn(SingletonComponent::class)
class BaseModule {

    @Provides
    fun provideFusedLocationProviderClient(@ApplicationContext context: Context): FusedLocationProviderClient =
        LocationServices.getFusedLocationProviderClient(context)

    @Provides
    fun provideLocationRepository(
        @ApplicationContext context: Context,
        appPreferences: AppPreferences,
        fusedLocationProviderClient: FusedLocationProviderClient
    ): BaseRepository {
        return BaseRepositoryImpl(context, appPreferences, fusedLocationProviderClient)
    }
}
