package com.noteactive.app.core.util.network

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import retrofit2.Response

abstract class NetworkBoundResourceWithLocal<ResultType, RequestType> {

    fun asFlow(): Flow<Resource<ResultType>> = flow {
        emit(Resource.Loading())
        val dbSource = loadFromDb().firstOrNull()
        if (shouldFetch(dbSource)) {
            emit(Resource.Loading(dbSource))
            try {
                val response = createCall()

                if (response.isSuccessful) {
                    response.body()?.let {
                        if (shouldDeleteTable()) {
                            deleteFromDb()
                        }
                        saveCallResult(it)
                        emitAll(loadFromDb().map { Resource.Success(it) })

                    } ?: emit(Resource.Error("Empty body", dbSource))
                } else {
                    emit(Resource.Error(response.message(), dbSource))
                }
            } catch (e: Exception) {
                emit(Resource.Error(e.localizedMessage ?: "Unknown", dbSource))
            }
        } else {
            emitAll(loadFromDb().map { Resource.Success(it) })
        }
    }

    protected abstract fun loadFromDb(): Flow<ResultType>
    protected abstract suspend fun deleteFromDb()
    protected abstract suspend fun createCall(): Response<RequestType>
    protected abstract suspend fun saveCallResult(item: RequestType)
    protected open fun shouldFetch(data: ResultType?): Boolean = true
    protected open suspend fun shouldDeleteTable(): Boolean = false

}

abstract class NetworkBoundResource<ResultType> {

    fun asFlow(): Flow<Resource<ResultType>> = flow {
        emit(Resource.Loading())
        try {
            val response = createCall()

            if (response.isSuccessful) {
                response.body()?.let {
                    saveToDb(it)
                    emit(Resource.Success(it))

                } ?: emit(Resource.Error("Empty body"))
            } else {
                emit(Resource.Error(response.message()))
            }
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "Unknown"))
        }
    }

    protected abstract suspend fun createCall(): Response<ResultType>
    protected abstract suspend fun saveToDb(result : ResultType)

}



suspend fun <T> safeApiRequest(
    apiCall: suspend () -> Response<T>,
    onSuccess: (T) -> Unit,
    onFailure: () -> Unit
) {
    try {
        val response = apiCall()
        if (response.isSuccessful) {
            response.body()?.let {
                onSuccess(it)
            } ?: onFailure()
        } else {
            onFailure()
        }
    } catch (e: Exception) {
        e.printStackTrace()
        onFailure()
    }
}

