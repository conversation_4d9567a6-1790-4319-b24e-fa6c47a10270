package com.noteactive.app.core.data.repositoryImpl

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntity
import com.noteactive.app.core.domain.repository.UserLocalRepository
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserDao
import javax.inject.Inject

class UserLocalRepositoryImpl @Inject constructor(private val userDao: UserDao) : UserLocalRepository {
    override suspend fun checkCorrectUserWithPin(pin: String): UserEntity? = userDao.getUserWithPin(pin)
}