package com.noteactive.app.core.presentation.designsystem.composable.chip

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp

@Composable
fun AppPrimaryChip(
    modifier: Modifier = Modifier
        .padding(2.dp)
        .defaultMinSize(minHeight = 26.dp),
    title: String,
    leadingIcon: Int?,
    leadingIconModifier: Modifier = Modifier.size(20.dp),
    trailingIconModifier: Modifier = Modifier.size(20.dp),
    trailingIcon: Int?,
    trailingIconClicked: () -> Unit,
    onClick: (() -> Unit?)? = null
) {
    AssistChip(
        onClick = { onClick?.invoke() }, label = {
            Text(
                text = title, style = MaterialTheme.typography.bodySmall.copy(
                    color = Color.Black
                )
            )
        }, leadingIcon = {
            leadingIcon?.let {
                Image(
                    painter = painterResource(it),
                    contentDescription = null,
                    modifier = leadingIconModifier
                )
            }

        }, trailingIcon = {
            trailingIcon?.let {
                Icon(
                    painter = painterResource(it),
                    contentDescription = "Remove",
                    modifier = trailingIconModifier.clickable {
                        trailingIconClicked.invoke()
                    })
            }

        }, modifier = modifier, // tighter outer padding
        shape = RoundedCornerShape(6.dp), colors = AssistChipDefaults.assistChipColors(
            containerColor = Color.White,
            trailingIconContentColor = MaterialTheme.colorScheme.outline
        )
    )
}