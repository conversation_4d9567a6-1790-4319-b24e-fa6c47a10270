package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.mapper

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.Facility
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi

fun Facility.toDomain(): FacilityItemUi = FacilityItemUi(
    facility = facility,
    facilitiesId = facilitiesId,
)

fun List<Facility>.toDomainList(): List<FacilityItemUi> {
    return map { it.toDomain() }
}