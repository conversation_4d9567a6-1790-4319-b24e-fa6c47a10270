package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "facility")
data class Facility(
    @PrimaryKey
    val facilitiesId: Int,
    val salt: String?,
    val status: Int?,
    val facility: String = "",
    val password: String?,
    val timezoneId: Int?,
    val customerKey: String?,
    val enableEscorted: Int?,
    val noDistribution: Int?,
    val allowQuickSave: Int?,
    val sharenotesPrint: Int?,
    val tagStatus: Int?,
    val sharenotesModify: Int?,
    val shareNotes: Int?,
    val taskStatus: Int?,
    val isMasterFacility: Int?,
    val rulesStatus: Int?,
    val sharenotesAssemble: Int?,
    val displayCamera: Int?,
    val inventoryAllow: Int?,
    val noteformStatus: Int?,
    val sharepinStatus: Int?,
    val taskformStatus: Int?,
    val isEnableAddNotesBy: Int?,
    val tagsCustomlistId: Int?,
    val multipleActivenote: Int?,
    val bedcheckCustomlistId: Int?,
    val rolecallCustomlistId: String?,
    val sendEmailShareNotes: Int?,
    val displayDashboard: Int?,

    // ✅ New Lists (Normalized or JSON)
    val formPrintLayout: List<String>?,
    val taskFacilitiesIds: List<String>?,
    val notesFacilitiesIds: List<String>?,
    val clientFacilitiesIds: List<String>?,

    // ✅ Facility Setting Embedded
    val movementType: String?,
    val rollCallScan: String?
) : Parcelable

data class FacilitySetting(
    val movementType: String?,
    val rollCallScan: String?
)

data class ClientInfoNotes(
    val showCase: String?,
    val showTask: String?,
    val showFormTag: String?,
    val clientInfoNotes: String?,
    val showClientImage: String?,
    val tagPopupOptions: String?,
    val clientViewOptions: String?,
    val clientDetailsViewOptions: String?
)


data class SettingData(
    val ccn: String?,
    val dob: String?,
    val ssn: String?,
    val gender: String?,
    val inmateName: String?,
    val outName: String?,
    val firstname: String?,
    val lastname: String?,
    val middlename: String?,
    val facility: String?,
    val location: String?,
    val dateFormat: String?,
    val timeFormat: String?,
    val redColor: String?,
    val greenColor: String?,
    val orangeColor: String?,
    val permanentcolorCode: String?,
    val temporarycolorCode: String?,

    val rulesStartTime: String?,
    val rulesEndTime: String?,
    val rulesOperation: String?,

    val searchFilter: List<String>?,
    val codeValues: List<String>?,
    val caseTypeValues: List<String>?,
    val assignInventoryOptions: List<String>?,
    val generateUserRole: List<String>?,

    val enableMqtt: String?,
    val displayDashboard: String?,
    val enableRevoke: String?,
    val enableRosterUpdate: String?,

    val rollCallNfcCommentRequired: String?,
    val outTheSellReminder: String?,

// Add other fields as needed from JSON
)
