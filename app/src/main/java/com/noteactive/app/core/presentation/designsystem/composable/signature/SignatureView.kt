package com.noteactive.app.core.presentation.designsystem.composable.signature

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.util.clampToSize

@Composable
fun SignatureView(
    modifier: Modifier = Modifier,
    signatureController: SignatureController = rememberSignatureController()
) {
    var canvasSize by remember { mutableStateOf(IntSize.Zero) }

    Box(
        modifier = modifier
            .onSizeChanged {
                canvasSize = it
                signatureController.updateCanvasSize(it)
            }
            .pointerInput(canvasSize) {
                detectDragGestures(
                    onDragStart = { offset ->
                        signatureController.addPoint(offset.clampToSize(canvasSize))
                    },
                    onDrag = { change, _ ->
                        signatureController.addPoint(change.position.clampToSize(canvasSize))
                    },
                    onDragEnd = {
                        signatureController.endCurrentPath()
                    }
                )
            }
    ) {
        Canvas(modifier = Modifier.matchParentSize()) {
            val stroke = Stroke(width = 6f)

            signatureController.paths.forEach {
                drawPath(it, color = Color.Black, style = stroke)
            }

            drawPath(signatureController.currentPath, color = Color.Black, style = stroke)
        }
    }
}


@PreviewScreenSizes
@Preview
@Composable
private fun SignatureViewPreview() {
    MaterialTheme {
        SignatureView(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp)
                .border(1.dp, MaterialTheme.colorScheme.outline)
                .padding(horizontal = 16.dp),
            signatureController = rememberSignatureController()
        )
    }
}

