package com.noteactive.app.core.presentation.designsystem.theme

// Color.kt
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

val LightColorPalette = lightColorScheme(
    primary = Color(0xFFFF6900),
    background = Color(0xFFF0F2F4),
    surface = Color.White,
    outline = Color(0xFFC3CCD5),
    outlineVariant = Color(0xFF889AAA),
    error = Color(0xFFFF0000),
    onSurfaceVariant = Color(0xFF000000),
    onError = Color(0xFF000000),
    onSurface = Color(0xFFC3CCD5),
)

val DarkColorPalette = darkColorScheme(
    primary = Color(0xFFFF6900),
    background = Color(0xFFF0F2F4),
    surface = Color.White,

    )


// Primary Colors
val Primary = Color(0xFF1D4ED8)
val OnPrimary = Color(0xFFFFFFFF)
val PrimaryContainer = Color(0xFFBFDBFE)
val OnPrimaryContainer = Color(0xFF00174C)

// Secondary Colors
val Secondary = Color(0xFFFB923C)
val OnSecondary = Color(0xFF000000)
val SecondaryContainer = Color(0xFFFFEDD5)
val OnSecondaryContainer = Color(0xFF4A2000)

// Neutral Colors
val Background = Color(0xFFFFFFFF)
val OnBackground = Color(0xFF121212)
val Surface = Color(0xFFFFFFFF)
val OnSurface = Color(0xFF121212)
val SurfaceVariant = Color(0xFFECECEC)
val OnSurfaceVariant = Color(0xFF434343)

// Error Colors (Optional)
val Error = Color(0xFFB00020)
val OnError = Color(0xFFFFFFFF)

// Blue Shades
val Blue50 = Color(0xFFEFF6FF)
val Blue100 = Color(0xFFDBEAFE)
val Blue200 = Color(0xFFBFDBFE)
val Blue300 = Color(0xFF93C5FD)
val Blue400 = Color(0xFF60A5FA)
val Blue500 = Color(0xFF3B82F6)
val Blue600 = Color(0xFF2563EB)
val Blue700 = Color(0xFF1D4ED8)
val Blue800 = Color(0xFF1E40AF)
val Blue900 = Color(0xFF1E3A8A)

// Red Shades
val Red50 = Color(0xFFFEF2F2)
val Red100 = Color(0xFFFEE2E2)
val Red200 = Color(0xFFFECACA)
val Red300 = Color(0xFFFCA5A5)
val Red400 = Color(0xFFF87171)
val Red500 = Color(0xFFEF4444)
val Red600 = Color(0xFFDC2626)
val Red700 = Color(0xFFB91C1C)
val Red800 = Color(0xFF991B1B)
val Red900 = Color(0xFF7F1D1D)

// Yellow Shades
val Yellow50 = Color(0xFFFEFCE8)
val Yellow100 = Color(0xFFFEF9C3)
val Yellow200 = Color(0xFFFEF08A)
val Yellow300 = Color(0xFFFDE047)
val Yellow400 = Color(0xFFFACC15)
val Yellow500 = Color(0xFFEAB308)
val Yellow600 = Color(0xFFCA8A04)
val Yellow700 = Color(0xFFA16207)
val Yellow800 = Color(0xFF854D0E)
val Yellow900 = Color(0xFF713F12)

// Green Shades
val Green50 = Color(0xFFF0FDF4)
val Green100 = Color(0xFFDCFCE7)
val Green200 = Color(0xFFBBF7D0)
val Green300 = Color(0xFF86EFAC)
val Green400 = Color(0xFF4ADE80)
val Green500 = Color(0xFF22C55E)
val Green600 = Color(0xFF16A34A)
val Green700 = Color(0xFF15803D)
val Green800 = Color(0xFF166534)
val Green900 = Color(0xFF14532D)


val LightColors = lightColorScheme(
    primary = Primary,
    onPrimary = OnPrimary,
    primaryContainer = PrimaryContainer,
    onPrimaryContainer = OnPrimaryContainer,
    secondary = Secondary,
    onSecondary = OnSecondary,
    background = Background,
    onBackground = OnBackground,
    surface = Surface,
    onSurface = OnSurface,
    surfaceVariant = SurfaceVariant,
    onSurfaceVariant = OnSurfaceVariant
)

val DarkColors = darkColorScheme(
    primary = Primary,
    onPrimary = OnPrimary,
    primaryContainer = PrimaryContainer,
    onPrimaryContainer = OnPrimaryContainer,
    secondary = Secondary,
    onSecondary = OnSecondary,
    background = Color(0xFF121212),
    onBackground = Color(0xFFFFFFFF),
    surface = Color(0xFF121212),
    onSurface = Color(0xFFFFFFFF),
    surfaceVariant = Color(0xFF434343),
    onSurfaceVariant = Color(0xFFECECEC)
)


val DisabledTextField = Color(0xFFD0D5DD)
val NoteRowDivider = Color(0xFFCAD1EB)
val NotesScreenHeaderDevider = Color(0xFFE6E6E6)
val NotesScreenHeaderTextBlue = Color(0xFF0075FF)
val NotesListHeaderBackground = Color(0xFFD9D9D9)
val NotesListVerticalDivider = Color(0XFFFFBEBE)
val NotesListChipBorderColor = Color(0XFFC3CCD5)
val NotesListChipContainerColor = Color(0XFFF0F2F4)
val NotesListChipTextColor = Color(0XFF3F4D5A)
val FocusTextTitleColor = Color(0XFF151A1E)
val FocusTextSubTitleColor = Color(0XFF556777)
val ActiveNoteBoxBorder = Color(0xFFDDDDDD)
val ActiveNoteHeaderBackgroundColor = Color(0xFFF7F7F7)
val SelectedBackgroundColor = Color(0xFFFFFFF3EB)
val OffenderItemBackgroundColor = Color(0xFFFEEFE7)