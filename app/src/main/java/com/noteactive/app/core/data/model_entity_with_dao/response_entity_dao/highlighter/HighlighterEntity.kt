package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.highlighter

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "highlighters")
data class HighlighterEntity(
    @PrimaryKey
    val highlighterId: Int,
    val highlighterName: String,
    val highlighterValue: String,
    val highlighterIcon: String,
    val status: Int,
    val customerKey: Int
)
