package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "api_url")
data class ApiUrl(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0L,
    val status: Int?,
    val keyname: String?,
    val uniqueId: String?,
    val apiUrlId: Int?,
    val dateAdded: String?,
    val apiFullUrl: String?,
    val customerKey: String?
)
