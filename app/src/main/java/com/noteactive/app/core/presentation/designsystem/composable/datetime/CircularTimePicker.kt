package com.noteactive.app.core.presentation.designsystem.composable.datetime

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.util.*
import kotlin.math.*

/**
 * Circular Time Picker Component
 *
 * A circular time picker that matches the design in the image.
 * Features:
 * - Digital time display at the top
 * - Circular clock interface with draggable selection
 * - Hour and minute selection modes
 * - Orange accent color for selection
 * - Compatible with API 24+
 */

data class TimeData(
    val hour: Int,
    val minute: Int
) {
    companion object {
        fun fromDate(date: Date): TimeData {
            val calendar = Calendar.getInstance()
            calendar.time = date
            return TimeData(
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE)
            )
        }

        fun now(): TimeData {
            return fromDate(Date())
        }
    }

    fun toDate(): Date {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, hour)
        calendar.set(Calendar.MINUTE, minute)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.time
    }

    fun withHour(newHour: Int): TimeData = copy(hour = newHour)
    fun withMinute(newMinute: Int): TimeData = copy(minute = newMinute)
}

@Composable
fun CircularTimePicker(
    selectedTime: TimeData,
    onTimeSelected: (TimeData) -> Unit,
    modifier: Modifier = Modifier
) {
    var isSelectingHour by remember { mutableStateOf(true) }

    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Digital time display
        DigitalTimeDisplay(
            time = selectedTime,
            isSelectingHour = isSelectingHour,
            onHourClick = { isSelectingHour = true },
            onMinuteClick = { isSelectingHour = false }
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Circular clock
        CircularClock(
            selectedTime = selectedTime,
            isSelectingHour = isSelectingHour,
            onTimeChanged = onTimeSelected,
            modifier = Modifier.size(280.dp)
        )
    }
}

@Composable
private fun DigitalTimeDisplay(
    time: TimeData,
    isSelectingHour: Boolean,
    onHourClick: () -> Unit,
    onMinuteClick: () -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // Hour
        Text(
            text = String.format("%02d", time.hour),
            fontSize = 48.sp,
            fontWeight = FontWeight.Bold,
            color = if (isSelectingHour) Color(0xFF6200EA) else Color.Black,
            modifier = Modifier
                .background(
                    color = if (isSelectingHour) Color(0xFF6200EA).copy(alpha = 0.1f) else Color.Transparent,
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 12.dp, vertical = 4.dp)
                .clickable { onHourClick() }
        )

        // Separator
        Text(
            text = ":",
            fontSize = 48.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            modifier = Modifier.padding(horizontal = 8.dp)
        )

        // Minute
        Text(
            text = String.format("%02d", time.minute),
            fontSize = 48.sp,
            fontWeight = FontWeight.Bold,
            color = if (!isSelectingHour) Color(0xFF6200EA) else Color.Black,
            modifier = Modifier
                .background(
                    color = if (!isSelectingHour) Color(0xFF6200EA).copy(alpha = 0.1f) else Color.Transparent,
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 12.dp, vertical = 4.dp)
                .clickable { onMinuteClick() }
        )
    }
}

@Composable
private fun CircularClock(
    selectedTime: TimeData,
    isSelectingHour: Boolean,
    onTimeChanged: (TimeData) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(isSelectingHour) {
                    detectDragGestures { change, _ ->
                        val center = Offset(size.width / 2f, size.height / 2f)
                        val offset = change.position - center
                        val angle = atan2(offset.y, offset.x)
                        val normalizedAngle = (angle + PI / 2).let {
                            if (it < 0) it + 2 * PI else it
                        }

                        if (isSelectingHour) {
                            val hour = ((normalizedAngle / (2 * PI) * 12).roundToInt() % 12)
                            val newTime = selectedTime.withHour(if (hour == 0) 12 else hour)
                            onTimeChanged(newTime)
                        } else {
                            val minute = ((normalizedAngle / (2 * PI) * 60).roundToInt() % 60)
                            val newTime = selectedTime.withMinute(minute)
                            onTimeChanged(newTime)
                        }
                    }
                }
        ) {
            drawCircularClock(
                selectedTime = selectedTime,
                isSelectingHour = isSelectingHour
            )
        }
    }
}

@RequiresApi(Build.VERSION_CODES.O)
private fun DrawScope.drawCircularClock(
    selectedTime: TimeData,
    isSelectingHour: Boolean
) {
    val center = size.center
    val radius = size.minDimension / 2f - 40.dp.toPx()
    val numberRadius = radius - 30.dp.toPx()

    // Draw outer circle
    drawCircle(
        color = Color.Gray.copy(alpha = 0.2f),
        radius = radius,
        center = center,
        style = Stroke(width = 2.dp.toPx())
    )

    // Draw numbers
    if (isSelectingHour) {
        drawHourNumbers(center, numberRadius)
    } else {
        drawMinuteNumbers(center, numberRadius)
    }

    // Draw selection
    drawSelection(
        center = center,
        radius = radius,
        selectedTime = selectedTime,
        isSelectingHour = isSelectingHour
    )
}

private fun DrawScope.drawHourNumbers(center: Offset, radius: Float) {
    for (hour in 1..12) {
        val angle = (hour - 3) * 30 * PI / 180
        val x = center.x + cos(angle).toFloat() * radius
        val y = center.y + sin(angle).toFloat() * radius
        
        drawCircle(
            color = Color.Gray.copy(alpha = 0.3f),
            radius = 16.dp.toPx(),
            center = Offset(x, y)
        )
        
        // Note: In a real implementation, you'd use drawText or a Text composable
        // positioned at these coordinates to show the hour numbers
    }
}

private fun DrawScope.drawMinuteNumbers(center: Offset, radius: Float) {
    for (minute in 0..59 step 5) {
        val angle = (minute - 15) * 6 * PI / 180
        val x = center.x + cos(angle).toFloat() * radius
        val y = center.y + sin(angle).toFloat() * radius
        
        val isMainMinute = minute % 15 == 0
        drawCircle(
            color = Color.Gray.copy(alpha = if (isMainMinute) 0.5f else 0.2f),
            radius = if (isMainMinute) 12.dp.toPx() else 6.dp.toPx(),
            center = Offset(x, y)
        )
    }
}

@RequiresApi(Build.VERSION_CODES.O)
private fun DrawScope.drawSelection(
    center: Offset,
    radius: Float,
    selectedTime: TimeData,
    isSelectingHour: Boolean
) {
    val selectedValue = if (isSelectingHour) selectedTime.hour % 12 else selectedTime.minute
    val totalValues = if (isSelectingHour) 12 else 60
    val angleStep = if (isSelectingHour) 30 else 6

    val angle = (selectedValue * angleStep - 90) * PI / 180
    val selectionRadius = radius - 30.dp.toPx()
    val selectionX = center.x + cos(angle).toFloat() * selectionRadius
    val selectionY = center.y + sin(angle).toFloat() * selectionRadius

    // Draw line from center to selection
    drawLine(
        color = Color(0xFFFF9800),
        start = center,
        end = Offset(selectionX, selectionY),
        strokeWidth = 3.dp.toPx()
    )

    // Draw center dot
    drawCircle(
        color = Color(0xFFFF9800),
        radius = 6.dp.toPx(),
        center = center
    )

    // Draw selection circle
    drawCircle(
        color = Color(0xFFFF9800),
        radius = 20.dp.toPx(),
        center = Offset(selectionX, selectionY)
    )

    // Draw inner white circle
    drawCircle(
        color = Color.White,
        radius = 16.dp.toPx(),
        center = Offset(selectionX, selectionY)
    )
}


