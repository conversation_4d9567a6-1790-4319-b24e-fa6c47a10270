package com.noteactive.app.core.presentation.designsystem.composable.datetime

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Date and Time Picker Dialog Component
 * 
 * A comprehensive dialog that allows users to select both date and time,
 * with the ability to switch between start date/time and end date/time.
 * 
 * Features:
 * - Date selection with calendar view
 * - Time selection with circular clock interface
 * - Toggle between start and end date/time
 * - Material 3 design
 * - Customizable styling
 */
@RequiresApi(Build.VERSION_CODES.O)
data class DateTimeSelection  constructor(
    val startDate: LocalDate = LocalDate.now(),
    val startTime: LocalTime = LocalTime.now(),
    val endDate: LocalDate = LocalDate.now(),
    val endTime: LocalTime = LocalTime.now().plusHours(1)
)

enum class DateTimeMode {
    START, END
}

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateTimePickerDialog(
    isVisible: Boolean,
    initialSelection: DateTimeSelection = DateTimeSelection(),
    onDismiss: () -> Unit,
    onConfirm: (DateTimeSelection) -> Unit,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            var currentSelection by remember { mutableStateOf(initialSelection) }
            var currentMode by remember { mutableStateOf(DateTimeMode.START) }
            var isSelectingTime by remember { mutableStateOf(true) }

            Card(
                modifier = modifier
                    .fillMaxWidth(0.9f)
                    .fillMaxHeight(0.8f),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    // Header with back button and title
                    DateTimePickerHeader(
                        title = if (isSelectingTime) "Select time" else "Select date",
                        onBackClick = onDismiss
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Date/Time mode toggle
                    DateTimeModeToggle(
                        currentMode = currentMode,
                        currentSelection = currentSelection,
                        isSelectingTime = isSelectingTime,
                        onModeChange = { currentMode = it },
                        onToggleView = { isSelectingTime = !isSelectingTime }
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Main content area
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        if (isSelectingTime) {
                            // Time Picker
                            CircularTimePicker(
                                selectedTime = if (currentMode == DateTimeMode.START) 
                                    currentSelection.startTime else currentSelection.endTime,
                                onTimeSelected = { newTime ->
                                    currentSelection = if (currentMode == DateTimeMode.START) {
                                        currentSelection.copy(startTime = newTime)
                                    } else {
                                        currentSelection.copy(endTime = newTime)
                                    }
                                }
                            )
                        } else {
                            // Date Picker
                            CalendarDatePicker(
                                selectedDate = if (currentMode == DateTimeMode.START) 
                                    currentSelection.startDate else currentSelection.endDate,
                                onDateSelected = { newDate ->
                                    currentSelection = if (currentMode == DateTimeMode.START) {
                                        currentSelection.copy(startDate = newDate)
                                    } else {
                                        currentSelection.copy(endDate = newDate)
                                    }
                                }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Action buttons
                    DateTimePickerActions(
                        onCancel = onDismiss,
                        onSave = { onConfirm(currentSelection) }
                    )
                }
            }
        }
    }
}

@Composable
private fun DateTimePickerHeader(
    title: String,
    onBackClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onBackClick) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "Back",
                tint = Color.Black
            )
        }
        
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black,
            modifier = Modifier.padding(start = 8.dp)
        )
    }
}

@RequiresApi(Build.VERSION_CODES.O)
@Composable
private fun DateTimeModeToggle(
    currentMode: DateTimeMode,
    currentSelection: DateTimeSelection,
    isSelectingTime: Boolean,
    onModeChange: (DateTimeMode) -> Unit,
    onToggleView: () -> Unit
) {
    Column {
        // Start/End toggle buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            DateTimeModeButton(
                text = "From",
                time = currentSelection.startTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                date = currentSelection.startDate.format(DateTimeFormatter.ofPattern("MMM dd")),
                isSelected = currentMode == DateTimeMode.START,
                onClick = { onModeChange(DateTimeMode.START) },
                modifier = Modifier.weight(1f)
            )
            
            DateTimeModeButton(
                text = "To",
                time = currentSelection.endTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                date = currentSelection.endDate.format(DateTimeFormatter.ofPattern("MMM dd")),
                isSelected = currentMode == DateTimeMode.END,
                onClick = { onModeChange(DateTimeMode.END) },
                modifier = Modifier.weight(1f)
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Time/Date view toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedButton(
                onClick = { if (!isSelectingTime) onToggleView() },
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = if (isSelectingTime) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f) else Color.Transparent
                ),
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Time",
                    color = if (isSelectingTime) MaterialTheme.colorScheme.primary else Color.Gray
                )
            }
            
            OutlinedButton(
                onClick = { if (isSelectingTime) onToggleView() },
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = if (!isSelectingTime) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f) else Color.Transparent
                ),
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Date",
                    color = if (!isSelectingTime) MaterialTheme.colorScheme.primary else Color.Gray
                )
            }
        }
    }
}

@Composable
private fun DateTimeModeButton(
    text: String,
    time: String,
    date: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = if (isSelected) Color(0xFFFFF3E0) else Color.Transparent,
            contentColor = if (isSelected) Color(0xFFFF9800) else Color.Gray
        ),
        border = ButtonDefaults.outlinedButtonBorder.copy(
            brush = androidx.compose.ui.graphics.SolidColor(
                if (isSelected) Color(0xFFFF9800) else Color.Gray
            )
        ),
        modifier = modifier
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = text,
                fontSize = 12.sp,
                fontWeight = FontWeight.Normal
            )
            Text(
                text = time,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = date,
                fontSize = 10.sp,
                fontWeight = FontWeight.Normal
            )
        }
    }
}

@Composable
private fun DateTimePickerActions(
    onCancel: () -> Unit,
    onSave: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        OutlinedButton(
            onClick = onCancel,
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = Color.Gray
            )
        ) {
            Text("Cancel")
        }
        
        Button(
            onClick = onSave,
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFFF9800)
            )
        ) {
            Text(
                text = "Save",
                color = Color.White
            )
        }
    }
}
