package com.noteactive.app.core.data.di

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.dao.ApiSyncDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.colors.ColorDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.gate_ways.GatewaysDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location.LocationDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role.UserRoleDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.sensor.SensorDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserDao
import com.noteactive.app.core.data.repositoryImpl.BaseRepositoryImpl
import com.noteactive.app.core.data.repositoryImpl.SyncRepositoryImpl
import com.noteactive.app.core.domain.repository.SyncRepository
import com.noteactive.app.core.util.preferences.AppPreferences
import com.noteactive.app.features.login.data.network.api.CommonApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent


@Module
@InstallIn(SingletonComponent::class)
class ProvideSyncRepository {
    @Provides
    fun provideSyncModule(
        appPreferences: AppPreferences,
        apiSync: ApiSyncDao,
        baseRepositoryImpl: BaseRepositoryImpl,
        commonApiService: CommonApiService,
        userDao: UserDao,
        colorDao: ColorDao,
        locationDao: LocationDao,
        gatewaysDao: GatewaysDao,
        userRoleDao: UserRoleDao,
        sensorDao: SensorDao,
    ): SyncRepository =
        SyncRepositoryImpl(
            appPreferences, apiSync, baseRepositoryImpl, commonApiService, userDao,
            colorDao, locationDao, gatewaysDao, userRoleDao, sensorDao
        )
}