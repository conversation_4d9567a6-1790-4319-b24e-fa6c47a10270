package com.noteactive.app.core.data.di

import com.noteactive.app.core.data.repositoryImpl.UserLocalRepositoryImpl
import com.noteactive.app.core.domain.repository.UserLocalRepository
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class PasswordModule {
    @Provides
    fun providePasswordModule(userDao: UserDao): UserLocalRepository =
        UserLocalRepositoryImpl(userDao)
}
