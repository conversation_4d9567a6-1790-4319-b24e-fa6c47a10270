package com.noteactive.app.core.presentation.designsystem.composable.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.LightColorPalette
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme

@Composable
fun PrimaryButton(
    modifier: Modifier = Modifier,
    text: String,
    isEnabled: Boolean = true,
    textStyle: TextStyle = MaterialTheme.typography.titleSmall,
    buttonClick: () -> Unit
) {

    val colors = ButtonDefaults.buttonColors(
        disabledContainerColor = MaterialTheme.colorScheme.onSurface,
        disabledContentColor = MaterialTheme.colorScheme.background
    )

    Button(
        onClick = {
            buttonClick.invoke()
        },
        modifier = modifier
            .height(44.dp),
        enabled = isEnabled,
        shape = RoundedCornerShape(4.dp),
        colors = colors
    ) {
        Text(text = text, style = textStyle)
    }
}

@Composable
fun SecondaryButton(
    modifier: Modifier = Modifier,
    text: String,
    isEnabled: Boolean = false,
    buttonClick: () -> Unit
) {

    OutlinedButton(
        onClick = {
            buttonClick.invoke()
        },
        modifier = modifier
            .height(44.dp),
        enabled = isEnabled,
        shape = RoundedCornerShape(4.dp),
        border = BorderStroke(1.dp, Color(0xFF2A333C)),

        ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2A333C)
        )
    }
}

@PreviewScreenSizes
@Preview(showBackground = true)
@Composable
fun AppButtonPreview(modifier: Modifier = Modifier) {
    MyAppTheme  {
        PrimaryButton(text = "Hello", modifier = Modifier.fillMaxWidth()) {
        }
    }
}

@PreviewScreenSizes
@Preview(showBackground = true)
@Composable
fun AppOutlineButtonPreview(modifier: Modifier = Modifier) {
    MyAppTheme {
        SecondaryButton(text = "Hello", modifier = Modifier.fillMaxWidth()) {

        }
    }
}