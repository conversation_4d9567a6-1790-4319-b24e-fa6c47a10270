package com.noteactive.app.core.data.repositoryImpl

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.FusedLocationProviderClient
import com.noteactive.app.core.domain.entity.location.DeviceLocation
import com.noteactive.app.core.domain.repository.BaseRepository
import com.noteactive.app.core.util.preferences.AppPreferences
import com.noteactive.app.core.util.advertising_id.AdvertisingIdManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject


class BaseRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appPreferences: AppPreferences,
    private val fusedLocationProviderClient: FusedLocationProviderClient
): BaseRepository {

    override suspend fun getAdId(): String {
        var adId = appPreferences.getAdId()
        if (adId.isNullOrEmpty()) {
            adId = AdvertisingIdManager.getAdvertisingId(context)
            if (adId.isNotEmpty()) {
                appPreferences.saveAdId(adId)
            }
        }
        return adId
    }

    override suspend fun getLastKnownLocation(): DeviceLocation? = suspendCancellableCoroutine { cont ->
        if (
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
        ) {
            cont.resume(null, null)
            return@suspendCancellableCoroutine
        }

        fusedLocationProviderClient.lastLocation
            .addOnSuccessListener { location ->
                if (location != null) {
                    cont.resume(DeviceLocation(location.latitude, location.longitude), null)
                } else {
                    cont.resume(null, null)
                }
            }
            .addOnFailureListener {
                cont.resume(null, null)
            }
    }

}