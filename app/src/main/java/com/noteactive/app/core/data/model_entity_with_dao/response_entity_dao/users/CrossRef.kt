package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Junction
import androidx.room.Relation

@Entity(primaryKeys = ["userId", "facilityId"])
data class UserFacilityCrossRef(val userId: Int, val facilityId: String)

@Entity(primaryKeys = ["userId", "customerId"])
data class UserCustomerCrossRef(val userId: Int, val customerId: String)


data class UserWithFacilities(
    @Embedded val user: UserEntity,
    @Relation(
        parentColumn = "userId",
        entityColumn = "facilityId",
        associateBy = Junction(UserFacilityCrossRef::class)
    )
    val facilities: List<FacilityEntity>
)

data class UserWithCustomers(
    @Embedded val user: UserEntity,
    @Relation(
        parentColumn = "userId",
        entityColumn = "customerId",
        associateBy = Junction(UserCustomerCrossRef::class)
    )
    val customers: List<CustomerEntity>
)
