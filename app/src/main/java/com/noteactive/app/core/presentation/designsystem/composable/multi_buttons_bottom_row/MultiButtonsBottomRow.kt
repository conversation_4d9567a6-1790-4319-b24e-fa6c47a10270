package com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.composable.button.SecondaryButton
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme

@Composable
fun MultiButtonBottomRow(
    modifier: Modifier = Modifier,
     @StringRes secondaryButtonTitle: Int,
    @StringRes primaryButtonTitle: Int,
    secondaryButtonClickListener: () -> Unit,
    primaryButtonClickListener: () -> Unit,
    isSecondaryButtonEnabled: Boolean = true,
    isPrimaryButtonEnabled: Boolean = true,
) {
    Row(
        modifier = modifier.fillMaxWidth().background(Color.White)
            .navigationBarsPadding() // Prevents bottom overlap
            .imePadding()
            .shadow(1.dp)
            .padding(top = 12.dp, bottom = 12.dp, start = 12.dp, end = 12.dp),
        horizontalArrangement = Arrangement.End
    ) {
        SecondaryButton(modifier = Modifier.padding(end = 16.dp),
            text = stringResource(secondaryButtonTitle),
            isEnabled = isSecondaryButtonEnabled
        ) {
            secondaryButtonClickListener.invoke()
        }
        PrimaryButton(
            text = stringResource(primaryButtonTitle),
            isEnabled = isPrimaryButtonEnabled
        ) {
            primaryButtonClickListener.invoke()

        }
    }
}

@PreviewScreenSizes
@Preview
@Composable
fun MultiButtonBottomRowPreview() {
    MyAppTheme {
        MultiButtonBottomRow(
            secondaryButtonTitle = R.string.close,
            primaryButtonTitle = R.string.activate,
            secondaryButtonClickListener = {
            },
            primaryButtonClickListener = {
            })
    }

}