package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction

@Dao
interface UserRoleDao  {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUsers(list: List<UserRoleEntity>)

    @Query("DELETE FROM user_role")
    fun deleteAll()

    @Transaction
    fun insertUserRole(list: List<UserRoleEntity>) {
        deleteAll()
        insertUsers(list)
    }
}