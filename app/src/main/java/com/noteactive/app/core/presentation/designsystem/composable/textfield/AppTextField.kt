package com.noteactive.app.core.presentation.designsystem.composable.textfield

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular

@Composable
fun DefaultOutlinedTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    label: String? = null,
    maxLines: Int = 1,
    enabled: Boolean = true,
    focusRequester: FocusRequester = FocusRequester(),
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Done,
    onImeAction: (() -> Unit)? = null,
    trailingIcon: (@Composable (() -> Unit))? = null,
) {
    var isFocused by remember { mutableStateOf(false) }

    val colorDefaultOutlineTextField = OutlinedTextFieldDefaults.colors(
        focusedBorderColor = Color(0xFFC3CCD5),
        unfocusedBorderColor = Color(0xFFC3CCD5),
        unfocusedLabelColor = Color(0xFF889AAA),
        focusedTextColor = Color(0xFF151A1E),
        disabledLabelColor = Color(0xFF889AAA),
    )

    OutlinedTextField(
        value = value,
        onValueChange = { onValueChange(it) },
        enabled = enabled,
        label = {
            if (!isFocused && value.isEmpty()) {
                label?.let { Text(it) }
            }
        },
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged {
                isFocused = it.isFocused
            },
        textStyle = TextStyle(fontFamily = CustomFontNunitoRegular, fontSize = 14.sp),
        singleLine = maxLines == 1,
        maxLines = maxLines,
        trailingIcon = trailingIcon,
        colors = colorDefaultOutlineTextField,
        keyboardOptions = KeyboardOptions(
            keyboardType = keyboardType,
            imeAction = imeAction
        ),
        keyboardActions = KeyboardActions(
            onNext = { if (imeAction == ImeAction.Next) onImeAction?.invoke() },
            onDone = { if (imeAction == ImeAction.Done) onImeAction?.invoke() }
        )
    )
}

@PreviewScreenSizes
@Preview
@Composable
fun DefaultOutlinedTextFieldPreview() {
    DefaultOutlinedTextField(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        value = "",
        onValueChange = {},
        label = "Label",
        focusRequester = FocusRequester(),
        keyboardType = KeyboardType.Text,
        imeAction = ImeAction.Next
    )
}



