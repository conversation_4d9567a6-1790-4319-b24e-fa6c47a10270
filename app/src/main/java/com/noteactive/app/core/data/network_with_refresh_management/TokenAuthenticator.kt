package com.noteactive.app.core.data.network_with_refresh_management

import com.noteactive.app.core.util.preferences.AppPreferences
import kotlinx.coroutines.runBlocking
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import javax.inject.Inject

class TokenAuthenticator @Inject constructor(
    private val tokenManager: AppPreferences,
    private val retrier: RequestRetries,
    private val refreshService: RefreshTokenService
) : Authenticator {
    override fun authenticate(route: Route?, response: Response): Request? {
        if (responseCount(response) >= 2) return null

        val originalRequest = response.request

        return runBlocking {
            retrier.retryAfterRefresh(originalRequest) {
                // 👇 Move refreshToken logic here
                val refreshToken = tokenManager.accessToken ?: return@retryAfterRefresh false
                try {
                    val response = refreshService.refreshToken(RefreshTokenRequest(refreshToken)).execute()
                    if (response.isSuccessful) {
                        response.body()?.let {
                            tokenManager.accessToken = it.accessToken
                            return@retryAfterRefresh true
                        }
                    }
                    false
                } catch (e: Exception) {
                    false
                }
            }
        }
    }


    private fun responseCount(response: Response): Int {
        var count = 1
        var prior = response.priorResponse
        while (prior != null) {
            count++
            prior = prior.priorResponse
        }
        return count
    }
}


