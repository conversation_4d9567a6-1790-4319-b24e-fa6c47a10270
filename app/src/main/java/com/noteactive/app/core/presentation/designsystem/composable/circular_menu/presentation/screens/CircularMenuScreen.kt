package com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.screens

//import androidx.compose.foundation.background
//import androidx.compose.foundation.border
//import androidx.compose.foundation.clickable
//import androidx.compose.foundation.layout.Arrangement
//import androidx.compose.foundation.layout.Box
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.fillMaxSize
//import androidx.compose.foundation.layout.offset
//import androidx.compose.foundation.layout.size
//import androidx.compose.foundation.shape.CircleShape
//import androidx.compose.material.icons.Icons
//import androidx.compose.material.icons.filled.Close
//import androidx.compose.material3.Icon
//import androidx.compose.material3.MaterialTheme
//import androidx.compose.material3.Text
//import androidx.compose.material3.adaptive.currentWindowAdaptiveInfo
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.LaunchedEffect
//import androidx.compose.runtime.getValue
//import androidx.compose.ui.Alignment
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.draw.clip
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.platform.LocalConfiguration
//import androidx.compose.ui.platform.LocalDensity
//import androidx.compose.ui.res.painterResource
//import androidx.compose.ui.text.font.FontWeight
//import androidx.compose.ui.tooling.preview.Preview
//import androidx.compose.ui.tooling.preview.PreviewScreenSizes
//import androidx.compose.ui.unit.dp
//import androidx.compose.ui.unit.sp
//import androidx.compose.ui.window.Dialog
//import androidx.hilt.navigation.compose.hiltViewModel
//import androidx.lifecycle.compose.collectAsStateWithLifecycle
//import com.noteactive.app.R
//import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.action.CircularMenuNavigation
//import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.action.CircularMenuViewModelAction
//import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.state.CircularMenuState
//import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.viewmodel.CircularMenuViewModel
//import com.noteactive.app.core.presentation.designsystem.theme.DeviceConfiguration
//import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
//import kotlin.math.cos
//import kotlin.math.sin
//
//@Composable
//fun CircularMenuScreen(
//    modifier: Modifier = Modifier,
//    onNavigation: (CircularMenuNavigation) -> Unit
//) {
//    val viewModel: CircularMenuViewModel = hiltViewModel()
//    val circularMenuState by viewModel.circularMenuState.collectAsStateWithLifecycle()
//    val navigationEvent by viewModel.navigationEvent.collectAsStateWithLifecycle()
//
//    LaunchedEffect(navigationEvent) {
//        navigationEvent?.let { event ->
//            onNavigation(event)
//            viewModel.clearNavigationEvent()
//        }
//    }
//
//    CircularMenuRoot(
//        modifier = modifier,
//        circularMenuState = circularMenuState,
//        onViewModelAction = viewModel::onAction
//    )
//}
//
//@Composable
//fun CircularMenuRoot(
//    modifier: Modifier = Modifier,
//    circularMenuState: CircularMenuState,
//    onViewModelAction: (CircularMenuViewModelAction) -> Unit
//) {
//
//    Dialog(
//        onDismissRequest = {},
//    ) {
//        Box(
//            modifier = modifier
//                .fillMaxSize().background(Color.Transparent),
//            contentAlignment = Alignment.BottomCenter
//        ) {
//
//            val centerX = 0f
//            val windowSizeClass = currentWindowAdaptiveInfo().windowSizeClass
//            val deviceConfiguration = DeviceConfiguration.fromWindowSizeClass(windowSizeClass)
//            val centerY = with(LocalDensity.current) {
//                when(deviceConfiguration){
//                    DeviceConfiguration.MOBILE_PORTRAIT -> {
//                        (-20.dp).toPx()                }
//                    DeviceConfiguration.MOBILE_LANDSCAPE -> {
//                        (-20.dp).toPx()
//                    }
//                    DeviceConfiguration.TABLET_PORTRAIT -> {
//                        (-20.dp).toPx()
//                    }
//                    DeviceConfiguration.TABLET_LANDSCAPE -> {
//                        (-20.dp).toPx()
//                    }
//                    DeviceConfiguration.DESKTOP -> {
//                        (-20.dp).toPx()
//                    }
//                }
//
//            }
//            val radius = with(LocalDensity.current) {
//                when(deviceConfiguration){
//                    DeviceConfiguration.MOBILE_PORTRAIT -> {
//                        30.dp.toPx()           }
//                    DeviceConfiguration.MOBILE_LANDSCAPE -> {
//                        30.dp.toPx()
//                    }
//                    DeviceConfiguration.TABLET_PORTRAIT -> {
//                        60.dp.toPx()
//                    }
//                    DeviceConfiguration.TABLET_LANDSCAPE -> {
//                        60.dp.toPx()
//                    }
//                    DeviceConfiguration.DESKTOP -> {
//                        (-20.dp).toPx()
//                    }
//                }
//
//            }
//
//            val isTablet =  when(deviceConfiguration){
//                DeviceConfiguration.MOBILE_PORTRAIT -> {
//                    false
//                }
//                DeviceConfiguration.MOBILE_LANDSCAPE -> {
//                    false
//                }
//                DeviceConfiguration.TABLET_PORTRAIT -> {
//                    true
//                }
//                DeviceConfiguration.TABLET_LANDSCAPE -> {
//                    true
//                }
//                DeviceConfiguration.DESKTOP -> {
//                    false
//                }
//            }
//
//            // Menu items data
//            val menuItems = listOf(
//                MenuItemData("SOS", R.drawable.ic_note_test5, CircularMenuViewModelAction.OnSOSClicked),
//                MenuItemData("Camera", R.drawable.ic_dropdown, CircularMenuViewModelAction.OnCameraClicked),
//                MenuItemData("Icon 1", R.drawable.ic_note_test1, CircularMenuViewModelAction.OnIcon1Clicked),
//                MenuItemData("Barcode", R.drawable.ic_bottom_nav_offender, CircularMenuViewModelAction.OnBarcodeClicked),
//                MenuItemData("Home", R.drawable.ic_note_test6, CircularMenuViewModelAction.OnHomeClicked)
//            )
//
//            menuItems.forEachIndexed { index, item ->
//                // Create a semicircle (180 degrees) starting from left and going to right
//                val angle = (index * 45.0 + 180.0) * Math.PI / 180.0 // 45 degrees apart, semicircle from left to right
//                val x = (centerX + radius * cos(angle)).dp
//                val y = (centerY + radius * sin(angle)).dp
//
//                CircularMenuItem(
//                    modifier = Modifier.offset(x = x, y = y),
//                    label = item.label,
//                    iconRes = item.iconRes,
//                    onClick = { onViewModelAction(item.action) }
//                )
//            }
//
//            // Central close button positioned in the center of the semicircle
//            Box(
//                modifier = Modifier
//                    .size(if (isTablet) 60.dp else 50.dp)
//                    .offset(y = if (isTablet) (-90).dp else (-40).dp) // Position it at the center of the semicircle
//                    .clip(CircleShape)
//                    .background(Color(0xFF2A333C))
//                    .clickable { onViewModelAction(CircularMenuViewModelAction.OnCloseClicked) },
//                contentAlignment = Alignment.Center
//            ) {
//                Icon(
//                    imageVector = Icons.Default.Close,
//                    contentDescription = "Close",
//                    tint = Color.White,
//                    modifier = Modifier.size(if (isTablet) 24.dp else 20.dp)
//                )
//            }
//        }
//    }
//
//    }
//
//
//@Composable
//fun CircularMenuItem(
//    modifier: Modifier = Modifier,
//    label: String,
//    iconRes: Int,
//    onClick: () -> Unit
//) {
//    val windowSizeClass = currentWindowAdaptiveInfo().windowSizeClass
//
//    val deviceConfiguration = DeviceConfiguration.fromWindowSizeClass(windowSizeClass)
//
//    val isTablet =  when(deviceConfiguration){
//        DeviceConfiguration.MOBILE_PORTRAIT -> {
//            false
//        }
//        DeviceConfiguration.MOBILE_LANDSCAPE -> {
//            false
//        }
//        DeviceConfiguration.TABLET_PORTRAIT -> {
//            true
//        }
//        DeviceConfiguration.TABLET_LANDSCAPE -> {
//            true
//        }
//        DeviceConfiguration.DESKTOP -> {
//            false
//        }
//    }
//
//    Column(
//        modifier = modifier,
//        horizontalAlignment = Alignment.CenterHorizontally,
//        verticalArrangement = Arrangement.Center
//    ) {
//        Box(
//            modifier = Modifier
//                .size(if (isTablet) 60.dp else 40.dp)
//                .clip(CircleShape)
//                .border(2.dp, MaterialTheme.colorScheme.primary, CircleShape)
//                .background(Color.White)
//                .clickable { onClick() },
//            contentAlignment = Alignment.Center
//        ) {
//            Icon(
//                painter = painterResource(id = iconRes),
//                contentDescription = label,
//                tint = Color(0xFF2A333C),
//                modifier = Modifier.size(if (isTablet) 24.dp else 20.dp)
//            )
//        }
//
//        Text(
//            text = label,
//            fontSize = if (isTablet) 12.sp else 10.sp,
//            fontWeight = FontWeight.Medium,
//            color = Color(0xFF2A333C),
//            modifier = Modifier.offset(y = if (isTablet) 8.dp else 6.dp)
//        )
//    }
//}
//
//data class MenuItemData(
//    val label: String,
//    val iconRes: Int,
//    val action: CircularMenuViewModelAction
//)
//
//@PreviewScreenSizes
//@Preview
//@Composable
//fun CircularMenuScreenPreview() {
//    MyAppTheme {
//        CircularMenuRoot(
//            circularMenuState = CircularMenuState(),
//            onViewModelAction = {}
//        )
//    }
//}



import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme

@Composable
fun FloatingButtonPanel(
    modifier: Modifier =Modifier,
    onSosClick: () -> Unit = {},
    onCameraClick: () -> Unit = {},
    onHomeClick: () -> Unit = {},
    onBarcodeClick: () -> Unit = {},
    onProfileClick: () -> Unit = {},
    onCloseClick: () -> Unit = {}
) {
    val buttons = listOf(
        FloatingButtonData("SOS", R.drawable.ic_camera, Color(0xFFFF6B35), onSosClick),
        FloatingButtonData("Camera", R.drawable.ic_attachment, Color(0xFF4A90E2), onCameraClick),
        FloatingButtonData("Home", R.drawable.ic_camera, Color(0xFF7ED321), onHomeClick),
        FloatingButtonData("Barcode", R.drawable.ic_camera, Color(0xFFFF6B35), onBarcodeClick),
        FloatingButtonData("Profile", R.drawable.ic_attachment, Color(0xFF4A90E2), onProfileClick)
    )

//    Box(
//        modifier = Modifier
//            .fillMaxSize()
//    ) {
        Box(
            modifier = Modifier
                .size(200.dp)
                .padding(20.dp),

        ) {
            // Arrange buttons in circular pattern
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                // Top button (Camera)
                Box(
                    modifier = Modifier
                        .offset(y = (-40).dp)
                        .align(Alignment.TopCenter)
                ) {
                    FloatingButton(button = buttons[1])
                }

                // Top-left button (SOS)
                Box(
                    modifier = Modifier
                        .offset(x = (-40).dp, y = (0).dp)
                        .align(Alignment.TopStart)
                ) {
                    FloatingButton(button = buttons[0])
                }

                // Top-right button (Home)
                Box(
                    modifier = Modifier
                        .offset(x = 40.dp, y = (0).dp)
                        .align(Alignment.TopEnd)
                ) {
                    FloatingButton(button = buttons[2])
                }

                // Bottom-left button (Barcode)
                Box(
                    modifier = Modifier
                        .offset(x = (-70).dp, y = -20.dp)
                        .align(Alignment.BottomStart)
                ) {
                    FloatingButton(button = buttons[3])
                }

                // Bottom-right button (Profile)
                Box(
                    modifier = Modifier
                        .offset(x = 70.dp, y = -20.dp)
                        .align(Alignment.BottomEnd)
                ) {
                    FloatingButton(button = buttons[4])
                }

                // Center close button
                Box(
                    modifier = Modifier
                        .offset(x = 0.dp, y = 40.dp)
                        .size(56.dp)
                        .clip(CircleShape)
                        .background(Color.Gray.copy(alpha = 0.6f))
                        .clickable { onCloseClick() },
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp).align(Alignment.Center)
                    )
                }
            }
        }
//    }

}

@Composable
private fun FloatingButton(button: FloatingButtonData) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(46.dp)
                .clip(CircleShape)
                .border(2.dp, MaterialTheme.colorScheme.primary, CircleShape)
                .background(Color.White)
                .clickable { button.onClick() },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                painter = painterResource(button.icon),
                contentDescription = button.label,
                modifier = Modifier.size(24.dp)
            )
        }

        Text(
            text = button.label,
            fontSize = 10.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF2A333C),
        )


    }
}

private data class FloatingButtonData(
    val label: String,
    val icon: Int,
    val color: Color,
    val onClick: () -> Unit
)

// Example usage in your screen
@Composable
fun ExampleScreen() {
    var showFloatingPanel by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .background(Color.Gray.copy(alpha = 0.3f))
            .fillMaxSize()
    ) {
        // Your main content here
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Button(
                onClick = { showFloatingPanel = !showFloatingPanel }
            ) {
                Text("Toggle Floating Panel")
            }
        }

        // Floating panel positioned at bottom
        if (showFloatingPanel) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = 80.dp),
                contentAlignment = Alignment.BottomCenter
            ) {
                FloatingButtonPanel(
                    onSosClick = { /* Handle SOS click */ },
                    onCameraClick = { /* Handle Camera click */ },
                    onHomeClick = { /* Handle Home click */ },
                    onBarcodeClick = { /* Handle Barcode click */ },
                    onProfileClick = { /* Handle Profile click */ },
                    onCloseClick = { showFloatingPanel = false }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun FloatingButtonPanelPreview() {
    MyAppTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Gray.copy(alpha = 0.3f))
                ,
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Bottom
        ) {
            FloatingButtonPanel()
        }
    }

}