package com.noteactive.app.core.presentation.designsystem.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.noteactive.app.R

val CustomFontNunitoBold = FontFamily(Font(R.font.nunito_bold))
val CustomFontNunitoRegular = FontFamily(Font(R.font.nunito_regular))
val CustomFontNunitoLight = FontFamily(Font(R.font.nunito_light))

//val MobileTypography = Typography(
//    titleSmall = TextStyle(
//        fontFamily = CustomFontNunitoBold,
//        fontWeight = FontWeight.W400,
//        fontSize = 14.sp
//    ),
//
//    headlineSmall = TextStyle(
//        fontFamily = CustomFontNunitoBold,
//        fontWeight = FontWeight.W700,
//        fontSize = 14.sp
//    ),
//    titleMedium = TextStyle(
//        fontFamily = CustomFontNunitoRegular,
//        fontWeight = FontWeight.W400,
//        fontSize = 14.sp
//    ),
//    bodyMedium = TextStyle(
//        fontFamily = CustomFontNunitoBold,
//        fontWeight = FontWeight.W400,
//        fontSize = 12.sp
//    ),
//    bodyLarge = TextStyle(
//        fontFamily = CustomFontNunitoBold,
//        fontWeight = FontWeight.W400,
//        fontSize = 12.sp
//    ),
//    bodySmall = TextStyle(
//        fontFamily = CustomFontNunitoRegular,
//        fontWeight = FontWeight.W400,
//        fontSize = 12.sp
//    ),
//)

val TabTypography = Typography(
    labelSmall = TextStyle(
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Medium
    ),
    labelMedium = TextStyle(
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Medium
    ),
    labelLarge = TextStyle(
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        fontFamily = CustomFontNunitoBold,
        fontWeight = FontWeight.Medium
    ),

    // Body
    bodySmall = TextStyle(
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Normal
    ),
    bodyMedium = TextStyle(
        fontSize = 14.sp,
        lineHeight = 20.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Normal
    ),
    bodyLarge = TextStyle(
        fontSize = 16.sp,
        lineHeight = 24.sp,
        fontFamily = CustomFontNunitoRegular,
        letterSpacing = 0.5.sp,
        fontWeight = FontWeight.Normal
    ),

    // Titles
    titleSmall = TextStyle(
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        fontFamily = CustomFontNunitoRegular,
    ),
    titleMedium = TextStyle(
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp,
        fontWeight = FontWeight.Medium,
        fontFamily = CustomFontNunitoRegular,

        ),
    titleLarge = TextStyle(
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp,
        fontWeight = FontWeight.Normal,
        fontFamily = CustomFontNunitoRegular,
    )
)


val MobileTypography = Typography(
    // Labels
    labelSmall = TextStyle(
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Medium
    ),
    labelMedium = TextStyle(
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Medium
    ),
    labelLarge = TextStyle(
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        fontFamily = CustomFontNunitoBold,
        fontWeight = FontWeight.Medium
    ),

    // Body
    bodySmall = TextStyle(
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Normal
    ),
    bodyMedium = TextStyle(
        fontSize = 14.sp,
        lineHeight = 20.sp,
        fontFamily = CustomFontNunitoRegular,
        fontWeight = FontWeight.Normal
    ),
    bodyLarge = TextStyle(
        fontSize = 16.sp,
        lineHeight = 24.sp,
        fontFamily = CustomFontNunitoRegular,
        letterSpacing = 0.5.sp,
        fontWeight = FontWeight.Normal
    ),

    // Titles
    titleSmall = TextStyle(
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        fontFamily = CustomFontNunitoRegular,
    ),
    titleMedium = TextStyle(
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp,
        fontWeight = FontWeight.Medium,
        fontFamily = CustomFontNunitoRegular,

        ),
    titleLarge = TextStyle(
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp,
        fontWeight = FontWeight.Normal,
        fontFamily = CustomFontNunitoRegular,
    )
)

//| Category  | Style       | Font Size | Line Height | Letter Spacing | Font Weight | Font                              |
//| --------- | ----------- | --------- | ----------- | -------------- | ----------- | --------------------------------- |
//| **Label** | labelSmall  | 11.sp     | 16.sp       | 0.5.sp         | Medium      | Nunito Regular                    |
//|           | labelMedium | 12.sp     | 16.sp       | 0.5.sp         | Medium      | Nunito Regular                    |
//|           | labelLarge  | 14.sp     | 20.sp       | 0.1.sp         | Medium      | Nunito **Bold** (visually bolder) |
//| **Body**  | bodySmall   | 12.sp     | 16.sp       | 0.4.sp         | Normal      | Nunito Regular                    |
//|           | bodyMedium  | 14.sp     | 20.sp       | *None*         | Normal      | Nunito Regular                    |
//|           | bodyLarge   | 16.sp     | 24.sp       | 0.5.sp         | Normal      | Nunito Regular                    |
//| **Title** | titleSmall  | 14.sp     | 20.sp       | 0.1.sp         | *Default*   | Nunito Regular                    |
//|           | titleMedium | 16.sp     | 24.sp       | 0.15.sp        | Medium      | Nunito Regular                    |
//|           | titleLarge  | 22.sp     | 28.sp       | 0.sp           | Normal      | Nunito Regular                    |

