package com.noteactive.app.core.data.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.colors.ColorEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.ApiUrl
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.CustomerInfo
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.Facility
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.gate_ways.GatewaysEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location.LocationEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role.UserRoleEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.sensor.SensorEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.CustomerEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.FacilityEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserCustomerCrossRef
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserFacilityCrossRef
import com.noteactive.app.features.login.data.model.dao.activation_key_dao.ActivationKeyResponseDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.ApiUrlDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.CustomerInfoDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.FacilityDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location.LocationDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserDao
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiSync
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.dao.ApiSyncDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.colors.ColorDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.gate_ways.GatewaysDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role.UserRoleDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.sensor.SensorDao

@Database(
    entities = [
        ActivationKeyResponseUserInfo::class, ApiUrl::class,
        Facility::class, CustomerInfo::class,
        UserEntity::class,
        ColorEntity::class,
        FacilityEntity::class,
        UserRoleEntity::class,
        CustomerEntity::class,
        UserFacilityCrossRef::class,
        UserCustomerCrossRef::class,
        ApiSync::class,
        SensorEntity::class,
        GatewaysEntity::class,
        LocationEntity::class],
    version = 2,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun activationKeyResponseDao(): ActivationKeyResponseDao
    abstract fun customerInfoDao(): CustomerInfoDao
    abstract fun apiUrlDao(): ApiUrlDao
    abstract fun facilityDao(): FacilityDao
    abstract fun colorDao(): ColorDao
    abstract fun userRoleDao(): UserRoleDao
    abstract fun userDao(): UserDao
    abstract fun apiSyncDao(): ApiSyncDao
    abstract fun gatewaysDao(): GatewaysDao
    abstract fun locationDao(): LocationDao
    abstract fun sensorDao(): SensorDao
}
