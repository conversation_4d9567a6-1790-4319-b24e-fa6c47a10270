package com.noteactive.app.core.presentation.designsystem.composable.empty_note_row

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.theme.NoteRowDivider
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider

@Composable
fun addEmptyViews(rowCount :Int) {
    repeat(rowCount) {
        Row(
            modifier = Modifier
                .background(Color.White)
                .heightIn(min = 36.dp)
                .height(IntrinsicSize.Min)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {

            Box(
                modifier = Modifier
                    .width(40.dp)
                    .fillMaxHeight()
                    .background(Color.White)
            )

            VerticalDivider(
                color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight()
            )


        }
        HorizontalDivider(color = NoteRowDivider)
    }
}
