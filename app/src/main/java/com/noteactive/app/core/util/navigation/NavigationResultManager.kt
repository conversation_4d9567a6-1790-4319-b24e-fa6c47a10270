package com.noteactive.app.core.util.navigation


import androidx.lifecycle.SavedStateHandle
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

object NavigationResultManager {

    fun <T> setResult(
        key: String,
        value: T,
        savedStateHandle: SavedStateHandle
    ) {
        savedStateHandle.set(key, value)
    }

    fun <T> getResult(
        key: String,
        savedStateHandle: SavedStateHandle
    ): StateFlow<T?> {
        val initialValue = savedStateHandle.get<T>(key)
        val resultFlow = MutableStateFlow(initialValue)
        savedStateHandle.getLiveData<T>(key).observeForever {
            resultFlow.value = it
        }
        return resultFlow.asStateFlow()
    }

    fun <T> clearResult(
        key: String,
        savedStateHandle: SavedStateHandle
    ) {
        savedStateHandle.remove<T>(key)
    }
}
