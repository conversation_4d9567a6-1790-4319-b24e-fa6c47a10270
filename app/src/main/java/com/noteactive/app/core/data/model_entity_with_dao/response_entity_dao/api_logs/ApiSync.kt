package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "api_sync_logs")
data class ApiSync(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0L,
    val apiId: Long,
    val apiType: String,
    val lastSyncTime: String?=null,
    val page: Int ? = null,
    val errorMessage: String ?=null,
    val success: Boolean
)
