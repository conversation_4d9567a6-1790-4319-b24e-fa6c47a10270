package com.noteactive.app.core.presentation.designsystem.composable.datetime

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.text.SimpleDateFormat
import java.util.*

/**
 * Calendar Date Picker Component
 *
 * A calendar-style date picker that allows users to select dates.
 * Features:
 * - Month/year navigation
 * - Grid layout for dates
 * - Highlighted selected date
 * - Material 3 design
 * - Compatible with API 24+
 */

data class CalendarMonth(
    val year: Int,
    val month: Int // 0-based (0 = January, 11 = December)
) {
    fun toCalendar(): Calendar {
        return Calendar.getInstance().apply {
            set(Calendar.YEAR, year)
            set(Calendar.MONTH, month)
            set(Calendar.DAY_OF_MONTH, 1)
        }
    }

    fun addMonths(months: Int): CalendarMonth {
        val calendar = toCalendar()
        calendar.add(Calendar.MONTH, months)
        return CalendarMonth(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH)
        )
    }
}

@Composable
fun CalendarDatePicker(
    selectedDate: Date,
    onDateSelected: (Date) -> Unit,
    modifier: Modifier = Modifier
) {
    val calendar = Calendar.getInstance()
    calendar.time = selectedDate

    var currentMonth by remember {
        mutableStateOf(
            CalendarMonth(
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH)
            )
        )
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Month/Year header with navigation
        CalendarHeader(
            currentMonth = currentMonth,
            onPreviousMonth = { currentMonth = currentMonth.addMonths(-1) },
            onNextMonth = { currentMonth = currentMonth.addMonths(1) }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Days of week header
        DaysOfWeekHeader()

        Spacer(modifier = Modifier.height(8.dp))

        // Calendar grid
        CalendarGrid(
            currentMonth = currentMonth,
            selectedDate = selectedDate,
            onDateSelected = onDateSelected
        )
    }
}

@Composable
private fun CalendarHeader(
    currentMonth: CalendarMonth,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    val monthFormatter = SimpleDateFormat("MMMM yyyy", Locale.getDefault())
    val calendar = currentMonth.toCalendar()

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowLeft,
                contentDescription = "Previous month",
                tint = Color.Gray
            )
        }

        Text(
            text = monthFormatter.format(calendar.time),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black
        )

        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "Next month",
                tint = Color.Gray
            )
        }
    }
}

@Composable
private fun DaysOfWeekHeader() {
    val daysOfWeek = listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat")
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        daysOfWeek.forEach { day ->
            Text(
                text = day,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray,
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun CalendarGrid(
    currentMonth: CalendarMonth,
    selectedDate: Date,
    onDateSelected: (Date) -> Unit
) {
    val calendar = currentMonth.toCalendar()
    val firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1 // Convert to 0-6 (Sun-Sat)
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)

    // Create list of all dates to display (including empty cells for alignment)
    val calendarDates = mutableListOf<Date?>()

    // Add empty cells for days before the first day of the month
    repeat(firstDayOfWeek) {
        calendarDates.add(null)
    }

    // Add all days of the current month
    for (day in 1..daysInMonth) {
        val dayCalendar = Calendar.getInstance().apply {
            set(Calendar.YEAR, currentMonth.year)
            set(Calendar.MONTH, currentMonth.month)
            set(Calendar.DAY_OF_MONTH, day)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        calendarDates.add(dayCalendar.time)
    }

    // Fill remaining cells to complete the grid (6 rows × 7 columns = 42 cells)
    while (calendarDates.size < 42) {
        calendarDates.add(null)
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(calendarDates) { date ->
            CalendarDateCell(
                date = date,
                isSelected = date?.let { isSameDay(it, selectedDate) } ?: false,
                isCurrentMonth = date?.let {
                    val dateCalendar = Calendar.getInstance()
                    dateCalendar.time = it
                    dateCalendar.get(Calendar.MONTH) == currentMonth.month
                } ?: false,
                onClick = { date?.let { onDateSelected(it) } }
            )
        }
    }
}

@Composable
private fun CalendarDateCell(
    date: Date?,
    isSelected: Boolean,
    isCurrentMonth: Boolean,
    onClick: () -> Unit
) {
    val today = Date()
    val isToday = date?.let { isSameDay(it, today) } ?: false

    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(
                color = when {
                    isSelected -> Color(0xFFFF9800)
                    isToday -> Color(0xFFE3F2FD)
                    else -> Color.Transparent
                }
            )
            .clickable(enabled = date != null && isCurrentMonth) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (date != null) {
            val calendar = Calendar.getInstance()
            calendar.time = date

            Text(
                text = calendar.get(Calendar.DAY_OF_MONTH).toString(),
                fontSize = 14.sp,
                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    isSelected -> Color.White
                    isToday -> Color(0xFF1976D2)
                    isCurrentMonth -> Color.Black
                    else -> Color.Gray.copy(alpha = 0.5f)
                },
                textAlign = TextAlign.Center
            )
        }
    }
}

// Helper function to check if two dates are the same day
private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = Calendar.getInstance()
    val cal2 = Calendar.getInstance()
    cal1.time = date1
    cal2.time = date2
    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
           cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}

/**
 * Simplified Calendar Date Picker for quick integration
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimpleDatePickerDialog(
    isVisible: Boolean,
    selectedDate: Date = Date(),
    onDateSelected: (Date) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        androidx.compose.ui.window.Dialog(
            onDismissRequest = onDismiss
        ) {
            Card(
                modifier = modifier
                    .fillMaxWidth(0.9f)
                    .wrapContentHeight(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Select Date",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    CalendarDatePicker(
                        selectedDate = selectedDate,
                        onDateSelected = { date ->
                            onDateSelected(date)
                            onDismiss()
                        }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(onClick = onDismiss) {
                            Text("Cancel", color = Color.Gray)
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(
                            onClick = { onDismiss() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFFF9800)
                            )
                        ) {
                            Text("OK", color = Color.White)
                        }
                    }
                }
            }
        }
    }
}
