package com.noteactive.app.core.presentation.designsystem.composable.textfield

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme

@Composable
fun TransparentHintTextField(
    text: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    hintText: String? = null,
    hintColor: Color = MaterialTheme.colorScheme.outline,
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant
    ),
    maxLines: Int = Int.MAX_VALUE,
    singleLine: Boolean = false,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    trailingIcon: @Composable (() -> Unit)? = null,
    focusRequester: FocusRequester = FocusRequester(),
) {

    BasicTextField(
        value = text,
        onValueChange = onValueChange,
        modifier = modifier
            .focusRequester(focusRequester)
            .focusable(),
        textStyle = textStyle,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        maxLines = maxLines,
        singleLine = singleLine,
        decorationBox = { innerTextField ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.CenterStart
                ) {
                    if (text.isEmpty() && hintText != null) {
                        Text(text = hintText, color = hintColor, style = textStyle)
                    }
                    innerTextField()
                }
                trailingIcon?.invoke()
            }
        }
    )
}


@Composable
fun PrimaryTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    hintText: String? = null,
    isError: Boolean = false,
    errorText: String = "",
    trailingIcon: @Composable() (() -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    singleLine: Boolean = false,
    enabled: Boolean = true,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    borderColor: Color = MaterialTheme.colorScheme.outline,
    errorBorderColor: Color = MaterialTheme.colorScheme.error,
    hintColor: Color = MaterialTheme.colorScheme.outline,
    textStyle: TextStyle = MaterialTheme.typography.titleSmall.copy(
        color = MaterialTheme.colorScheme.onSurfaceVariant,
        fontSize = 14.sp
    ),
    focusRequester: FocusRequester = FocusRequester(),
) {
    val actualBorderColor = if (isError) errorBorderColor else borderColor
    val focusManager = LocalFocusManager.current
    val focusState = remember { mutableStateOf(false) }

    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier
            .focusRequester(focusRequester)
            .onFocusChanged { focusState.value = it.isFocused },
        enabled = enabled,
        textStyle = textStyle,
        keyboardActions = keyboardActions,
        keyboardOptions = keyboardOptions,
        maxLines = maxLines,
        singleLine = singleLine,
        visualTransformation = if (keyboardOptions.keyboardType == KeyboardType.Password) {
            PasswordVisualTransformation()
        } else {
            VisualTransformation.None
        },
        decorationBox = { innerTextField ->
            Column {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(43.dp)
                        .border(1.dp, actualBorderColor, RoundedCornerShape(4.dp))
                        .padding(horizontal = 12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        when {
                            value.isBlank() && hintText != null -> {
                                Text(
                                    text = hintText,
                                    color = hintColor,
                                    style = textStyle,
                                    overflow = TextOverflow.Ellipsis,
                                    maxLines = maxLines,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }

                            !focusState.value -> {
                                Text(
                                    text = value,
                                    style = textStyle,
                                    maxLines = maxLines,
                                    overflow = TextOverflow.Ellipsis,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }

                            else -> {
                                // Editing mode: use the actual text field
                                innerTextField()
                            }
                        }
                    }

                    trailingIcon?.let {
                        Box(
                            modifier = Modifier.padding(start = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            it()
                        }
                    }
                }

                if (isError) {
                    Text(
                        modifier = Modifier.padding(top = 2.dp),
                        text = errorText,
                        color = errorBorderColor,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    )
}

@Composable
fun CommentOutlinedTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    hint: String = "Write here",
    isError: Boolean = false,
    errorText: String = "",
    maxLength: Int = 100,
    textStyle: TextStyle = MaterialTheme.typography.bodyLarge,
    hintColor: Color = MaterialTheme.colorScheme.outline,
    borderColor: Color = MaterialTheme.colorScheme.outline,
    errorBorderColor: Color = MaterialTheme.colorScheme.error,
    cornerRadius: Dp = 4.dp,
    height: Dp = 132.dp,
    trailingIcon: @Composable (() -> Unit)? = null
) {
    val actualBorderColor = if (isError) errorBorderColor else borderColor
    val focusState = remember { mutableStateOf(false) }

    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(height)
                .border(
                    width = 1.dp,
                    color = actualBorderColor,
                    shape = RoundedCornerShape(cornerRadius)
                )
        ) {
            BasicTextField(
                value = value,
                onValueChange = { if (it.length <= maxLength) onValueChange(it) },
                textStyle = textStyle,
                modifier = Modifier
                    .fillMaxSize()
                    .onFocusChanged { focusState.value = it.isFocused },
                maxLines = 6,
                decorationBox = { innerTextField ->
                    Box(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        contentAlignment = Alignment.TopStart
                    ) {
                        if (value.isEmpty()) {
                            Text(
                                text = hint,
                                color = hintColor,
                                style = textStyle
                            )
                        }
                        innerTextField()
                    }
                }
            )

            trailingIcon?.let {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(4.dp)
                ) {
                    it()
                }
            }
        }
        if (isError && value.length > 100) {
            Text(
                modifier = Modifier.padding(top = 4.dp, start = 4.dp),
                text = errorText,
                color = errorBorderColor,
                style = MaterialTheme.typography.bodySmall
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = "${value.length}/$maxLength",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Black
            )
        }
    }
}



@Preview(showBackground = true)
@Composable
private fun TransparentHintTextFieldPreview() {
    MyAppTheme {
        TransparentHintTextField(
            text = "+91-8317059293",
            onValueChange = {},
            modifier = Modifier
                .padding(20.dp)
                .fillMaxWidth(),
            hintText = "Hint",
            trailingIcon = {
                Icon(
                    painter = painterResource(R.drawable.ic_dropdown),
                    contentDescription = null,
                    Modifier.clickable { })
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PrimaryTextFieldPreviewError() {
    MyAppTheme {
        PrimaryTextField(
            value = "+91-83170593afaffafafafafafafafaafafafafafafaafafafaAAAADADD",
            onValueChange = {},
            modifier = Modifier
                .padding(20.dp)
                .fillMaxWidth(),
            maxLines = 1,
            hintText = "Hint",
            isError = true,
            errorText = "Invalid Pin",
            trailingIcon = {
                Icon(
                    painter = painterResource(R.drawable.ic_dropdown),
                    contentDescription = null,
                    Modifier.clickable { })
            },
            focusRequester = FocusRequester()
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PrimaryTextFieldPreview() {
    MyAppTheme {
        TransparentHintTextField(
            text = "91-83170593",
            onValueChange = {},
            modifier = Modifier
                .padding(20.dp)
                .fillMaxWidth(),
            hintText = "Hint",
            trailingIcon = {
                Icon(
                    painter = painterResource(R.drawable.ic_dropdown),
                    contentDescription = null,
                    Modifier.clickable { })
            },
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PrimaryTextFieldPreviewHint() {
    MyAppTheme {
        PrimaryTextField(
            value = "",
            onValueChange = {},
            modifier = Modifier
                .padding(20.dp)
                .fillMaxWidth(),
            hintText = "Enter Mobile Number",
            isError = false,
            errorText = "Invalid Pin",
            trailingIcon = {
                Icon(
                    painter = painterResource(R.drawable.ic_dropdown),
                    contentDescription = null,
                    Modifier.clickable { })
            },
            focusRequester = FocusRequester()
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CommentOutlinedTextFieldPreview() {

    MyAppTheme {
        CommentOutlinedTextField(
            value = "",
            onValueChange = {  },
            trailingIcon = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_scaling),
                    contentDescription = null,
                    tint = Color.Black,
                    modifier = Modifier.size(8.dp)
                )
            },
            modifier = Modifier
                .fillMaxWidth()
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CommentOutlinedTextFieldErrorPreview() {
    val input = "abcdefghijklmnopqrstuvwxyz. ABCDEFGHIJKLMNOPQRSTUVWXYZ. abcdefghijklmnopqrstuvwxyz. ABCDEFGHIJKLMNOPQ"
        MyAppTheme {
        CommentOutlinedTextField(
            value = input,
            onValueChange = {},
            isError = input.length > 100,
            errorText = "Comment cannot exceed 100 characters",
            trailingIcon = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_scaling),
                    contentDescription = null,
                    tint = Color.Black,
                    modifier = Modifier.size(8.dp)
                )
            },
            modifier = Modifier
                .fillMaxWidth()
        )
    }
}
