package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users

import com.noteactive.app.core.util.network.ApiResponse
import javax.inject.Inject

class UserEntrySource @Inject constructor(private val userDao: UserDao) {
    suspend fun saveUserData(result: ApiResponse<List<UserEntity>>) {
        val users = result.data
        val facilities = mutableSetOf<FacilityEntity>()
        val customers = mutableSetOf<CustomerEntity>()
        val facilityRefs = mutableListOf<UserFacilityCrossRef>()
        val customerRefs = mutableListOf<UserCustomerCrossRef>()

        users?.forEach { user ->
//            user.facilities.forEach {
//                facilities.add(FacilityEntity(it))
//                facilityRefs.add(UserFacilityCrossRef(user.userId, it))
//            }

            //                    user.assignCustomerIds.forEach {
            //                        customers.add(CustomerEntity(it))
            //                        customerRefs.add(UserCustomerCrossRef(user.userId, it))
            //                    }
        }

        if (users != null) {
            userDao.insertUsers(users)
        }
        userDao.insertFacilities(facilities.toList())
        userDao.insertCustomers(customers.toList())
        userDao.insertUserFacilityCrossRefs(facilityRefs)
        userDao.insertUserCustomerCrossRefs(customerRefs)
    }



}