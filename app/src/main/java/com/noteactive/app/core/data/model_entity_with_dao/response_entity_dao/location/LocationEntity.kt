package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "location")
data class LocationEntity(
    @PrimaryKey
    val locationsId: Int,
    val locationName: String,
    val locationAddress: String,
    val locationType: String,
    val status: Int,
    val nfcLocationTag: Int,
    val nfcLocationTagRequired: Int,
    val facilitiesId: Int,
    val customerKey: String,
    val type: String
)
