package com.noteactive.app.core.data.repositoryImpl

import android.util.MalformedJsonException
import com.google.gson.JsonSyntaxException
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiResult
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.SyncResultSummary
import com.noteactive.app.core.util.preferences.AppPreferences

import com.noteactive.app.features.login.data.network.api.CommonApiService
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiSync
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiType
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.dao.ApiSyncDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.colors.ColorDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.gate_ways.GatewaysDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location.LocationDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role.UserRoleDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.sensor.SensorDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserDao
import com.noteactive.app.core.data.model_entity_with_dao.request.CommonApiRequest
import com.noteactive.app.core.domain.repository.SyncRepository
import retrofit2.Response
import kotlinx.coroutines.*

import javax.inject.Inject

class SyncRepositoryImpl @Inject constructor(
    private val appPreferences: AppPreferences,
    private val apiSync: ApiSyncDao,
    private val baseRepositoryImpl: BaseRepositoryImpl,
    private val commonApiService: CommonApiService,
    private val userDao: UserDao,
    private val colorDao: ColorDao,
    private val locationDao: LocationDao,
    private val gatewaysDao: GatewaysDao,
    private val userRoleDao: UserRoleDao,
    private val sensorDao: SensorDao,
) : SyncRepository {

    private val facilityId: String = appPreferences.singleFacilityIdToLoadFirstTimeData ?: ""

    override suspend fun syncAllFailedApis(): SyncResultSummary = withContext(Dispatchers.IO) {
        val listToSyncApis = apiSync.getAllLogs().filter { !it.success }

        val jobs = listToSyncApis.map { apiSyncEntry ->
            async {
                try {
                    performApiCall(apiSyncEntry)
                    ApiResult(
                        apiType = ApiType.valueOf(apiSyncEntry.apiType),
                        success = true,
                        page = apiSyncEntry.page
                    )
                } catch (e: JsonParseException) {
                    // Handle JSON parsing specific errors
                    ApiResult(
                        apiType = ApiType.valueOf(apiSyncEntry.apiType),
                        success = false,
                        errorMessage = "JSON parsing failed: ${e.message}",
                        page = apiSyncEntry.page
                    )
                } catch (e: JsonSyntaxException) {
                    // Handle Gson JSON syntax errors
                    ApiResult(
                        apiType = ApiType.valueOf(apiSyncEntry.apiType),
                        success = false,
                        errorMessage = "Invalid JSON format: ${e.message}",
                        page = apiSyncEntry.page
                    )
                } catch (e: MalformedJsonException) {
                    // Handle malformed JSON errors
                    ApiResult(
                        apiType = ApiType.valueOf(apiSyncEntry.apiType),
                        success = false,
                        errorMessage = "Malformed JSON: ${e.message}",
                        page = apiSyncEntry.page
                    )
                } catch (e: Exception) {
                    ApiResult(
                        apiType = ApiType.valueOf(apiSyncEntry.apiType),
                        success = false,
                        errorMessage = e.message,
                        page = apiSyncEntry.page
                    )
                }
            }
        }

        val results = jobs.awaitAll()
        val successCount = results.count { it.success }
        val failedCount = results.size - successCount

        SyncResultSummary(
            successCount = successCount,
            failedCount = failedCount,
            results = results
        )
    }

    private suspend fun performApiCall(apiSync: ApiSync) {
        val request = CommonApiRequest(
            dateAdded = "",
            deviceId = baseRepositoryImpl.getAdId(),
            facilitiesId = facilityId,
            page = null
        )

        when (val apiType = ApiType.valueOf(apiSync.apiType)) {
            ApiType.USERS, ApiType.ACTIVE_NOTE, ApiType.LOCATION, ApiType.WATCH, ApiType.SENSOR, ApiType.GATEWAYS, ApiType.TAGS -> {
                request.page = 1
                handlePaginatedApiCall(apiSync, apiType, request)
            }

            ApiType.HIGHLIGHTER, ApiType.UPDATED_INFORMATION, ApiType.ROLES, ApiType.TASKS, ApiType.COMMON, ApiType.TASK_COMMON, ApiType.COLOR -> {
                handleNonPaginatedApiCall(apiSync, apiType, request)
            }
        }
    }

    private suspend fun handlePaginatedApiCall(
        apiSync: ApiSync,
        apiType: ApiType,
        request: CommonApiRequest
    ) {
        var page = request.page
        var totalPages: Int? = null

        if (page != null) {
            while (totalPages == null || page <= totalPages) {
                try {
                    val response = when (apiType) {
                        ApiType.USERS -> initiateApiCall(
                            apiCall = { commonApiService.users(request.copy(page = page)) },
                            responseForDb = { data ->
                                try {
                                    data.data?.let { userDao.insertUsers(it) }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse users data: ${e.message}")
                                }
                            },
                            apiFailed = {
                                throw Exception("USERS API failed at page $page")
                            }
                        )

                        ApiType.LOCATION -> initiateApiCall(
                            apiCall = { commonApiService.location(request.copy(page = page)) },
                            responseForDb = { data ->
                                try {
                                    // Save location data with JSON parsing error handling
                                    data.data?.let { /* locationDao.insertItems(it) */ }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse location data: ${e.message}")
                                }
                            },
                            apiFailed = {
                                throw Exception("LOCATION API failed at page $page")
                            }
                        )

                        ApiType.GATEWAYS -> initiateApiCall(
                            apiCall = { commonApiService.gateWays(request.copy(page = page)) },
                            responseForDb = { data ->
                                try {
                                    data.data?.let { gateways -> gatewaysDao.insertItems(gateways) }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse gateways data: ${e.message}")
                                }
                            },
                            apiFailed = {
                                throw Exception("GATEWAYS API failed at page $page")
                            }
                        )

                        ApiType.SENSOR -> initiateApiCall(
                            apiCall = { commonApiService.sensor(request.copy(page = page)) },
                            responseForDb = { data ->
                                try {
                                    data.data?.let { sensors -> sensorDao.insertItems(sensors) }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse sensor data: ${e.message}")
                                }
                            },
                            apiFailed = {
                                throw Exception("SENSOR API failed at page $page")
                            }
                        )

                        else -> throw Exception("Pagination not supported for $apiType")
                    }

                    // Log progress for the current page
                    logApiProgress(
                        apiSync,
                        apiType,
                        page,
                        response != null,
                        response?.errorBody()?.string()
                    )

                    if (page == 1) {
                        when (apiType) {
                            ApiType.USERS -> {
                                try {
                                    totalPages = response?.body()?.total?.let {
                                        if (it > 300) {
                                            divideAndRoundUp(it, limit = 300)
                                        } else {
                                            1
                                        }
                                    }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse total pages for USERS: ${e.message}")
                                }
                            }

                            ApiType.LOCATION -> {
                                try {
                                    totalPages = response?.body()?.total?.let {
                                        divideAndRoundUp(it, limit = 300)
                                    }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse total pages for LOCATION: ${e.message}")
                                }
                            }

                            else -> {
                                try {
                                    totalPages = response?.body()?.total?.let {
                                        if (it > 300) {
                                            divideAndRoundUp(it, limit = 300)
                                        } else {
                                            1
                                        }
                                    }
                                } catch (e: Exception) {
                                    throw JsonParseException("Failed to parse total pages for USERS: ${e.message}")
                                }
                            }
                        }
                    }
                    page++

                } catch (e: JsonParseException) {
                    // Log JSON parsing error and continue to next page or fail completely
                    logApiProgress(
                        apiSync,
                        apiType,
                        page,
                        false,
                        "JsonParseException: ${e.message}"
                    )
                    throw e
                } catch (e: JsonSyntaxException) {
                    // Log JSON syntax error
                    logApiProgress(
                        apiSync,
                        apiType,
                        page,
                        false,
                        "JsonSyntaxException: ${e.message}"
                    )
                    throw e
                } catch (e: MalformedJsonException) {
                    // Log malformed JSON error
                    logApiProgress(
                        apiSync,
                        apiType,
                        page,
                        false,
                        "MalformedJsonException: ${e.message}"
                    )
                    throw e
                } catch (e: Exception) {
                    // Log other errors
                    logApiProgress(apiSync, apiType, page, false, "Exception: ${e.message}")
                    throw e
                }
            }
        }
    }

    private suspend fun handleNonPaginatedApiCall(
        apiSync: ApiSync,
        apiType: ApiType,
        request: CommonApiRequest
    ) {
        try {
            val response = when (apiType) {
                ApiType.ROLES -> initiateApiCall(
                    apiCall = { commonApiService.userRole(request) },
                    responseForDb = { data ->
                        try {
                            data.data?.let { userWithRole ->
                                userRoleDao.insertUserRole(userWithRole)
                            }
                        } catch (e: Exception) {
                            throw JsonParseException("Failed to parse user role data: ${e.message}")
                        }
                    },
                    apiFailed = { throw Exception("ROLES API failed") }
                )

                ApiType.COLOR -> initiateApiCall(
                    apiCall = { commonApiService.colors(request) },
                    responseForDb = { data ->
                        try {
                            data.data?.let { colorDao.insertItems(it) }
                        } catch (e: Exception) {
                            throw JsonParseException("Failed to parse color data: ${e.message}")
                        }
                    },
                    apiFailed = { throw Exception("COLOR API failed") }
                )

                else -> throw Exception("Non-paginated API not supported for $apiType")
            }

            // Log progress for the non-paginated API
            logApiProgress(
                apiSync,
                apiType,
                null,
                response != null,
                response?.errorBody()?.string()
            )

        } catch (e: JsonParseException) {
            logApiProgress(apiSync, apiType, null, false, "JsonParseException: ${e.message}")
            throw e
        } catch (e: JsonSyntaxException) {
            logApiProgress(apiSync, apiType, null, false, "JsonSyntaxException: ${e.message}")
            throw e
        } catch (e: MalformedJsonException) {
            logApiProgress(apiSync, apiType, null, false, "MalformedJsonException: ${e.message}")
            throw e
        } catch (e: Exception) {
            logApiProgress(apiSync, apiType, null, false, "Exception: ${e.message}")
            throw e
        }
    }

    private suspend fun <T> initiateApiCall(
        apiCall: suspend () -> Response<T>,
        responseForDb: (T) -> Unit,
        apiFailed: () -> Unit
    ): Response<T>? {
        return try {
            val response = apiCall()
            if (response.isSuccessful) {
                response.body()?.let {
                    try {
                        responseForDb(it)
                    } catch (e: JsonParseException) {
                        // Re-throw JSON parsing exceptions to be caught at higher level
                        throw e
                    } catch (e: JsonSyntaxException) {
                        throw e
                    } catch (e: MalformedJsonException) {
                        throw e
                    } catch (e: Exception) {
                        // Wrap other exceptions that might be JSON-related
                        throw JsonParseException("Database operation failed, possibly due to JSON parsing: ${e.message}")
                    }
                }
                response
            } else {
                // Check if error response body contains invalid JSON
                val errorBody = response.errorBody()?.string()
                if (errorBody != null && isInvalidJson(errorBody)) {
                    throw JsonParseException("Error response contains invalid JSON: $errorBody")
                }
                apiFailed()
                response
            }
        } catch (e: JsonParseException) {
            throw e
        } catch (e: JsonSyntaxException) {
            throw e
        } catch (e: MalformedJsonException) {
            throw e
        } catch (e: Exception) {
            apiFailed()
            null
        }
    }

    // Helper function to validate JSON format
    private fun isInvalidJson(jsonString: String): Boolean {
        return try {
            // You can use your preferred JSON library here (Gson, Moshi, etc.)
            // This is a simple example using basic validation
            if (jsonString.trim().isEmpty()) return true
            if (!jsonString.trim().startsWith("{") && !jsonString.trim()
                    .startsWith("[")
            ) return true

            // Add more sophisticated JSON validation if needed
            false
        } catch (e: Exception) {
            true
        }
    }

    private suspend fun logApiProgress(
        apiSyncModel: ApiSync,
        apiType: ApiType,
        page: Int?,
        success: Boolean,
        errorMessage: String?
    ) {
        page?.let {
            if (page == 1) {
                apiSync.insertStatus(
                    ApiSync(
                        id = apiSyncModel.id,
                        apiId = apiSyncModel.apiId,
                        apiType = apiType.name,
                        success = success,
                        errorMessage = errorMessage,
                        page = page,
                        lastSyncTime = System.currentTimeMillis().toString(),
                    )
                )
            } else {
                apiSync.insertStatus(
                    ApiSync(
                        apiId = apiSyncModel.apiId,
                        apiType = apiType.name,
                        success = success,
                        errorMessage = errorMessage,
                        page = page,
                        lastSyncTime = System.currentTimeMillis().toString(),
                    )
                )
            }
        } ?: run {
            apiSync.insertStatus(
                ApiSync(
                    id = apiSyncModel.id,
                    apiId = apiSyncModel.apiId,
                    apiType = apiType.name,
                    success = success,
                    errorMessage = errorMessage,
                    page = page,
                    lastSyncTime = System.currentTimeMillis().toString(),
                )
            )
        }
    }
    private fun divideAndRoundUp(totalPage: Int, limit: Int): Int {
        return (totalPage + limit - 1) / limit
    }

    // Custom exception for JSON parsing failures
    class JsonParseException(message: String) : Exception(message)

}




// pagination : {{url}}api/v1/getkeywords
//            : {{url}}api/v1/getusers
//            : {{url}}api/v1/getlocations
//            : {{url}}api/v1/getwatches
//            : {{url}}api/v1/getsensors
//            : {{url}}api/v1/getgateways

// non-pagination : {{url}}api/v1/getfacilities
//                : {{url}}api/v1/getupdatedinformations
//                : {{url}}api/v1/getcolors
//                : {{url}}api/v1/gethighlighters
//                : {{url}}api/v1/getroles
//                : {{url}}api/v1/gettasks
//                : {{url}}api/v1/getcommon
//                : {{url}}api/v1/gettaskcommon
//                : {{url}}api/v1/generatetoken

