package com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.action.CircularMenuNavigation
import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.action.CircularMenuViewModelAction
import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.state.CircularMenuState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CircularMenuViewModel @Inject constructor() : ViewModel() {

    private val _circularMenuState = MutableStateFlow(CircularMenuState())
    val circularMenuState: StateFlow<CircularMenuState> = _circularMenuState.asStateFlow()

    private val _navigationEvent = MutableStateFlow<CircularMenuNavigation?>(null)
    val navigationEvent: StateFlow<CircularMenuNavigation?> = _navigationEvent.asStateFlow()

    fun onAction(action: CircularMenuViewModelAction) {
        viewModelScope.launch {
            when (action) {
                is CircularMenuViewModelAction.OnSOSClicked -> {
                    _navigationEvent.value = CircularMenuNavigation.NavigateToSOS
                }
                is CircularMenuViewModelAction.OnCameraClicked -> {
                    _navigationEvent.value = CircularMenuNavigation.NavigateToCamera
                }
                is CircularMenuViewModelAction.OnHomeClicked -> {
                    _navigationEvent.value = CircularMenuNavigation.NavigateToHome
                }
                is CircularMenuViewModelAction.OnBarcodeClicked -> {
                    _navigationEvent.value = CircularMenuNavigation.NavigateToBarcode
                }
                is CircularMenuViewModelAction.OnIcon1Clicked -> {
                    _navigationEvent.value = CircularMenuNavigation.NavigateToIcon1
                }
                is CircularMenuViewModelAction.OnCloseClicked -> {
                    _navigationEvent.value = CircularMenuNavigation.NavigateBack
                }
            }
        }
    }

    fun clearNavigationEvent() {
        _navigationEvent.value = null
    }
}
