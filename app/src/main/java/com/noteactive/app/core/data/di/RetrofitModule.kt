package com.noteactive.app.core.data.di

import com.noteactive.app.core.data.network_with_refresh_management.AuthInterceptor
import com.noteactive.app.core.data.network_with_refresh_management.RefreshTokenService
import com.noteactive.app.core.data.network_with_refresh_management.TokenAuthenticator
import com.noteactive.app.features.login.data.network.api.CommonApiService
import com.noteactive.app.features.login.data.network.api.LicenceApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Qualifier
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    // 🔹 Clean OkHttpClient for RefreshTokenService (no TokenAuthenticator)
    @Provides
    @<PERSON>ton
    @RefreshTokenOkHttpClient
    fun provideRefreshTokenOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            })
            .build()
    }

    // 🔹 Retrofit for RefreshTokenService (uses clean client)
    @Provides
    @Singleton
    @RefreshTokenRetrofit
    fun provideRefreshTokenRetrofit(
        @RefreshTokenOkHttpClient okHttpClient: OkHttpClient
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://gdcdev.noteactive.com/newui/public/index.php/api/v1/")
            .addConverterFactory(GsonConverterFactory.create())
            .client(okHttpClient)
            .build()
    }

    @Provides
    @Singleton
    fun provideRefreshTokenServiceApi(
        @RefreshTokenRetrofit retrofit: Retrofit
    ): RefreshTokenService {
        return retrofit.create(RefreshTokenService::class.java)
    }

    // 🔹 Main OkHttpClient with AuthInterceptor + TokenAuthenticator
    @Provides
    @Singleton
    fun provideOkHttpClient(
        authInterceptor: AuthInterceptor,
        tokenAuthenticator: TokenAuthenticator
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            })
            .addInterceptor(authInterceptor)
            .authenticator(tokenAuthenticator)
            .build()
    }

    @Provides
    @Singleton
    @LicenseServerRetrofit
    fun provideLicenseServerRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://licenseserver.noteactive.com/")
            .addConverterFactory(GsonConverterFactory.create())
            .client(okHttpClient)
            .build()
    }

    @Provides
    @Singleton
    @NewUiRetrofit
    fun provideNewUiRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://gdcdev.noteactive.com/newui/public/index.php/")
            .addConverterFactory(GsonConverterFactory.create())
            .client(okHttpClient)
            .build()
    }

    @Provides
    @Singleton
    fun provideLicenseApi(@LicenseServerRetrofit retrofit: Retrofit): LicenceApiService {
        return retrofit.create(LicenceApiService::class.java)
    }

    @Provides
    @Singleton
    @NewUiRetrofit
    fun provideNewUiApiWithQualifier(@NewUiRetrofit retrofit: Retrofit): CommonApiService {
        return retrofit.create(CommonApiService::class.java)
    }

    @Provides
    @Singleton
    fun provideNewUiApi(@NewUiRetrofit retrofit: Retrofit): CommonApiService {
        return retrofit.create(CommonApiService::class.java)
    }
}

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class LicenseServerRetrofit

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NewUiRetrofit

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RefreshTokenRetrofit

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RefreshTokenOkHttpClient