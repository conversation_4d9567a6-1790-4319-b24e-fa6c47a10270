package com.noteactive.app.core.util.network



object ApiEndpoints {
    private const val BASE_WEB = "web_services.php"
    private const val BASE_API = "api/v1/"

    object Licence {
        const val LOGIN = "$BASE_WEB?type=Login"
    }

    object V1 {
        const val FACILITIES = "${BASE_API}getfacilities"
        const val USERS = "${BASE_API}getusers"
        const val LOCATIONS = "${BASE_API}getlocations"
        const val HIGHLIGHTER = "${BASE_API}gethighlighters"
        const val ROLES = "${BASE_API}getroles"
        const val GATE_WAYS = "${BASE_API}getgateways"
        const val SENSOR = "${BASE_API}getsensors"
        const val KEYWORDS = "${BASE_API}getkeywords"
        const val COMMON = "${BASE_API}getcommon"
        const val COMMON_TASK = "${BASE_API}gettaskcommon"
        const val TAGS = "${BASE_API}gettags"
        const val NOTES = "${BASE_API}getnotes"
        const val KEYWORD_WITH_QUERY = "${BASE_API}generateToke"
        const val GENERATE_TOKEN = "${BASE_API}generatetoken"
        const val TASKS = "${BASE_API}gettasks"
        const val UPDATED_INFO = "${BASE_API}getupdatedinformations"
        const val COLORS = "${BASE_API}getcolors"
    }


}

