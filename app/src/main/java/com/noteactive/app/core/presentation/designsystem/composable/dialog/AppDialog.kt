package com.noteactive.app.core.presentation.designsystem.composable.dialog

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.authCardBackground
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.composable.button.SecondaryButton
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme

@Composable
fun AppDialog(
    modifier: Modifier = Modifier,
    @StringRes titleId: Int,
    @StringRes subTitleId: Int,
    @StringRes secondaryButtonTitleId: Int = R.string.close,
    @StringRes primaryButtonTitleId: Int = R.string.time_picker_update_button_title,
    onPrimaryButtonClicked: () -> Unit,
    onSecondaryButtonClicked: () -> Unit,
) {
    Dialog(onDismissRequest = {

    }, properties = DialogProperties(dismissOnBackPress = true, dismissOnClickOutside = true)) {
        Column(
            modifier = modifier.authCardBackground().padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(titleId),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier.padding(top = 4.dp,start = 24.dp,end = 24.dp)
            )
            Text(
                text = stringResource(subTitleId),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(top = 4.dp,start = 24.dp,end = 24.dp)
            )
            Row(modifier = Modifier.padding(top = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val buttonModifier = Modifier
                    .weight(1f)
                SecondaryButton(
                    text = stringResource(secondaryButtonTitleId),
                    modifier = buttonModifier,
                ) {
                    onSecondaryButtonClicked.invoke()
                }
                PrimaryButton(
                    text = stringResource(primaryButtonTitleId),
                    modifier = buttonModifier,
                ) {
                    onPrimaryButtonClicked.invoke()

                }
            }
        }
    }
}

@PreviewScreenSizes
@Composable
private fun AppDialogPreview() {
    MyAppTheme {
        com.noteactive.app.features.common.presentation.dialog.AppDialog(
            titleId = R.string.test_dialog_title,
            subTitleId = R.string.test_dialog_subtitle,
            onPrimaryButtonClicked = {},
            onSecondaryButtonClicked = {}
        )
    }

}