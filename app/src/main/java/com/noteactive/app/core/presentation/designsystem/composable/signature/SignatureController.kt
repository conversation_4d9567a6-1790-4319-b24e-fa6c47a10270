package com.noteactive.app.core.presentation.designsystem.composable.signature

import androidx.compose.runtime.*
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.CanvasDrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection

class SignatureController {
    val paths = mutableStateListOf<Path>()

    private val currentPoints = mutableStateListOf<Offset>()
    private var canvasSize by mutableStateOf(IntSize.Zero)

    fun addPoint(offset: Offset) {
        currentPoints.add(offset)
    }

    fun endCurrentPath() {
        if (currentPoints.isNotEmpty()) {
            paths.add(currentPath)
            currentPoints.clear()
        }
    }

    fun clear() {
        paths.clear()
        currentPoints.clear()
    }

    fun updateCanvasSize(size: IntSize) {
        canvasSize = size
    }

    val currentPath: Path
        get() {
            val path = Path()
            if (currentPoints.isNotEmpty()) {
                path.moveTo(currentPoints.first().x, currentPoints.first().y)
                currentPoints.drop(1).forEach { path.lineTo(it.x, it.y) }
            }
            return path
        }

    fun toImageBitmap(): ImageBitmap {
        val width = canvasSize.width
        val height = canvasSize.height

        val imageBitmap = ImageBitmap(width, height)
        val canvas = Canvas(imageBitmap)

        val scope = CanvasDrawScope()
        scope.draw(
            density = Density(1f),
            layoutDirection = LayoutDirection.Ltr,
            canvas = canvas,
            size = Size(width.toFloat(), height.toFloat())
        ) {
            val stroke = Stroke(width = 4f)
            paths.forEach { path ->
                drawPath(path = path, color = Color.Black, style = stroke)
            }
        }

        return imageBitmap
    }
}

@Composable
fun rememberSignatureController(): SignatureController {
    return remember { SignatureController() }
}
