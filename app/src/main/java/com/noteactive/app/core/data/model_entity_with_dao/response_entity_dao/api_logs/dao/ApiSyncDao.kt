package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiSync

@Dao
interface ApiSyncDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStatus(status: ApiSync)
    @Query("DELETE FROM api_sync_logs")
    suspend fun delete()


    @Query("SELECT * FROM api_sync_logs")
    fun getAllLogs(): List<ApiSync>



}