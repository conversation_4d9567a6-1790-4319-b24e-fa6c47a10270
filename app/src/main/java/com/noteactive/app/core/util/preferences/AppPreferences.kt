package com.noteactive.app.core.util.preferences

import android.content.SharedPreferences
import androidx.core.content.edit
import javax.inject.Inject

class AppPreferences @Inject constructor(
    private var sharedPreferences: SharedPreferences
) {

    companion object {
        private const val AD_ID_KEY = "advertisement_id"
        private const val ENABLE_LOGGED_IN = "isLoggedIn"
        private const val ACCESS_TOKEN = "access_token"
        private const val SELECTED_CUSTOMER = "selected_customer_key"
        private const val SELECTED_FACILITY = "selected_facility"
        private const val SELECTED_FACILITY_ID = "FACILITY_ID_FOR_FIRST_TIME_DATA_LOAD"
    }

    fun saveAdId(adId: String) = sharedPreferences.edit { putString(AD_ID_KEY, adId) }

    fun getAdId() = sharedPreferences.getString(AD_ID_KEY, "")

    fun isLoggedIn(isLoggedIn: Boolean) =
        sharedPreferences.edit { putBoolean(ENABLE_LOGGED_IN, isLoggedIn) }

    fun isLoggedIn() = sharedPreferences.getBoolean(ENABLE_LOGGED_IN, false)

    fun saveString(key: String, value: String) = sharedPreferences.edit { putString(key, value) }

    fun getString(key: String) = sharedPreferences.getString(key, "")
    fun clearAccessToken() = sharedPreferences.edit { putString(ACCESS_TOKEN, "") }

    var accessToken: String?
        get() = sharedPreferences.getString(ACCESS_TOKEN, null)
        set(value) = sharedPreferences.edit().putString(ACCESS_TOKEN, value).apply()

    var selectedCustomer: String?
        get() = sharedPreferences.getString(SELECTED_CUSTOMER, null)
        set(value) = sharedPreferences.edit().putString(SELECTED_CUSTOMER, value).apply()

    var selectedFacility: String?
        get() = sharedPreferences.getString(SELECTED_FACILITY, null)
        set(value) = sharedPreferences.edit().putString(SELECTED_FACILITY, value).apply()

    var singleFacilityIdToLoadFirstTimeData: String?
        get() = sharedPreferences.getString(SELECTED_FACILITY_ID, null)
        set(value) = sharedPreferences.edit().putString(SELECTED_FACILITY_ID, value).apply()

}