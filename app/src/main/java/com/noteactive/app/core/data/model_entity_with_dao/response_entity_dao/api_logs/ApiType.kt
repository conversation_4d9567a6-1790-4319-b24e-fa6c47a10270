package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs

enum class ApiType(val type: String) {
    //PAGINATION APIS
    USERS("USERS"),
    ACTIVE_NOTE("ACTIVE_NOTE"), //getkeywords
    LOCATION("LOCATION"),
    WATCH("WATCH"),
    SENSOR("SENSOR"),
    GATEWAYS("GATEWAYS"),


    HIGHLIGHTER("HIGHLIGHTER"),
    UPDATED_INFORMATION("UPDATED_INFORMATION"),
    ROLES("ROLES"),
    TASKS("TASKS"),
    COMMON("COMMON"),
    COLOR("COLOR"),
    TASK_COMMON("TASK_COMMON"),
    TAGS("TAGS"),
}



// pagination : {{url}}api/v1/getkeywords
//            : {{url}}api/v1/getusers
//            : {{url}}api/v1/getlocations
//            : {{url}}api/v1/getwatches
//            : {{url}}api/v1/getsensors
//            : {{url}}api/v1/getgateways

// non-pagination : {{url}}api/v1/getupdatedinformations
//                : {{url}}api/v1/getcolors
//                : {{url}}api/v1/gethighlighters
//                : {{url}}api/v1/getroles
//                : {{url}}api/v1/gettasks
//                : {{url}}api/v1/getcommon
//                : {{url}}api/v1/gettaskcommon



//                 : {{url}}api/v1/getfacilities
//                : {{url}}api/v1/generatetoken
