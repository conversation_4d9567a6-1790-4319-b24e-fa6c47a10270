# Date & Time Picker Components

A comprehensive set of date and time picker components built with Jetpack Compose, designed to match modern UI patterns with Material 3 design principles.

## Components Overview

### 1. DateTimePickerDialog
A full-featured dialog that combines date and time selection with the ability to switch between start and end date/time.

### 2. CircularTimePicker
A circular time picker with a clock-like interface, matching the design shown in your reference image.

### 3. CalendarDatePicker
A calendar-style date picker with month navigation and date selection.

### 4. SimpleDatePickerDialog
A simplified date picker dialog for quick date selection.

## Features

✅ **Material 3 Design** - Modern, clean interface  
✅ **Start/End Date Selection** - Toggle between start and end dates  
✅ **Circular Time Picker** - Intuitive clock-like time selection  
✅ **Calendar Date Picker** - Month-based date selection  
✅ **Customizable Styling** - Orange accent colors and clean typography  
✅ **Touch Interactions** - Drag-to-select time, tap-to-select dates  
✅ **Responsive Design** - Works on different screen sizes  

## Usage Examples

### Basic DateTime Picker Dialog

```kotlin
@Composable
fun MyScreen() {
    var showPicker by remember { mutableStateOf(false) }
    var selection by remember { mutableStateOf(DateTimeSelection()) }
    
    But<PERSON>(onClick = { showPicker = true }) {
        Text("Select Date & Time")
    }
    
    DateTimePickerDialog(
        isVisible = showPicker,
        initialSelection = selection,
        onDismiss = { showPicker = false },
        onConfirm = { newSelection ->
            selection = newSelection
            showPicker = false
        }
    )
}
```

### Simple Date Picker

```kotlin
@Composable
fun DatePickerExample() {
    var showDatePicker by remember { mutableStateOf(false) }
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    
    Button(onClick = { showDatePicker = true }) {
        Text("Select Date")
    }
    
    SimpleDatePickerDialog(
        isVisible = showDatePicker,
        selectedDate = selectedDate,
        onDateSelected = { date ->
            selectedDate = date
        },
        onDismiss = { showDatePicker = false }
    )
}
```

### Standalone Time Picker

```kotlin
@Composable
fun TimePickerExample() {
    var selectedTime by remember { mutableStateOf(LocalTime.now()) }
    
    CircularTimePicker(
        selectedTime = selectedTime,
        onTimeSelected = { time ->
            selectedTime = time
        }
    )
}
```

### Standalone Calendar

```kotlin
@Composable
fun CalendarExample() {
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    
    CalendarDatePicker(
        selectedDate = selectedDate,
        onDateSelected = { date ->
            selectedDate = date
        }
    )
}
```

## Data Classes

### DateTimeSelection
```kotlin
data class DateTimeSelection(
    val startDate: LocalDate = LocalDate.now(),
    val startTime: LocalTime = LocalTime.now(),
    val endDate: LocalDate = LocalDate.now(),
    val endTime: LocalTime = LocalTime.now().plusHours(1)
)
```

### DateTimeMode
```kotlin
enum class DateTimeMode {
    START, END
}
```

## Customization

### Colors
The components use these primary colors:
- **Orange Accent**: `Color(0xFFFF9800)` - For selections and primary actions
- **Purple Accent**: `Color(0xFF6200EA)` - For time display highlighting
- **Background**: `Color.White` - Dialog and card backgrounds
- **Text**: `Color.Black` - Primary text color
- **Secondary**: `Color.Gray` - Secondary text and borders

### Styling
You can customize the appearance by modifying:
- Button colors in the action sections
- Background colors in Card components
- Text colors and font weights
- Border colors and shapes

## File Structure

```
datetime/
├── DateTimePickerDialog.kt      # Main dialog component
├── CircularTimePicker.kt        # Circular time picker
├── CalendarDatePicker.kt        # Calendar date picker
├── DateTimePickerDemo.kt        # Demo and usage examples
└── README.md                    # This documentation
```

## Integration

1. **Add to your project**: Copy the `datetime` package to your `core/presentation/designsystem/composable/` directory

2. **Import components**:
```kotlin
import com.noteactive.app.core.presentation.designsystem.composable.datetime.*
```

3. **Use in your screens**: Follow the usage examples above

## Demo Screen

Run `DateTimePickerDemoScreen` to see all components in action:

```kotlin
@Composable
fun MyApp() {
    DateTimePickerDemoScreen()
}
```

## Requirements

- **Jetpack Compose** - Latest stable version
- **Material 3** - For design components
- **Java 8+ Time API** - For LocalDate and LocalTime

## Notes

- The circular time picker uses drag gestures for time selection
- Calendar picker supports month navigation with arrow buttons
- All dialogs are dismissible by tapping outside or using the back button
- Components are designed to be responsive and work on different screen sizes

## Future Enhancements

- [ ] 24-hour vs 12-hour time format toggle
- [ ] Date range selection
- [ ] Custom color themes
- [ ] Accessibility improvements
- [ ] Animation enhancements
- [ ] Localization support
