package com.noteactive.app.core.presentation.designsystem.composable.camera

import android.annotation.SuppressLint
import android.net.Uri
import android.util.Log
import androidx.annotation.OptIn
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import java.io.File

@OptIn(ExperimentalGetImage::class)
@SuppressLint("ResourceType")
@Composable
fun AutoCaptureCameraXPopup(
    onImageCaptured: (Uri) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    val imageCapture = remember { ImageCapture.Builder().build() }
    val executor = remember { ContextCompat.getMainExecutor(context) }

    var alreadyCaptured = remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .background(Color.Black, RoundedCornerShape(12.dp))
        ) {
            AndroidView(
                factory = { ctx ->
                    val previewView = PreviewView(ctx)
                    val cameraProviderFuture = ProcessCameraProvider.getInstance(ctx)

                    cameraProviderFuture.addListener({
                        val cameraProvider = cameraProviderFuture.get()

                        val preview = Preview.Builder().build().apply {
                            setSurfaceProvider(previewView.surfaceProvider)
                        }

                            val imageAnalyzer = ImageAnalysis.Builder()
                            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                            .build()

                        imageAnalyzer.setAnalyzer(executor) { imageProxy ->

                            val mediaImage = imageProxy.image
                            if (mediaImage != null) {
                                val inputImage = InputImage.fromMediaImage(
                                    mediaImage,
                                    imageProxy.imageInfo.rotationDegrees
                                )



                                val detector = FaceDetection.getClient()
                                detector.process(inputImage)
                                    .addOnSuccessListener { faces ->
                                        if (faces.isNotEmpty() && !alreadyCaptured.value) {
                                            alreadyCaptured.value = true

                                            val photoFile = File(
                                                context.getExternalFilesDir(null),
                                                "${System.currentTimeMillis()}.jpg"
                                            )
                                            val outputOptions =
                                                ImageCapture.OutputFileOptions.Builder(photoFile)
                                                    .build()

                                            imageCapture.takePicture(
                                                outputOptions,
                                                executor,
                                                object : ImageCapture.OnImageSavedCallback {
                                                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                                                        onImageCaptured(Uri.fromFile(photoFile))
                                                        onDismiss()
                                                    }

                                                    override fun onError(exc: ImageCaptureException) {
                                                        Log.e("CameraX", "Auto-capture failed", exc)
                                                    }
                                                }
                                            )
                                        }
                                    }
                                    .addOnFailureListener {
                                        Log.e("FaceDetect", "Failed: ${it.message}")
                                    }
                                    .addOnCompleteListener {
                                        imageProxy.close()
                                    }
                            } else {
                                imageProxy.close()
                            }
                        }

                        val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA

                        try {
                            cameraProvider.unbindAll()
                            cameraProvider.bindToLifecycle(
                                lifecycleOwner,
                                cameraSelector,
                                preview,
                                imageCapture,
                                imageAnalyzer
                            )
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }, executor)

                    previewView
                },
                modifier = Modifier.fillMaxSize()
            )

            // Optional manual capture (kept for testing or UI fallback)
            MultiButtonBottomRow(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .align(Alignment.BottomCenter),
                secondaryButtonTitle = R.string.cancel,
                primaryButtonTitle = R.string.save,
                secondaryButtonClickListener = {
                    onDismiss()
                },
                primaryButtonClickListener = {
                    if (!alreadyCaptured.value) {
                        alreadyCaptured.value = true
                        val photoFile = File(
                            context.getExternalFilesDir(null),
                            "${System.currentTimeMillis()}.jpg"
                        )
                        val outputOptions =
                            ImageCapture.OutputFileOptions.Builder(photoFile).build()

                        imageCapture.takePicture(
                            outputOptions,
                            executor,
                            object : ImageCapture.OnImageSavedCallback {
                                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                                    onImageCaptured(Uri.fromFile(photoFile))
                                    onDismiss()
                                }

                                override fun onError(exc: ImageCaptureException) {
                                    Log.e("CameraX", "Capture failed", exc)
                                }
                            }
                        )
                    }
                },
                isPrimaryButtonEnabled = true
            )
        }
    }
}
