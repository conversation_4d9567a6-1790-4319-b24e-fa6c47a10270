package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction

@Dao
interface UserDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUsers(users: List<UserEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFacilities(facilities: List<FacilityEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomers(customers: List<CustomerEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserFacilityCrossRefs(refs: List<UserFacilityCrossRef>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserCustomerCrossRefs(refs: List<UserCustomerCrossRef>)

    @Transaction
    @Query("SELECT * FROM users WHERE userId = :id")
    suspend fun getUserWithFacilities(id: Int): UserWithFacilities

    @Transaction
    @Query("SELECT * FROM users WHERE userId = :id")
    suspend fun getUserWithCustomers(id: Int): UserWithCustomers

    @Transaction
    @Query("SELECT * FROM users WHERE userPin = :pin")
    suspend fun getUserWithPin(pin: String): UserEntity?

}
