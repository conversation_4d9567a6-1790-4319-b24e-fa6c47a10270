package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao

import androidx.room.withTransaction
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.CustomerInfo
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.FacilityApiResponse
import com.noteactive.app.core.data.db.AppDatabase
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.mapper.toDomainList
import javax.inject.Inject

class FacilityDataSaveSource @Inject constructor(
    private val facilityDao: FacilityDao,
    private val apiUrlDao: ApiUrlDao,
    private val customerInfoDao: CustomerInfoDao,
    private val database: AppDatabase,

    ) {
    suspend fun saveFacilityData(facilityResponse: List<FacilityApiResponse>) {
        database.withTransaction {
            facilityResponse.forEachIndexed { index, facilityApiResponse ->
                facilityApiResponse.facilities?.let {
                    facilityDao.insert(it)
                }
                facilityApiResponse.apiurls?.let {
                    apiUrlDao.insert(it)
                }
                customerInfoDao.insert(
                    CustomerInfo(
                        companyName = facilityApiResponse.companyName,
                        customerKey = facilityApiResponse.customerKey,
                        settingData = facilityApiResponse.settingData,
                        activecustomerId = facilityApiResponse.activecustomerId,
                        clientInfoNotes = facilityApiResponse.clientInfoNotes,
                        isSelected = false
                    )
                )
            }
        }
    }

    fun getAllFacility(customerKey: String) = facilityDao.getAllFacility(customerKey).toDomainList()

    suspend fun getAllCustomer() =
        customerInfoDao.getAllCustomer().toDomainList()
    suspend fun selectCustomer(customerKey: String) = customerInfoDao.updateSelectedCustomer(customerKey)
}
