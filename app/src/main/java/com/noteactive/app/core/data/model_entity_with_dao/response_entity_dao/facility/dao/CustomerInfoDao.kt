package com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Transaction
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.CustomerInfo
import com.noteactive.app.core.util.base.BaseDao

@Dao
interface CustomerInfoDao : BaseDao<CustomerInfo> {
    @Query("DELETE FROM customer_info")
    suspend fun deleteAll()

    @Query("SELECT companyName,customerKey,isSelected FROM customer_info ")
    suspend fun getAllCustomer(): List<CustomerInfo>


    @Query("UPDATE customer_info SET isSelected = 0")
    fun resetAllSelections()

    @Query("UPDATE customer_info SET isSelected = 1 WHERE customerKey = :customerKey")
    fun selectCustomer(customerKey: String?)

    @Transaction
    fun updateSelectedCustomer(customerKey: String?) {
        resetAllSelections()
        selectCustomer(customerKey)
    }

}