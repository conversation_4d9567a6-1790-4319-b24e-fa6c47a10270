package com.noteactive.app.core.presentation.designsystem.composable.toolbar

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.noteactive.app.R

@Composable
fun AppCommonToolbar(
    modifier: Modifier = Modifier,
    title: String,
    backIcon: Int = R.drawable.ic_back,
    endIcon: Int? = null,
    onBackIconClicked: () -> Unit,
    onEndIconClicked: (() -> Unit?)? = null,
) {
    TopAppBar(
        modifier = modifier.shadow(20.dp),
        title = {
            Text(text = title, style = MaterialTheme.typography.labelLarge.copy(color = Color.Black ))
        },
        navigationIcon = {
            Icon(
                painter = painterResource(backIcon),
                contentDescription = null,
                modifier = Modifier
                    .clickable { onBackIconClicked.invoke() }
                    .padding(horizontal = 16.dp),
                tint = Color.Black
            )
        },
        actions = {
            endIcon?.let {
                Icon(
                    painter = painterResource(id = endIcon), // your right icon here
                    contentDescription = "End Icon",
                    modifier = Modifier
                        .clickable { onEndIconClicked?.invoke() }
                        .padding(horizontal = 16.dp)
                )
            }

        }
    )
}


@Preview
@Composable
fun AppCommonToolbarPreview() {
    AppCommonToolbar(
        modifier = Modifier,
        title = "Select Facility",
        endIcon = R.drawable.ic_search,
        onBackIconClicked = {

        },
        onEndIconClicked = {

        })
}
