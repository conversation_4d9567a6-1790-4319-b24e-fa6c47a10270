package com.noteactive.app.navigation.navgraph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.noteactive.app.navigation.bottombar_nav.MainHomeAppNav
import com.noteactive.app.navigation.Screens

fun NavGraphBuilder.mainNavGraph(navController: NavHostController) {
    navigation(startDestination = Screens.Notes.route, route = Screens.MainGraph.route) {
        composable(Screens.Notes.route) {
            MainHomeAppNav(navController)
        }
    }
}
