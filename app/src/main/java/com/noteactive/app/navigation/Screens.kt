package com.noteactive.app.navigation

sealed class Screens(val route: String) {
    // AUTH SCREEN NAV_GRAPH

    object AuthGraph : Screens("auth")
    object EnterActivationKeyScreen : Screens("EnterActivationKeyScreen")
    object LoginScreen : Screens("LoginScreen")
    object ActivationDetailScreen : Screens("ActivationDetailScreen")
    object SelectCustomerScreen : Screens("SelectCustomerScreen")
    object SelectFacilityScreen {
        const val route = "selectFacility"
        fun passCustomerKey(customerKey: String) = "$route/$customerKey"
    }


    // HOME SCREEN NAV_GRAPH
    object MainGraph : Screens("main")
    object Notes : Screens("notes")
    object Attachments : Screens("attachments")
    object Offenders : Screens("offenders")
    object OffendersDetails : Screens("OffendersDetails")
    object Tasks : Screens("tasks")
    object More : Screens("more")


    // GLOBAL SCREEN NAV_GRAPH
    object DateRangePickerScreen : Screens("DateRangePickerScreen")
    object TimePickerScreen : Screens("TimePickerScreen")
    object FilePickerScreen : Screens("TimePickerScreen")


    // Create Note Graph
    object CreateNoteScreenGraph : Screens("CreateNoteScreenGraph")
    object CreateNoteScreen : Screens("CreateNoteScreen")
    object SelectActiveNoteScreen : Screens("SelectActiveNoteScreen")
    object FloatingOptionsScreen : Screens("FloatingOptionsScreen")
    object NoteSelectFacilityScreen : Screens("NoteSelectFacilityScreen")
    object NoteSelectLocationScreen : Screens("NoteSelectLocationScreen")
    object NoteSelectUserScreen : Screens("NoteSelectUserScreen")
    object UpdateDateAndTimeScreen : Screens("UpdateDateAndTimeScreen")
    object NoteSelectOffenderScreen : Screens("NoteSelectOffenderScreen")
    object AdvanceSearchScreen : Screens("AdvanceSearchScreen")




}