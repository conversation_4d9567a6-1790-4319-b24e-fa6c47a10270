package com.noteactive.app.navigation.navgraph

import androidx.compose.ui.window.Dialog
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import com.aki.notesapp.feature.filepicker.action.FilePickerNavigationAction
import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.screens.FloatingButtonPanel
import com.noteactive.app.features.common.domain.state.DateRangeSelected
import com.noteactive.app.features.common.presentation.date_picker.DatePickerScreen
import com.noteactive.app.features.common.presentation.date_picker.actions.DateRangeSelectionNavigationAction
import com.noteactive.app.features.common.presentation.filepicker.FilePickerScreen
import com.noteactive.app.features.common.presentation.time_picker.TimePickerStateRoot
import com.noteactive.app.features.home.inmate.inmate_details_screen.presentation.InmateDetailsScreenRoot
import com.noteactive.app.features.home.shownotes.advance_search.presentation.SearchNotesScreenRoot
import com.noteactive.app.navigation.Screens

fun NavGraphBuilder.globalNavGraph(navController: NavHostController) {

    composable(Screens.DateRangePickerScreen.route) {
        DatePickerScreen(isPickRange = false) { action ->
            when (action) {
                is DateRangeSelectionNavigationAction.OnDateSelectedComplete -> {
                    navController.previousBackStackEntry?.savedStateHandle?.set(
                        "datePick", DateRangeSelected(action.start, action.end)
                    )
                    navController.popBackStack()
                }

                DateRangeSelectionNavigationAction.OnDismiss -> {
                    navController.navigateUp()
                }

                else -> Unit
            }
        }
    }

    composable(Screens.TimePickerScreen.route) {
        TimePickerStateRoot {

        }
    }
    composable(Screens.FloatingOptionsScreen.route) {
        Dialog(onDismissRequest = {}) {
            FloatingButtonPanel(
                onSosClick = {},
                onCameraClick = {},
                onHomeClick = { /*...*/ },
                onBarcodeClick = { /*...*/ },
                onProfileClick = { /*...*/ },
                onCloseClick = {}
            )
        }
    }
    composable(Screens.Attachments.route) {
        FilePickerScreen { filePickerAction ->
            when (filePickerAction) {
                FilePickerNavigationAction.OnBackPressed -> {
                    navController.navigateUp()
                }

                is FilePickerNavigationAction.OnSubmit -> {
                    if (filePickerAction.listOfFile.isNotEmpty()) {
                        navController.previousBackStackEntry
                            ?.savedStateHandle
                            ?.set("picked_attachments", filePickerAction.listOfFile)
                        navController.popBackStack()
                    }
                }
            }

        }
    }

    composable(Screens.AdvanceSearchScreen.route) { SearchNotesScreenRoot() }
    composable(Screens.OffendersDetails.route) {
        InmateDetailsScreenRoot()
    }
}
