package com.noteactive.app.navigation.navgraph

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.aki.notesapp.feature.filepicker.action.FilePickerNavigationAction
import com.noteactive.app.features.common.presentation.date_picker.DatePickerScreen
import com.noteactive.app.features.common.presentation.date_picker.actions.DateRangeSelectionNavigationAction
import com.noteactive.app.features.common.presentation.filepicker.FilePickerScreen
import com.noteactive.app.features.common.presentation.time_picker.TimePickerStateRoot
import com.noteactive.app.features.home.createnote.create_note_screen.CreateNewScreenViewModel
import com.noteactive.app.features.home.createnote.create_note_screen.CreateNoteScreenRoot
import com.noteactive.app.features.home.createnote.create_note_screen.action.CreateNoteNavigationAction
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_popup_options.AddNoteActionEnum
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_popup_options.NotePopupOptionsActions
import com.noteactive.app.features.home.createnote.select_active_note_screen.presentation.SelectActiveNoteScreen
import com.noteactive.app.features.home.createnote.select_facility.NoteSelectFacilityScreenRoot
import com.noteactive.app.features.home.createnote.select_facility.action.NoteSelectFacilityNavigationAction
import com.noteactive.app.features.home.createnote.select_location.NoteSelectLocationScreenRoot
import com.noteactive.app.features.home.createnote.select_location.action.NoteSelectLocationNavigationAction
import com.noteactive.app.features.home.createnote.select_offenders_screen.presentation.SelectOffenderScreen
import com.noteactive.app.features.home.createnote.select_user.NoteSelectUserScreenRoot
import com.noteactive.app.features.home.createnote.select_user.action.NoteSelectUserNavigationAction
import com.noteactive.app.features.home.createnote.update_date_time_screen.presentation.UpdateDateAndTimeScreenRoot
import com.noteactive.app.navigation.Screens


fun NavGraphBuilder.createNoteNavGraph(
    navController: NavHostController
) {
    navigation(
        startDestination = Screens.CreateNoteScreen.route,
        route = Screens.CreateNoteScreenGraph.route
    ) {

        composable(Screens.CreateNoteScreen.route) { navBackStackEntry ->
            val viewModel = navBackStackEntry.sharedCreateNoteViewModel(navController)
            CreateNoteScreenRoot(
                viewModel = viewModel,
                navigationAction = { action ->
                    when (action) {
                        is CreateNoteNavigationAction.FilePicker ->
                            navController.navigate(Screens.Attachments.route)
                    }
                },
                navigationPopupAction = { action ->
                    when (action) {
                        is NotePopupOptionsActions.NotePopupAction -> {
                            when (action.actionEnum) {
                                AddNoteActionEnum.ACTIVE_NOTE -> {
                                    navController.navigate(Screens.SelectActiveNoteScreen.route)
                                }

                                AddNoteActionEnum.OFFENDERS -> {
                                    navController.navigate(Screens.NoteSelectOffenderScreen.route)
                                }

                                AddNoteActionEnum.LOCATION -> {
                                    navController.navigate(Screens.NoteSelectFacilityScreen.route)
                                }

                                AddNoteActionEnum.BUILDING -> {
                                    navController.navigate(Screens.NoteSelectLocationScreen.route)
                                }

                                AddNoteActionEnum.ATTACH -> {
                                    navController.navigate(Screens.Attachments.route)
                                }

                                AddNoteActionEnum.FORM -> {}
                                AddNoteActionEnum.NEXT -> {}
                                AddNoteActionEnum.PREVIOUS -> {}
                                AddNoteActionEnum.CAMERA -> {}
                                AddNoteActionEnum.MIC -> {}
                                AddNoteActionEnum.EDIT -> {}
                                AddNoteActionEnum.TEXT -> {}
                                AddNoteActionEnum.USER -> {
                                    navController.navigate(Screens.NoteSelectUserScreen.route)

                                }

                                AddNoteActionEnum.STRIKE -> {}
                                AddNoteActionEnum.NONE -> {
                                    TODO()
                                }
                            }
                        }
                    }
                }
            )
//            FillInfoScreen()
        }

        composable(Screens.DateRangePickerScreen.route) { navBackStackEntry ->
            val viewModel = navBackStackEntry.sharedCreateNoteViewModel(navController)

            DatePickerScreen(
                isPickRange = false,
            ) { action ->
                when (action) {
                    is DateRangeSelectionNavigationAction.OnDateSelectedComplete -> {
//                        viewModel.updateSelectedDate(action.start, action.end)
                        navController.popBackStack()
                    }

                    DateRangeSelectionNavigationAction.OnDismiss -> navController.navigateUp()
                    else -> Unit
                }

            }
        }

        // 🔵 Time Picker - UPDATES SHARED VIEWMODEL ON SELECTION
        composable(Screens.TimePickerScreen.route) { navBackStackEntry ->
            val viewModel = navBackStackEntry.sharedCreateNoteViewModel(navController)

            TimePickerStateRoot(
            ) { selectedTime ->
                // 🎯 UPDATE SHARED VIEWMODEL
//                viewModel.updateSelectedTime(selectedTime)
                navController.popBackStack()
            }
        }

        composable(Screens.SelectActiveNoteScreen.route) { navBackStackEntry ->
            val viewModel = navBackStackEntry.sharedCreateNoteViewModel(navController)
            SelectActiveNoteScreen{

            }
        }

        composable(Screens.NoteSelectFacilityScreen.route) { navBackStackEntry ->
            val sharedViewModel = navBackStackEntry.sharedCreateNoteViewModel(navController)

            NoteSelectFacilityScreenRoot { navAction ->
                when (navAction) {

                    NoteSelectFacilityNavigationAction.OnBackPressed -> {
                        navController.navigateUp()
                    }

                    is NoteSelectFacilityNavigationAction.OnSelectFacilityAction -> {
                        sharedViewModel.addSelectedFacilities(navAction.facilities.toMutableList())
                        navController.popBackStack()

                    }
                }
            }
        }

        composable(Screens.NoteSelectLocationScreen.route) { navBackStackEntry ->
            val sharedViewModel = navBackStackEntry.sharedCreateNoteViewModel(navController)
            NoteSelectLocationScreenRoot { navActions ->
                when (navActions) {
                    NoteSelectLocationNavigationAction.OnBackPressed -> {
                        navController.navigateUp()
                    }

                    is NoteSelectLocationNavigationAction.OnSelectLocationAction -> {
                        sharedViewModel.addSelectedLocation(navActions.location.toMutableList())
                        navController.popBackStack()

                    }
                }

            }
        }

        composable(Screens.NoteSelectUserScreen.route) { navBackStackEntry ->
            val parentEntry = remember(navBackStackEntry) {
                navController.getBackStackEntry(Screens.CreateNoteScreenGraph.route)
            }
            val sharedViewModel: CreateNewScreenViewModel = hiltViewModel(parentEntry)

            NoteSelectUserScreenRoot { navActions ->
                when (navActions) {
                    NoteSelectUserNavigationAction.OnBackPressed -> {
                        navController.navigateUp()
                    }

                    is NoteSelectUserNavigationAction.OnSelectUserAction -> {
                        sharedViewModel.addSelectedUsers(navActions.users.toMutableList())
                        navController.popBackStack()

                    }
                }
            }
        }
        composable(Screens.NoteSelectOffenderScreen.route) { navBackStackEntry ->
            val parentEntry = remember(navBackStackEntry) {
                navController.getBackStackEntry(Screens.CreateNoteScreenGraph.route)
            }
            val sharedViewModel: CreateNewScreenViewModel = hiltViewModel(parentEntry)
            SelectOffenderScreen(
                onBackPressed = { navController.navigateUp() },
                selectedOffenders = { offenders ->
                    sharedViewModel.addOffenders(offenders)
                    navController.popBackStack()
                }
            )
        }

        composable(Screens.UpdateDateAndTimeScreen.route){
            UpdateDateAndTimeScreenRoot()
        }

        // 🔵 Attachment Selector
        composable(Screens.Attachments.route) {
            FilePickerScreen { filePickerAction ->
                when (filePickerAction) {
                    FilePickerNavigationAction.OnBackPressed -> {
                        navController.navigateUp()
                    }

                    is FilePickerNavigationAction.OnSubmit -> {
                        if (filePickerAction.listOfFile.isNotEmpty()) {
                            navController.popBackStack()
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun NavBackStackEntry.sharedCreateNoteViewModel(navController: NavHostController): CreateNewScreenViewModel {
    val parentEntry = remember(this) {
        navController.getBackStackEntry(Screens.CreateNoteScreenGraph.route)
    }
    return hiltViewModel(parentEntry)
}