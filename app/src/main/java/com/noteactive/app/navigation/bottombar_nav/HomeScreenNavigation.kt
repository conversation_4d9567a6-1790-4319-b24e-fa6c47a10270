package com.noteactive.app.navigation.bottombar_nav

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.noteactive.app.features.home.bottom_nav.CustomBottomBarWithFab
import com.noteactive.app.features.home.inmate.inmate_details_screen.presentation.InmateDetailsScreenRoot
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.InmateScreenRoot
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenNavigationAction
import com.noteactive.app.features.home.shownotes.notes.presentation.NotesScreenRoot
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenNavigationAction
import com.noteactive.app.navigation.Screens


@Composable
fun MainHomeAppNav(rootNavController: NavController) {
    val tabNavController = rememberNavController()
    val currentRoute = tabNavController.currentBackStackEntryAsState().value?.destination?.route

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .consumeWindowInsets(WindowInsets.navigationBars) // Consume system insets
            .padding(WindowInsets.navigationBars.asPaddingValues()),

        bottomBar = {
            CustomBottomBarWithFab(
                navController = tabNavController,
                currentRoute = currentRoute ?: Screens.Notes.route,
                onFabClick = {
                    rootNavController.navigate(Screens.CreateNoteScreen.route)
//                    rootNavController.navigate(Screens.FloatingOptionsScreen.route)
                },
                modifier = Modifier
                    .navigationBarsPadding() // <- Important for tablets & gesture nav
                    .imePadding()             // <- Important when keyboard is open
            )
        },

        floatingActionButton = {},

        ) { innerPadding ->
        NavHost(
            navController = tabNavController,
            startDestination = Screens.Notes.route,
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            composable(Screens.Notes.route) {
                NotesScreenRoot(navController = rootNavController) { action ->
                    when (action) {
                        NotesScreenNavigationAction.OnSelectDate,
                        NotesScreenNavigationAction.OnSelectDateRange -> {
                            rootNavController.navigate(Screens.DateRangePickerScreen.route)
                        }

                        NotesScreenNavigationAction.OnSelectUpdateTime -> {
                            rootNavController.navigate(Screens.TimePickerScreen.route)
                        }

                        NotesScreenNavigationAction.AdvanceSearchClicked,
                        NotesScreenNavigationAction.OnFilterClicked -> {
                            rootNavController.navigate(Screens.AdvanceSearchScreen.route)
                        }

                        else -> {}
                    }
                }
            }

            composable(Screens.Offenders.route) {
                InmateScreenRoot { action ->
                    when (action) {
                        is InmateScreenNavigationAction.OnInmateDetailsClicked -> {
                            rootNavController.navigate(Screens.OffendersDetails.route)

                        }
                    }

                }
            }

            composable(Screens.OffendersDetails.route) {
                InmateDetailsScreenRoot()
            }
            composable(Screens.Tasks.route) { Text("Tasks") }
            composable(Screens.More.route) { Text("More") }
        }
    }
}


