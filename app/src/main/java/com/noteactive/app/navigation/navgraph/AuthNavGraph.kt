package com.noteactive.app.navigation.navgraph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.ActivationDetailScreenRoot
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsNavigationAction
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.EnterActivationKeyScreen
import com.noteactive.app.features.login.presentation.screens.login_screen.LoginScreenRoot
import com.noteactive.app.features.login.presentation.screens.login_screen.action.LoginScreenNavigation
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectCustomerScreenRoot
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.SelectFacilityScreenRoot
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectCustomerNavigationAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityNavigationAction
import com.noteactive.app.navigation.Screens

fun NavGraphBuilder.authNavGraph(
    navController: NavHostController,
    startDestination: String
) {
    navigation(
        startDestination = startDestination,
        route = Screens.AuthGraph.route
    ) {
        composable(Screens.EnterActivationKeyScreen.route) {
            EnterActivationKeyScreen {
                navController.navigate(Screens.ActivationDetailScreen.route) {
                    popUpTo(Screens.EnterActivationKeyScreen.route) {
                        inclusive = true
                    }
                    launchSingleTop = true
                }
            }

        }

        composable(Screens.ActivationDetailScreen.route) {
            ActivationDetailScreenRoot { navAction ->
                when (navAction) {
                    ActivationDetailsNavigationAction.GoToLoginScreen -> {
                        navController.navigate(Screens.LoginScreen.route) {
                            launchSingleTop = true
                        }
                    }

                    ActivationDetailsNavigationAction.OnDismiss -> {
                        navController.popBackStack()
                    }
                }

            }
        }

        composable(
            route = Screens.SelectFacilityScreen.route + "/{customerKey}",
            arguments = listOf(navArgument("customerKey") { type = NavType.StringType })
        ) { backStackEntry ->
            val customerKey = backStackEntry.arguments?.getString("customerKey") ?: ""
            SelectFacilityScreenRoot(customerKey = customerKey) {
                if (it is SelectFacilityNavigationAction.NavigateBackWithFacility) {
                    navController.previousBackStackEntry?.savedStateHandle?.set(
                        "facilityResult", it.facility.facility
                    )
                    navController.popBackStack()
                }
            }
        }

        composable(Screens.SelectCustomerScreen.route) {
            SelectCustomerScreenRoot  {
                if (it is SelectCustomerNavigationAction.NavigateBackWithCustomer) {
                    navController.previousBackStackEntry?.savedStateHandle?.apply {
                        set("customerResult", it.facility.customerName)
                        set("customerKey", it.facility.customerKey) // assuming this was a mistake in your original code
                    }
                    navController.popBackStack()
                }
            }
        }

        composable(Screens.LoginScreen.route) {
            LoginScreenRoot(navController = navController) { navAction ->
                when (navAction) {
                    LoginScreenNavigation.HomeScreenNav -> {
                        navController.navigate(Screens.MainGraph.route) {
                            popUpTo(Screens.AuthGraph.route) { inclusive = true }
                        }
                    }

                    LoginScreenNavigation.SelectCustomerScreenNav -> {
                        navController.navigate(Screens.SelectCustomerScreen.route)
                    }

                    is LoginScreenNavigation.SelectFacilityScreenNav -> {
                        navController.navigate(Screens.SelectFacilityScreen.passCustomerKey(navAction.customerKey))
                    }
                }
            }
        }
    }
}
