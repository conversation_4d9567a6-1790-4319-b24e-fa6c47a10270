package com.noteactive.app.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import com.noteactive.app.navigation.navgraph.authNavGraph
import com.noteactive.app.navigation.navgraph.createNoteNavGraph
import com.noteactive.app.navigation.navgraph.globalNavGraph
import com.noteactive.app.navigation.navgraph.mainNavGraph

@Composable
fun AppNavigation(navController: NavHostController, modifier: Modifier = Modifier,startDestination: String = Screens.EnterActivationKeyScreen.route) {
    NavHost(
        navController = navController,
        startDestination = Screens.AuthGraph.route,
        modifier = modifier
    ) {
        // Auth Flow
        authNavGraph(navController,startDestination)

        // Main App Flow
        mainNavGraph(navController)

        // Global Screens
        globalNavGraph(navController)

        //Create Note Graph
        createNoteNavGraph(navController)
    }
}

