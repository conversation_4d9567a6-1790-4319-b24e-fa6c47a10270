package com.noteactive.app.features.home.inmate.inmate_details_screen.presentation

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AssistChip
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.theme.DarkColorPalette
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextSubTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.LightColorPalette
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.home.inmate.inmate_details_screen.action.InmateDetailsScreenViewModelAction
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.classification.ClassificationTabContent
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.document.DocTabContent
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.personalInfo.PersonalInfoTabContent
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.stickyNotes.StickyNotesTabContent
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.timeline.TimelineTabContent
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.InmateDetailsState
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.RowTabItem

@Composable
fun InmateDetailsScreenRoot() {
    val viewModel: InmateDetailsScreenViewModel = hiltViewModel()
    val inmateDetailsState by viewModel.inmateDetailsState.collectAsStateWithLifecycle()
    InmateDetailsScreen(inmateDetailsState, viewModel::action)
}

@Composable
fun InmateDetailsScreen(
    inmateDetailsState: InmateDetailsState,
    action: (InmateDetailsScreenViewModelAction) -> Unit,
) {
    Scaffold(
        topBar = {
            TopBar(inmateDetailsState)
        },
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(Color.White)
                .padding(vertical = 12.dp)
        ) {
                UserInfoSection(inmateDetailsState)
                HorizontalDivider(color = Color(0XFFE1E6EA))
                TabContent(inmateDetailsState, action)
                HorizontalDivider(color = Color(0XFFE1E6EA))

            when (inmateDetailsState.selectedTab) {
                    RowTabItem.PERSONAL_INFO -> PersonalInfoTabContent(inmateDetailsState)
                    RowTabItem.CLASSIFICATION -> ClassificationTabContent(inmateDetailsState)
                    RowTabItem.STICKY_NOTES -> StickyNotesTabContent()
                    RowTabItem.DOC -> DocTabContent(inmateDetailsState)
                    RowTabItem.HEALTH -> {}
                    RowTabItem.TIMELINE -> TimelineTabContent(inmateDetailsState)
                }
            }
        }
    }

@Composable
private fun UserInfoSection(inmateDetailsState: InmateDetailsState) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(start = 16.dp)
    ) {
        Image(
            painter = painterResource(inmateDetailsState.image),
            contentDescription = "",
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape).align(Alignment.Top)
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(6.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${inmateDetailsState.name} |",
                    style = MaterialTheme.typography.labelLarge
                )
                Icon(
                    painter = painterResource(R.drawable.ic_note_test1),
                    contentDescription = "",
                    tint = Color(0xFF999999)
                )
                Text(
                    text = inmateDetailsState.date,
                    style = MaterialTheme.typography.labelMedium.copy(color = Color(0xFF999999))
                )
            }
            Text(
                text = inmateDetailsState.locationDetails,
                style = MaterialTheme.typography.labelSmall.copy(Color(0xFF666666))
            )
                AssistChip(
                    onClick = {},
                    label = { Text(text = "Status: ${inmateDetailsState.status}", style = MaterialTheme.typography.labelMedium.copy(Color.Black)) },
                    leadingIcon = { Icon(
                        painter = painterResource(R.drawable.ic_note_test1),
                        contentDescription = "Leading icon",
                        tint = FocusTextSubTitleColor,
                        modifier = Modifier.size(16.dp)
                    ) },
                    trailingIcon = { Icon(
                        painter = painterResource(R.drawable.ic_edit),
                        contentDescription = "Trailing icon",
                        tint = FocusTextSubTitleColor,
                        modifier = Modifier.size(16.dp)
                    ) },
                    modifier = Modifier.padding(start = 6.dp),
                    border = BorderStroke(width = 1.dp, color = MaterialTheme.colorScheme.background),
                )
            }
        }
    }

@Composable
fun TabContent(
    inmateDetailsState: InmateDetailsState,
    action: (InmateDetailsScreenViewModelAction) -> Unit,
) {
    val selectedIndex = RowTabItem.entries.indexOf(inmateDetailsState.selectedTab)
    ScrollableTabRow(
        selectedTabIndex = selectedIndex,
        modifier = Modifier.fillMaxWidth(),
        containerColor = Color.White,
        contentColor = MaterialTheme.colorScheme.primary,
        edgePadding = 0.dp,
        divider = { HorizontalDivider(color = Color(0XFFE1E6EA)) }
    ) {
        RowTabItem.entries.forEachIndexed { index, tabItem ->
            val selected = index == selectedIndex
            Tab(
                selected = selected,
                onClick = { action(InmateDetailsScreenViewModelAction.OnTabSelected(tabItem)) },
                selectedContentColor = MaterialTheme.colorScheme.primary,
                unselectedContentColor = MaterialTheme.colorScheme.outlineVariant,
                text = {
                    Text(
                        text = tabItem.tab,
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal
                    )
                }
            )
        }
    }
}

@Composable
fun TopBar(inmateDetailsState: InmateDetailsState){
    Box {
        AppCommonToolbar(
            title = inmateDetailsState.toolbarTitle,
            onEndIconClicked = {},
            onBackIconClicked = {}
        )
        Row(
            modifier = Modifier.padding(top = 12.dp, end = 16.dp).height(21.dp).clip(RoundedCornerShape(4.dp)).background(Color.White).padding(2.dp).align(Alignment.CenterEnd),
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_recreation),
                contentDescription = null,
                tint = Color(0xFF6B7280)
            )
            Text(
                text = inmateDetailsState.time,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.W600,
                color = Color(0xFF2A333C),
                textAlign = TextAlign.Center
            )
            Icon(
                painter = painterResource(R.drawable.ic_inmate_recreation_down_arrow),
                contentDescription = null,
            )
        }
    }
}

@PreviewScreenSizes
@Composable
private fun InmateDetailsScreenRootPreview() {
    MyAppTheme {
        InmateDetailsScreen(inmateDetailsState = InmateDetailsState()) { }
    }
}