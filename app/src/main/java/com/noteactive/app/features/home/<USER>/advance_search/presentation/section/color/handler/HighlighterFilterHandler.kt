package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.color.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import javax.inject.Inject

class HighlighterFilter<PERSON>andler @Inject constructor() : FilterHandler {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val highlighters = state.listOfColor
        val selectAll = !highlighters.isSelectAllEnable
        val updatedFiltered = highlighters.filteredItems.map { it.copy(isSelected = selectAll) }
        val updatedList = highlighters.listItems.map {
            if (updatedFiltered.any { u -> u.id == it.id })
                it.copy(isSelected = selectAll) else it
        }
        return state.copy(
            listOfColor = highlighters.copy(
                listItems = updatedList,
                filteredItems = updatedFiltered,
                isSelectAllEnable = selectAll
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val highlighters = state.listOfColor
        val updatedFiltered = highlighters.filteredItems.toMutableList()
        val item = updatedFiltered[index]
        val updatedItem = item.copy(isSelected = !item.isSelected)
        updatedFiltered[index] = updatedItem

        val updatedList = highlighters.listItems.map {
            if (it.id == updatedItem.id) it.copy(isSelected = updatedItem.isSelected) else it
        }
        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            listOfColor = highlighters.copy(
                listItems = updatedList,
                filteredItems = updatedFiltered,
                isSelectAllEnable = allSelected
            )
        )
    }

    override fun search(state: FilterSectionState, searchText: String): FilterSectionState = state
}
