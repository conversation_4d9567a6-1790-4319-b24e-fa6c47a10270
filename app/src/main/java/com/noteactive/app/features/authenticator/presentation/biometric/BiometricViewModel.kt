package com.noteactive.app.features.authenticator.presentation.biometric

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModel
import com.noteactive.app.features.authenticator.domain.repository.AuthenticatorRepository
import com.noteactive.app.features.authenticator.domain.state.BiometricAuthState
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
open class BiometricViewModel @Inject constructor(
    private val authenticatorRepository: AuthenticatorRepository
) : ViewModel() {

    private val _authState = mutableStateOf<BiometricAuthState>(BiometricAuthState.Idle)
    val authState: State<BiometricAuthState> = _authState

    fun authenticate(fragmentActivity: FragmentActivity) {
        authenticatorRepository.authenticate(fragmentActivity) { result ->
            _authState.value = result
        }
    }
}
