package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.EaseInOut
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.composable.authCardBackground
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.cancellation.CancellationInfo
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.document_info.DocumentsInfo
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.history.HistoryInfo
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.inmate_card_header.InmateCardHeader
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.other_info.OtherInfo
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.status_info.StatusInfo
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile


@Composable
fun InmateCardRoot(
    isExpanded: Boolean,
    modifier: Modifier = Modifier,
    profile: ClientProfile,
    index: Int,
    viewModelAction: (InmateScreenViewModelAction) -> Unit,
    onInmateDetailsCardClicked: () -> Unit
) {
    ProfileCard(isExpanded, modifier, profile, index, viewModelAction, onInmateDetailsCardClicked)
}


@Composable
fun ProfileCard(
    isExpanded: Boolean,
    modifier: Modifier,
    profile: ClientProfile,
    index: Int,
    viewModelAction: (InmateScreenViewModelAction) -> Unit,
    onInmateDetailsCardClicked: () -> Unit

) {


    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 16.dp)
            .border(
                BorderStroke(width = 1.dp, color = Color(0xFFCAD1EB)),
                RoundedCornerShape(4.dp)  // Add shape to border
            )
            .clip(RoundedCornerShape(4.dp))  // Clip after border
            .authCardBackground()
            .clickable {
                viewModelAction.invoke(InmateScreenViewModelAction.OnCardToggle(index = index))
            },
    ) {
        Column {
            InmateCardHeader(modifier = modifier.clickable {
                onInmateDetailsCardClicked.invoke()
            }, clientProfile = profile)
            StatusInfo(profile = profile)

            AnimatedVisibility(
                visible = isExpanded || profile.isExpanded,
                enter = expandVertically(
                    animationSpec = tween(200, easing = EaseInOut)
                ) + fadeIn(),
                exit = shrinkVertically(
                    animationSpec = tween(200, easing = EaseInOut)
                ) + fadeOut()
            ) {
                Column {
                    HorizontalDivider(color = Color(0xFFF3F4F6))
                    DocumentsInfo(profile = profile)
                    HorizontalDivider(color = Color(0xFFF3F4F6))
                    OtherInfo(profile = profile)
                    HorizontalDivider(color = Color(0xFFF3F4F6))
                    CancellationInfo(profile = profile)
                    HorizontalDivider(color = Color(0xFFF3F4F6))
                    HistoryInfo(profile = profile)
                }
            }
        }
    }
}


@PreviewScreenSizes
@Composable
fun InmateCardPreview() {
    ProfileCard(
        isExpanded = true, modifier = Modifier, index = 0, profile = ClientProfile(),
        viewModelAction = {},
    ) {

    }
}