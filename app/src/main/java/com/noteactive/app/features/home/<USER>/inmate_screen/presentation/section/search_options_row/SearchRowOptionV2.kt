package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.search_options_row

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenToolbarViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ImageType
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.InmateScreenState

@Composable
fun SearchOptionsRowV2(
    modifier: Modifier = Modifier,
    inmateScreenState: InmateScreenState,
    action: (InmateScreenToolbarViewModelAction) -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "24 Offenders",
            style = MaterialTheme.typography.labelLarge.copy(lineHeight = 14.sp),
            color = Color.Black,
            fontSize = 14.sp,
            lineHeight = 18.sp,
            fontFamily = CustomFontNunitoRegular,
            fontWeight = FontWeight.W700)
        Spacer(modifier = Modifier.weight(1f))

        inmateScreenState.headerIcons.forEachIndexed { index, icons ->
            Box(
                modifier = Modifier
                    .size(30.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(
                        if (icons.isSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            Color.Transparent
                        }

                    )
                    .padding(all = 3.dp)
            ) {

                Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.clickable {
                    when (icons.type) {
                        ImageType.FILTER -> {

                        }
                        ImageType.SORT -> {

                        }

                        ImageType.SEARCH -> {

                        }

                        ImageType.EXPAND_TOGGLE -> {
                            action(InmateScreenToolbarViewModelAction.OnExpandToggle)
                        }
                    }
                }) {
                    Image(
                        painter = painterResource(icons.image),
                        contentDescription = null,
                        colorFilter = if (icons.isSelected) {
                            ColorFilter.tint(Color.White)
                        } else {
                            null
                        }
                    )

                    if (icons != inmateScreenState.headerIcons.last()) {
                        VerticalDivider(
                            modifier = Modifier
                                .height(16.dp)
                                .padding(horizontal = 4.dp),
                            color = Color(0xFFCCCCCC)
                        )
                    }


                }


            }
        }
    }
}


@PreviewScreenSizes
@Composable
fun SearchOptionsRowV2Preview(
) {
    MyAppTheme {
        SearchOptionsRowV2(
            inmateScreenState = InmateScreenState()
        ) {}
    }

}

