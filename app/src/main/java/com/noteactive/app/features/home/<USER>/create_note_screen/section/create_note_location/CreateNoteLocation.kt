package com.noteactive.app.features.home.createnote.create_note_screen.section.create_note_location

import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.create_note_screen.action.CreateNoteViewModelAction
import com.noteactive.app.features.home.createnote.create_note_screen.section.CreateNoteItemChip
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions
import com.noteactive.app.features.home.createnote.select_location.state.SelectLocation

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CreateNoteLocation(
    modifier: Modifier = Modifier,
    note: NewNoteRow,
    onCrossClicked: (String) -> Unit
) {
    FlowRow(
        maxItemsInEachRow = 2,
        modifier = modifier
    ) {
        note.locations.forEachIndexed { locationIndex, location ->
            CreateNoteItemChip(
                title = location.locationName,
                onRemove = {
                    onCrossClicked.invoke(location.locationId)
                },
                trailingIcon = R.drawable.ic_cross
            )

        }
    }
}