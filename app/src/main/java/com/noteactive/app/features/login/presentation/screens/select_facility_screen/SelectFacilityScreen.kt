package com.noteactive.app.features.login.presentation.screens.select_facility_screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityNavigationAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.SelectFacilityState

@Composable
fun SelectFacilityScreenRoot(
    modifier: Modifier = Modifier,
    customerKey: String,
    selectFacilityNavigation: (SelectFacilityNavigationAction) -> Unit
) {
    val viewModel: SelectedFacilityScreenViewModel = hiltViewModel()
    LaunchedEffect(customerKey) {
        viewModel.getSelectedFacilities(customerKey)
    }
    val selectFacilityState by viewModel.selectFacilityState.collectAsStateWithLifecycle()
    SelectFacilityScreen(
        modifier = modifier,
        selectFacilityState = selectFacilityState,
        action = viewModel::onAction,
        navAction = selectFacilityNavigation
    )
}

@Composable
fun SelectFacilityScreen(
    modifier: Modifier = Modifier,
    selectFacilityState: SelectFacilityState,
    action: (SelectFacilityViewModelAction) -> Unit,
    navAction: (SelectFacilityNavigationAction) -> Unit
) {


    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            ToolbarSearchView(
                title = "Select Facility",
                hint = "Select Facility",
                isSearchEnabled = selectFacilityState.isSearchEnabled,
                searchText = selectFacilityState.searchText,
                onSearchTextChanged = {
                    action(SelectFacilityViewModelAction.OnSearchFacilityTextAction(it))
                },
                onSearchCrossClicked = {
                    action(SelectFacilityViewModelAction.OnSearchCrossClicked)
                },
                onSearchEnabledClicked = {
                    action(SelectFacilityViewModelAction.OnEnableSearch)
                },
                onBackIconClicked = {
                    navAction(SelectFacilityNavigationAction.NavigateBack)

                }
            )
        }
    ) { innerPadding ->
        LazyColumn(
            modifier = Modifier.padding(innerPadding)
        ) {
            items(selectFacilityState.filteredList) { facility ->
                Row(
                    Modifier
                        .background(if (facility.isSelected) Color(0xFFFEEFE7) else Color.White)
                        .clickable {
                            action(SelectFacilityViewModelAction.OnFacilitySelectAction(facility))
                            navAction.invoke(
                                SelectFacilityNavigationAction.NavigateBackWithFacility(facility)
                            )
                        }
                ) {
                    Text(
                        text = facility.facility,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        style = MaterialTheme.typography.titleMedium
                    )
                }
                HorizontalDivider()
            }
        }
    }
}


@PreviewScreenSizes
@Preview
@Composable
fun SelectFacilityScreenPreview() {
    MyAppTheme {
        SelectFacilityScreen(
            selectFacilityState = SelectFacilityState(
                allFacilityList = mutableListOf(), filteredList = mutableListOf(), searchText = "2"
            ), action = {}, navAction = {}
        )
    }

}