package com.noteactive.app.features.home.createnote.select_location.state

import androidx.compose.ui.graphics.Color
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions

data class NoteSelectLocationState(
    val toolbarTitle: String = "Select Location",
    val hint: String = "Search Location",
    val isSearchEnabled: Boolean = false,
    var isSelectAllToggle: Boolean = false,
    val searchText: String = "",
    val count: String = "Select location",
    val locations: List<SelectLocation> = locationSample,
    val filteredLocations: List<SelectLocation> = locationSample,
)

data class SelectLocation(
    val locationName: String,
    val locationId: String,
    var isSelected: Boolean,
    val tint: Color,
    val background: Color,
    val description: String = "4F Demo and Training",
    val type: NewNoteRowTypeOptions = NewNoteRowTypeOptions.LOCATION
)

val locationSample = listOf(
    SelectLocation(
        "Arnette-R-6-4F Demo and training",
        "1", false,
        tint = Color(0xFF13B4EA),
        background = Color(0xFFE7F9FF)
    ),
    SelectLocation(
        "Arnette-R-6-4F Demo and training", "2", false,
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1)
    ),
    SelectLocation(
        "Building C", "3", false,
        tint = Color(0xFFFF6900),
        background = Color(0xFFFFF3EB)
    ),
    SelectLocation(
        "Block A", "4", false,
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1)
    ),
    SelectLocation(
        "Dorm-L-B", "5", false,
        tint = Color(0xFF7AB51D),
        background = Color(0xFFEFFFD6)
    ),
    SelectLocation(
        "Dorm-L-B", "6", false,
        tint = Color(0xFFD03798),
        background = Color(0xFFFFECF8)
    ),
    SelectLocation(
        "Dorm-L-B", "21", false,
        tint = Color(0xFF7E3EF4),
        background = Color(0xFFF3F0FF)
    ),
    SelectLocation(
        "Building C", "8", false,
        tint = Color(0xFFCC1400),
        background = Color(0xFFFFECEB)
    ),
    SelectLocation(
        "Dorm-L-B", "15", false,
        tint = Color(0xFF7E3EF4),
        background = Color(0xFFF3F0FF)
    ),
    SelectLocation(
        "Building C", "8", false,
        tint = Color(0xFFCC1400),
        background = Color(0xFFFFECEB)
    ), SelectLocation(
        "Dorm-L-B", "17", false,
        tint = Color(0xFF7E3EF4),
        background = Color(0xFFF3F0FF)
    ),
    SelectLocation(
        "Building C", "19", false,
        tint = Color(0xFFCC1400),
        background = Color(0xFFFFECEB)
    )

)
