package com.noteactive.app.features.home.createnote.update_date_time_screen.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.createnote.update_date_time_screen.action.UpdateDateAndTimeViewModelAction
import com.noteactive.app.features.home.createnote.update_date_time_screen.state.UpdateDateAndTimeState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class UpdateDateAndTimeViewModel @Inject constructor() : ViewModel() {
    private val _updateDateAndTimeState = MutableStateFlow(
        UpdateDateAndTimeState(
            time = "12:20",
            date = "07/07/2025",
            comment = ""
        )
    )
    val updateDateAndTimeState = _updateDateAndTimeState.asStateFlow()

    fun action(viewModelAction: UpdateDateAndTimeViewModelAction) {
        when (viewModelAction) {
            is UpdateDateAndTimeViewModelAction.OnCommentChanged -> {
                _updateDateAndTimeState.update {
                    it.copy(
                        comment = viewModelAction.comment
                    )
                }
            }

            is UpdateDateAndTimeViewModelAction.OnDateChanged -> {
                _updateDateAndTimeState.update {
                    it.copy(
                        date = viewModelAction.date
                    )
                }
            }

            is UpdateDateAndTimeViewModelAction.OnTimeChanged -> {
                _updateDateAndTimeState.update {
                    it.copy(
                        time = viewModelAction.time
                    )
                }
            }
        }
    }
}
