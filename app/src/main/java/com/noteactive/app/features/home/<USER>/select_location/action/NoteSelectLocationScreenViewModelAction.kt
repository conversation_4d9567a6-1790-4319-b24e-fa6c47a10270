package com.noteactive.app.features.home.createnote.select_location.action


sealed interface NoteSelectLocationScreenViewModelAction {
    data object OnEnableSearch : NoteSelectLocationScreenViewModelAction
    data object OnSearchCrossClicked : NoteSelectLocationScreenViewModelAction
    data object OnSelectAllToggle : NoteSelectLocationScreenViewModelAction
    data class OnSelectToggle(val index: Int) : NoteSelectLocationScreenViewModelAction
    data class OnSearchTextChanged(val text: String) : NoteSelectLocationScreenViewModelAction
}