package com.noteactive.app.features.login.presentation.screens.enter_activation_key

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.adaptive.currentWindowAdaptiveInfo
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.authCardBackground
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.theme.DeviceConfiguration
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyNavigation
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyViewModelAction
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyApiState
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyState

@Composable
fun EnterActivationKeyScreen(
    modifier: Modifier = Modifier,
    onnNavigation: (EnterActivationKeyNavigation) -> Unit
) {
    val viewModel: EnterActivationKeyViewModel = hiltViewModel()
    val enterActivationScreenState by viewModel.activationKeyState.collectAsStateWithLifecycle()
    val activationApiState by viewModel.apiState.collectAsStateWithLifecycle()

    ActivationKeyRoot(
        modifier = modifier,
        activationKeyState = enterActivationScreenState,
        activationApiState = activationApiState,
        onViewmodelAction = viewModel::onAction,
        onNavigation = onnNavigation
    )
}

@Composable
fun ActivationKeyRoot(
    modifier: Modifier = Modifier,
    activationKeyState: ActivationKeyState,
    activationApiState: ActivationKeyApiState,
    onViewmodelAction: (EnterActivationKeyViewModelAction) -> Unit,
    onNavigation: (EnterActivationKeyNavigation) -> Unit
) {
    val windowSizeClass = currentWindowAdaptiveInfo().windowSizeClass
    val deviceConfiguration = DeviceConfiguration.fromWindowSizeClass(windowSizeClass)
    val imagePadding = when (deviceConfiguration) {
        DeviceConfiguration.MOBILE_PORTRAIT -> 120.dp
        DeviceConfiguration.MOBILE_LANDSCAPE -> 20.dp
        DeviceConfiguration.TABLET_PORTRAIT -> 150.dp
        DeviceConfiguration.TABLET_LANDSCAPE -> 80.dp
        else -> 100.dp
    }

    LaunchedEffect(activationApiState) {
        if (activationApiState is ActivationKeyApiState.Success) {
            onNavigation(EnterActivationKeyNavigation.ActivationFormNav)
        } else if (activationApiState is ActivationKeyApiState.Error) {
            onViewmodelAction(EnterActivationKeyViewModelAction.OnError)
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {

        Box(
            modifier = Modifier
                .fillMaxWidth(.75f)
                .authCardBackground()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                Text(
                    text = stringResource(R.string.login_screen_title),
                    style = MaterialTheme.typography.labelLarge
                )

                PrimaryTextField(
                    modifier = Modifier
                        .fillMaxWidth(),
                    value = activationKeyState.text,
                    onValueChange = { value ->
                        onViewmodelAction(EnterActivationKeyViewModelAction.OnTextChanged(value))
                    },
                    hintText = stringResource(R.string.enter_activation_key_hint),
                    maxLines = 1,
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    errorText = stringResource(R.string.activation_key_invalid_error),
                    isError = activationKeyState.isError
                )

                PrimaryButton(
                    text = stringResource(R.string.enter_activation_key_button_title),
                    modifier = Modifier.fillMaxWidth(),
                    isEnabled = activationKeyState.isButtonEnabled
                ) {
                    onViewmodelAction(EnterActivationKeyViewModelAction.OnSubmit)
                }
            }

        }
        Image(
            modifier = Modifier
                .padding(top = imagePadding, start = 0.dp, end = 0.dp, bottom = 0.dp)
                .align(Alignment.TopCenter),
            painter = painterResource(R.drawable.ic_app_name),
            contentDescription = null,
            contentScale = ContentScale.FillBounds
        )
    }


}


@PreviewScreenSizes
@Preview
@Composable
fun ActivationKeyPreview() {
    MyAppTheme {
        ActivationKeyRoot(
            activationKeyState = ActivationKeyState(),
            activationApiState = ActivationKeyApiState.Loading,
            onViewmodelAction = {},
            onNavigation = {})
    }

}