package com.noteactive.app.features.home.inmate.inmate_screen.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenToolbarViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ImageType
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.InmateScreenState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class InmateScreenViewModel @Inject constructor() : ViewModel() {

    private val _inmateScreenState = MutableStateFlow(InmateScreenState())
    val inmateScreenState = _inmateScreenState.asStateFlow()

    fun actionToolbar(action: InmateScreenToolbarViewModelAction) {
        when (action) {
            InmateScreenToolbarViewModelAction.OnExpandToggle -> {
                val currentState = _inmateScreenState.value
                val newIsExpanded = !currentState.isExpanded

                val index =
                    currentState.headerIcons.indexOfFirst { it.type == ImageType.EXPAND_TOGGLE }

                if (index != -1) {
                    val updatedIcons = currentState.headerIcons.mapIndexed { i, icon ->
                        if (i == index) icon.copy(isSelected = !icon.isSelected) else icon
                    }

                    _inmateScreenState.update {
                        it.copy(
                            isExpanded = newIsExpanded,
                            headerIcons = updatedIcons
                        )
                    }
                }
            }
        }
    }

    fun action(action: InmateScreenViewModelAction) {
        when (action) {

            is InmateScreenViewModelAction.OnCardToggle -> {
                val currentState = _inmateScreenState.value
                val updatedProfiles = currentState.profiles.mapIndexed { index, profile ->
                    if (index == action.index) {
                        profile.copy(isExpanded = !profile.isExpanded)
                    } else {
                        profile
                    }
                }

                _inmateScreenState.update {
                    it.copy(profiles = updatedProfiles)
                }
            }
        }
    }
}