package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.classification

import com.noteactive.app.R

data class ClassificationState(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<AdvanceSearchClassification> = formState(),
    var filteredListState: List<AdvanceSearchClassification> = formState(),
    var searchText: String = ""
)

data class AdvanceSearchClassification(
    val icon: Int,
    val title: String,
    val id: String,
    val isSelected: Boolean
)


private fun formState() = listOf(
    AdvanceSearchClassification(
        icon = R.drawable.ic_fire,
        title = "ACA CLERK",
        isSelected = false,
        id = "1"
    ),
    AdvanceSearchClassification(
        icon = R.drawable.ic_note_test1,
        title = "ADA",
        isSelected = false,
        id = "2"

    ),
    AdvanceSearchClassification(
        icon = R.drawable.ic_fire,
        title = "BLUE",
        isSelected = false,
        id = "3"

    ),
    AdvanceSearchClassification(
        icon = R.drawable.ic_note_test3,
        title = "Disciplinary Report Task",
        isSelected = false,
        id = "4"
    ),

    )