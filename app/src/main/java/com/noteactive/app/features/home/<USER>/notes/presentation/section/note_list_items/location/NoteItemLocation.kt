package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.location

import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MaterialTheme.typography
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteBoldTextStyle
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteTextStyle

@Composable
fun NoteItemLocation(
    noteHeader: NoteHeader,
    noteContent: NoteContent,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val title = buildAnnotatedString {
            withStyle(
                getNoteBoldTextStyle()
            ) {
                if (noteHeader.expanded) {
                    append("Location : ")
                }
            }
            withStyle(
                getNoteTextStyle()
            ) {
                append(noteContent.location)
            }
        }



        NoteItemIcon(imageId = R.drawable.iv_location)


        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())
        Text(
            text = title,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            style = typography.titleMedium

        )

    }
}

@PreviewScreenSizes
@Preview
@Composable
fun NoteItemLocation() {
    MaterialTheme {
        NoteItemLocation(
            noteHeader = NoteHeader(
                time = "12.20",
                id = 13,
                expanded = true
            ),
            noteContent = NoteContent(
                location = "Cajun 2 - AB",
                type = NoteItemType.LOCATION
            )
        )
    }
}