package com.noteactive.app.features.home.createnote.select_location

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.DisabledTextField
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.IconWithBackgroundContainer
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.createnote.select_location.action.NoteSelectLocationNavigationAction
import com.noteactive.app.features.home.createnote.select_location.action.NoteSelectLocationScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_location.state.NoteSelectLocationState
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsNavigationAction
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsViewModelAction

@Composable
fun NoteSelectLocationScreenRoot(
    navAction: (NoteSelectLocationNavigationAction) -> Unit
) {
    val viewModel: NoteSelectLocationScreenViewModel = hiltViewModel()
    val locationState by viewModel.noteSelectLocationScreenState.collectAsStateWithLifecycle()
    NoteSelectLocationScreen(
        locationState = locationState,
        action = viewModel::action,
        navAction = navAction::invoke
    )
}

@Composable
private fun NoteSelectLocationScreen(
    locationState: NoteSelectLocationState,
    action: (NoteSelectLocationScreenViewModelAction) -> Unit,
    navAction: (NoteSelectLocationNavigationAction) -> Unit

) {
    Scaffold(
        topBar = {
            ToolbarSearchView(
                title = locationState.toolbarTitle,
                hint = locationState.hint,
                isSearchEnabled = locationState.isSearchEnabled,
                searchText = locationState.searchText,
                onSearchTextChanged = {
                    action(
                        NoteSelectLocationScreenViewModelAction.OnSearchTextChanged(
                            it
                        )
                    )
                },
                onSearchCrossClicked = { action(NoteSelectLocationScreenViewModelAction.OnSearchCrossClicked) },
                onSearchEnabledClicked = { action(NoteSelectLocationScreenViewModelAction.OnEnableSearch) },
                onBackIconClicked = {
                    navAction(NoteSelectLocationNavigationAction.OnBackPressed)
                },
            )
        },
        bottomBar = {
            MultiButtonBottomRow(
                modifier = Modifier.background(Color.White),
                secondaryButtonTitle = R.string.cancel,
                primaryButtonTitle = R.string.submit,
                secondaryButtonClickListener = {
                    navAction(NoteSelectLocationNavigationAction.OnBackPressed)
                },
                primaryButtonClickListener = {
                    val locations = locationState.locations.filter { it.isSelected }
                    navAction(NoteSelectLocationNavigationAction.OnSelectLocationAction(locations))

                })

        }) { innerPadding ->
        LocationItems(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(color = Color.White),

            state = locationState,
            action = action
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LocationItems(
    modifier: Modifier,
    state: NoteSelectLocationState,
    action: (NoteSelectLocationScreenViewModelAction) -> Unit
) {

    LazyColumn(modifier = modifier) {
        stickyHeader {
            SelectAllHeader(state, action)
        }

        itemsIndexed(state.filteredLocations) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        action.invoke(
                            NoteSelectLocationScreenViewModelAction.OnSelectToggle(
                                index
                            )
                        )
                    }
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))

                IconWithBackgroundContainer(
                    imageRes = R.drawable.iv_location,
                    tint = item.tint,
                    bgColor = item.background,
                )
                Column(verticalArrangement = Arrangement.spacedBy(2.dp)) {
                    Text(
                        text = item.locationName,
                        style = MaterialTheme.typography.labelLarge.copy(lineHeight = 1.sp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodySmall.copy(
                            lineHeight = 1.sp,
                            color = Color(0xFF999999)
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }


            }
            HorizontalDivider(color = NotesScreenHeaderDevider)
        }
    }
}

@Composable
private fun SelectAllHeader(
    state: NoteSelectLocationState,
    action: (NoteSelectLocationScreenViewModelAction) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconHelper(
            imageRes = getCheckBoxIcon(state.isSelectAllToggle),
            modifier = Modifier.clickable {
                action(NoteSelectLocationScreenViewModelAction.OnSelectAllToggle)
            })
        Text(text = state.count, style = MaterialTheme.typography.labelLarge)
    }
    HorizontalDivider(color = NotesScreenHeaderDevider)
}


@PreviewScreenSizes
@Composable
private fun NoteSelectLocationScreenRootPreview() {
    NoteSelectLocationScreen(locationState = NoteSelectLocationState(), {}) {}
}