package com.noteactive.app.features.home.createnote.create_note_screen.section.note_facility

import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.create_note_screen.section.CreateNoteItemChip
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AddNoteFacility(
    modifier: Modifier = Modifier,
    note: NewNoteRow,
    onCrossClicked: (String) -> Unit
) {
    FlowRow(
        maxItemsInEachRow = 2,
        modifier = modifier
    ) {
        note.facilities.forEachIndexed { locationIndex, location ->
            CreateNoteItemChip(
                title = location.facilityName,
                onRemove = {
                    onCrossClicked.invoke(location.facilityId)
                },
                trailingIcon = R.drawable.ic_cross
            )

        }
    }
}