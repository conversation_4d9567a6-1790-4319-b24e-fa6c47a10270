package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.other_info

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon
import kotlin.random.Random

@Composable
fun OtherInfo(modifier: Modifier = Modifier, profile: ClientProfile) {

    Row(
        modifier = modifier
            .heightIn(min = 40.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {

        NoteItemIcon(modifier = Modifier.width(40.dp), imageId = R.drawable.ic_note_test1)
        VerticalDivider(
            color = NotesListVerticalDivider,
            modifier = Modifier
                .fillMaxHeight()
                .padding(end = 8.dp)
        )

        profile.otherTags.forEachIndexed { index, tags ->
            Row(
                modifier = Modifier
                    .padding(2.dp)
                    .height(28.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color(Random.nextInt()).copy(alpha = 0.1f))
                    .padding(vertical = 4.dp, horizontal = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(painter = painterResource(R.drawable.ic_download), contentDescription = null)
                Text(
                    text = tags,
                    textAlign = TextAlign.Center,
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    fontFamily = CustomFontNunitoRegular,
                    fontWeight = FontWeight(400),
                )
            }
        }

    }

}

@PreviewScreenSizes
@Preview
@Composable
fun OtherInfoPreview() {
    OtherInfo(profile = ClientProfile())
}