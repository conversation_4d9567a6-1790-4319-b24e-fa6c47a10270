package com.noteactive.app.features.home.createnote.select_active_note_screen.presentation

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.ActiveNoteBoxBorder
import com.noteactive.app.core.presentation.designsystem.theme.ActiveNoteHeaderBackgroundColor
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.home.createnote.select_active_note_screen.action.SelectActiveNoteViewModelAction
import com.noteactive.app.features.home.createnote.select_active_note_screen.state.ActiveNote

@Composable
fun SelectActiveNoteScreen(onBackPressed: () -> Unit) {
    val viewModel: SelectActiveNoteViewModel = hiltViewModel()
    val state by viewModel.selectActiveNoteState.collectAsStateWithLifecycle()

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            ToolbarSearchView(
                title = stringResource(R.string.select_active_notes_screen_title),
                hint = stringResource(R.string.select_active_notes_screen_title),
                isSearchEnabled = state.isSearchEnabled,
                searchText = state.searchText,
                onSearchTextChanged = {
                    viewModel.onAction(
                        SelectActiveNoteViewModelAction.OnSearchTextChanged(
                            it
                        )
                    )
                },
                onSearchCrossClicked = { viewModel.onAction(SelectActiveNoteViewModelAction.OnSearchCrossClicked) },
                onSearchEnabledClicked = { viewModel.onAction(SelectActiveNoteViewModelAction.OnEnableSearch) },
                onBackIconClicked = {onBackPressed()},
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(color = Color.White)
        ) {
            state.filteredActiveNote.forEach { activeNoteCollection ->
                SectionHeader(title = activeNoteCollection.title)
                ActiveNoteGrid(activeNotes = activeNoteCollection.activeNotes)
            }
        }

    }
}

@Composable
fun SectionHeader(title: String) {
    HorizontalDivider(color = NotesScreenHeaderDevider)
    Text(
        text = title,
        modifier = Modifier
            .fillMaxWidth()
            .background(ActiveNoteHeaderBackgroundColor)
            .padding(horizontal = 16.dp, vertical = 11.dp),
        style = MaterialTheme.typography.labelLarge
    )
    HorizontalDivider(color = NotesScreenHeaderDevider)
}

@Composable
fun ActiveNoteGrid(activeNotes: List<ActiveNote>) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        contentPadding = PaddingValues(16.dp),
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(activeNotes) { activeNote ->
            CartButton(activeNote)
        }
    }
}

@Composable
fun CartButton(activeNote: ActiveNote) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        modifier = Modifier
            .fillMaxWidth()
            .border(1.dp, ActiveNoteBoxBorder, shape = RoundedCornerShape(4.dp))
            .clickable {

            }
            .padding(12.dp)
    ) {
        Icon(
            painter = painterResource(activeNote.image),
            contentDescription = "Active Note Icon",
            tint = FocusTextTitleColor,
        )
        Text(text = activeNote.name, style = MaterialTheme.typography.bodyMedium)
    }
}

@PreviewScreenSizes
@Preview
@Composable
fun SelectActiveNoteScreenPreview() {
    MyAppTheme {
        SelectActiveNoteScreen{

        }
    }
}