package com.noteactive.app.features.home.shownotes.advance_search.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.AdvanceSearchViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note.handler.ActiveNoteFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.classification.handler.ClassificationFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.color.handler.HighlighterFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.form.handler.FormFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.location.handler.LocationFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.offenders.handler.OffenderStatusFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.task_type.handler.TaskTypeFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.user.handler.UserFilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject


@HiltViewModel
class AdvanceSearchScreenViewModel @Inject constructor(
    private val userHandler: UserFilterHandler,
    private val locationHandler: LocationFilterHandler,
    private val highlighterHandler: HighlighterFilterHandler,
    private val formHandler: FormFilterHandler,
    private val activeNoteHandler: ActiveNoteFilterHandler,
    private val offenderStatusHandler: OffenderStatusFilterHandler,
    private val taskTypeStatusHandler: TaskTypeFilterHandler,
    private val classificationHandler: ClassificationFilterHandler
) : ViewModel() {

    private val _filterItems = MutableStateFlow(FilterSectionState())
    val filterItems = _filterItems.asStateFlow()

    fun onAction(action: AdvanceSearchViewModelAction) {
        _filterItems.update { state ->
            when (action) {
                is AdvanceSearchViewModelAction.OnSelectFilter ->
                    state.copy(selectedSection = action.filterSection)

                is AdvanceSearchViewModelAction.OnKeywordTextChange ->
                    state.copy(keyword = action.keyword)

                is AdvanceSearchViewModelAction.OnSelectDateAndTime -> state

                is AdvanceSearchViewModelAction.OnSearchText ->
                    handleSearchText(state, action.filterSection, action.text)

                is AdvanceSearchViewModelAction.OnSelectAllToggle ->
                    handleSelectAllToggle(state, action.filterSection)

                is AdvanceSearchViewModelAction.OnSelectToggle ->
                    handleSelectToggle(state, action.filterSection, action.index)
            }
        }
    }

    private fun handleSearchText(
        state: FilterSectionState,
        filterSection: FilterSection,
        text: String
    ): FilterSectionState {
        return when (filterSection) {
            FilterSection.USER -> userHandler.search(state, text)
            FilterSection.LOCATION -> locationHandler.search(state, text)
            FilterSection.FORM -> formHandler.search(state, text)
            FilterSection.ACTIVE_NOTES -> activeNoteHandler.search(state, text)
            FilterSection.OFFENDER_STATUS -> offenderStatusHandler.search(state, text)
            FilterSection.CLASSIFICATION -> classificationHandler.search(state, text)
            FilterSection.TASK_TYPE -> taskTypeStatusHandler.search(state, text)
            else -> state
        }
    }

    private fun handleSelectAllToggle(
        state: FilterSectionState,
        filterSection: FilterSection
    ): FilterSectionState {
        return when (filterSection) {
            FilterSection.USER -> userHandler.toggleSelectAll(state)
            FilterSection.LOCATION -> locationHandler.toggleSelectAll(state)
            FilterSection.HIGHLIGHTER -> highlighterHandler.toggleSelectAll(state)
            FilterSection.ACTIVE_NOTES -> activeNoteHandler.toggleSelectAll(state)
            FilterSection.FORM -> formHandler.toggleSelectAll(state)
            FilterSection.OFFENDER_STATUS -> offenderStatusHandler.toggleSelectAll(state)
            FilterSection.CLASSIFICATION -> classificationHandler.toggleSelectAll(state)
            FilterSection.TASK_TYPE -> taskTypeStatusHandler.toggleSelectAll(state)
            else -> state
        }
    }

    private fun handleSelectToggle(
        state: FilterSectionState,
        filterSection: FilterSection,
        index: Int
    ): FilterSectionState {
        return when (filterSection) {
            FilterSection.USER -> userHandler.toggleItem(state, index)
            FilterSection.LOCATION -> locationHandler.toggleItem(state, index)
            FilterSection.HIGHLIGHTER -> highlighterHandler.toggleItem(state, index)
            FilterSection.ACTIVE_NOTES -> activeNoteHandler.toggleItem(state, index)
            FilterSection.FORM -> formHandler.toggleItem(state, index)
            FilterSection.OFFENDER_STATUS -> offenderStatusHandler.toggleItem(state, index)
            FilterSection.CLASSIFICATION -> classificationHandler.toggleItem(state, index)
            FilterSection.TASK_TYPE -> taskTypeStatusHandler.toggleItem(state, index)
            else -> state
        }
    }
}





