package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.classification.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.classification.ClassificationState
import javax.inject.Inject

class ClassificationFilterHandler @Inject constructor() : FilterHandler {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val classificationState = state.classificationState
        val isSelectAllEnabledReverse = !classificationState.isSelectAllEnable
        val selectAllItems = classificationState.listItems.map {
            it.copy(isSelected = isSelectAllEnabledReverse)
        }
        return state.copy(
            classificationState = ClassificationState(
                isSelectAllEnable = isSelectAllEnabledReverse,
                listItems = selectAllItems,
                filteredListState = selectAllItems,
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val classificationState = state.classificationState
        val filteredList = classificationState.filteredListState.toMutableList()
        val item = filteredList[index]
        val updatedItem = item.copy(isSelected = !item.isSelected)
        filteredList[index] = updatedItem
        val updatedList = state.classificationState.listItems.map {
            if (it.id == updatedItem.id) it.copy(isSelected = updatedItem.isSelected) else it
        }


        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            classificationState = classificationState.copy(
                isSelectAllEnable = allSelected,
                listItems = updatedList,
                filteredListState = filteredList,
            )
        )
    }


    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val notes = state.classificationState
        val selectedIds =
            (notes.listItems + notes.filteredListState).filter { it.isSelected }.map { it.id }
                .toSet()
        val filtered = notes.listItems.filter {
            it.title.contains(searchText, ignoreCase = true)
        }.map {
            it.copy(isSelected = selectedIds.contains(it.id))
        }
        return state.copy(
            classificationState = notes.copy(
                searchText = searchText, filteredListState = filtered
            )
        )
    }
}