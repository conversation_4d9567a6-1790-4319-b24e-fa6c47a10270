package com.noteactive.app.features.home.createnote.create_note_screen.section.toolbar

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderTextBlue

@Composable
fun CreateNoteToolbar(
    modifier: Modifier = Modifier,
    title: String = "New Note",
    date: String = "20 May 2024",
    time: String = "12:10"
) {

    TopAppBar(modifier = modifier.shadow(20.dp), title = {
        Column(modifier = Modifier.padding(horizontal = 16.dp)) {
            Text(
                text = title,
                style = MaterialTheme.typography.labelLarge.copy(fontWeight = FontWeight.W700)
                    .copy(color = Color.Black)
            )
            Row {
                Text(
                    text = date,
                    style = MaterialTheme.typography.bodyMedium.copy(color = NotesScreenHeaderTextBlue)
                )
                VerticalDivider(
                    thickness = 1.dp, modifier = Modifier
                        .height(16.dp)
                        .padding(horizontal = 10.dp)
                )
                Text(
                    text = time,
                    style = MaterialTheme.typography.bodyMedium.copy(color = NotesScreenHeaderTextBlue)
                )
            }
        }
    }, navigationIcon = {
        Icon(
            painter = painterResource(R.drawable.ic_toolbar_back),
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = Color.Black
        )
    })
}