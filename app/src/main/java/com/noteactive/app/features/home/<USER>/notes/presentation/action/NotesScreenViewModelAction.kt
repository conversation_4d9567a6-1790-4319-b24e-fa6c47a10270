package com.noteactive.app.features.home.shownotes.notes.presentation.action

import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader

sealed interface NotesScreenViewModelAction {
    data class OnToggleSeeAll(val noteHeader: NoteHeader) : NotesScreenViewModelAction
    data class OnDateRangeSelected(val start: Long, val end: Long?) : NotesScreenViewModelAction
    data class OnUpdateTimeSelected(val time: String) : NotesScreenViewModelAction
}