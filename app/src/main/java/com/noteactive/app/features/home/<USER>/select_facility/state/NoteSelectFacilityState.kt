package com.noteactive.app.features.home.createnote.select_facility.state

import androidx.compose.ui.graphics.Color
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions

data class NoteSelectFacilityState(
    val toolbarTitle: String = "Select Facilities",
    val hint: String = "Search Facilities",
    val isSearchEnabled: Boolean = false,
    var isSelectAllToggle: Boolean = false,
    val searchText: String = "",
    val count: String = "Select location",
    val facilities: List<SelectFacility> = facilitiesSample,
    val filteredFacilities: List<SelectFacility> = facilitiesSample,
) {
}

data class SelectFacility(
    val facilityName: String,
    val facilityId: String,
    var isSelected: Boolean,
    val tint: Color,
    val background: Color,
    val type: NewNoteRowTypeOptions = NewNoteRowTypeOptions.FACILITIES

)

val facilitiesSample = listOf(
    SelectFacility(
        "Dorm-L-B", "1", false,
        tint = Color(0xFF13B4EA),
        background = Color(0xFFE7F9FF)
    ),
    SelectFacility(
        "Building B", "2", false,
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1)
    ),
    SelectFacility(
        "Building C", "3", false,
        tint = Color(0xFFFF6900),
        background = Color(0xFFFFF3EB)
    ),
    SelectFacility(
        "Block A", "4", false,
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1)
    ),
    SelectFacility(
        "Dorm-L-B", "5", false,
        tint = Color(0xFF7AB51D),
        background = Color(0xFFEFFFD6)
    ),
    SelectFacility(
        "Dorm-L-B", "6", false,
        tint = Color(0xFFD03798),
        background = Color(0xFFFFECF8)
    ),
    SelectFacility(
        "Dorm-L-B", "7", false,
        tint = Color(0xFF7E3EF4),
        background = Color(0xFFF3F0FF)
    ),
    SelectFacility(
        "Building C", "8", false,
        tint = Color(0xFFCC1400),
        background = Color(0xFFFFECEB)
    )
)
