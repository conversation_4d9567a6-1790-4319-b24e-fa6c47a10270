package com.noteactive.app.features.login.data.model.dao.activation_key_dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Transaction
import com.noteactive.app.core.util.base.BaseDao
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import kotlinx.coroutines.flow.Flow

@Dao
interface ActivationKeyResponseDao : BaseDao<ActivationKeyResponseUserInfo> {

    @Query("SELECT * FROM activation_user_table LIMIT 1")
    fun getActivationUserData(): Flow<List<ActivationKeyResponseUserInfo>>

    @Query("SELECT * FROM activation_user_table where activationKey = :activationKey LIMIT 1")
    fun getActivatedUser(activationKey: String): Flow<ActivationKeyResponseUserInfo?>

    @Query("SELECT * FROM activation_user_table LIMIT 1")
    suspend fun getActivatedUserDetail(): ActivationKeyResponseUserInfo?

    @Query("DELETE FROM activation_user_table")
    suspend fun delete()

    @Query("SELECT facilities from activation_user_table LIMIT 1")
    fun getFacilities(): String


    @Transaction
    suspend fun replaceActivationKeys(activationKeys: List<ActivationKeyResponseUserInfo>) {
        delete()
        insert(activationKeys)
    }

}
