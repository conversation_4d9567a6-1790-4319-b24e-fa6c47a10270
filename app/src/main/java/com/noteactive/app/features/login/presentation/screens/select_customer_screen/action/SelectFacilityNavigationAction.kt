package com.noteactive.app.features.login.presentation.screens.select_facility_screen.action

import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.CustomerItemUi
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi

sealed interface SelectCustomerNavigationAction {
    data class NavigateBackWithCustomer(val facility: CustomerItemUi) : SelectCustomerNavigationAction
    object  NavigateBack : SelectCustomerNavigationAction
}