package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.date_time

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.date_time.state.DateAndTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import javax.inject.Inject

@HiltViewModel
class DateAndTimeViewModel @Inject constructor() : ViewModel() {
    val dateAndTime = MutableStateFlow(DateAndTime(
        startDate = "12/12/24",
        startTime = "19:12",
        endDate = "24/12/24",
        endTime = "18:21"
    ))
}