package com.noteactive.app.features.authenticator.data.repositoryimpl

import android.content.Context
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.noteactive.app.features.authenticator.domain.repository.AuthenticatorRepository
import com.noteactive.app.features.authenticator.domain.state.BiometricAuthState

class AuthenticatorRepositoryImpl(private val context: Context) : AuthenticatorRepository {
    override fun authenticate(
        fragmentActivity: FragmentActivity,
        callback: (BiometricAuthState) -> Unit
    ) {
        val executor = ContextCompat.getMainExecutor(context)

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle("Biometric Login")
            .setSubtitle("Use your fingerprint to log in")
            .setNegativeButtonText("Cancel")
            .build()

        val biometricPrompt = BiometricPrompt(
            fragmentActivity,
            executor,
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    callback(BiometricAuthState.Success)
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    callback(BiometricAuthState.Error(errString.toString()))
                }

                override fun onAuthenticationFailed() {
                    callback(BiometricAuthState.Failed)
                }
            }
        )

        biometricPrompt.authenticate(promptInfo)
    }
}