package com.noteactive.app.features.home.createnote.create_note_screen.action

import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions

sealed interface CreateNoteViewModelAction {
    data class OnTextChange(val text: String) : CreateNoteViewModelAction
    data class OnActiveNoteRemoved(val index: Int) : CreateNoteViewModelAction
    data class OnNoteItemOptionRemoved(
        val index: Int,
        val rowTypeOptions: NewNoteRowTypeOptions,
        val typeTobeRemoved: NewNoteRowTypeOptions?,
        val id: String
    ) : CreateNoteViewModelAction
}