package com.noteactive.app.features.home.createnote.select_active_note_screen.state

import com.noteactive.app.R

data class ActiveNoteCollection(
    val title: String,
    val activeNotes: List<ActiveNote>
)

data class ActiveNote(
    val id: Int,
    val image: Int,
    val name: String,
)

data class SelectActiveNoteState(
    val activeNoteCollection : List<ActiveNoteCollection>,
    val filteredActiveNote: List<ActiveNoteCollection>,
    val searchText: String = "",
    val isSearchEnabled: Boolean = false
)

fun getActiveNoteCollection() = listOf(
    ActiveNoteCollection(
        "Suggested Active Note", listOf(
            ActiveNote(
                1,
                R.drawable.ic_notes, "Chow Cart 1"
            ),
            ActiveNote(2, R.drawable.ic_notes, "Chow Cart 2")
        )
    ),
    ActiveNoteCollection(
        "Active Note", listOf(
            ActiveNote(
                1,
                R.drawable.ic_search, "Medication"
            ),
            ActiveNote(
                2,
                R.drawable.ic_search, "Visitor"
            ),
            ActiveNote(
                3,
                R.drawable.ic_search, "Fire"
            ),
            ActiveNote(
                4,
                R.drawable.ic_search, "Laundry"
            ),
            ActiveNote(
                4,
                R.drawable.ic_search, "Chow Cart 3"
            ),ActiveNote(
                4,
                R.drawable.ic_search, "Chow Cart 4"
            ),ActiveNote(
                4,
                R.drawable.ic_search, "Chow Cart 5"
            ),
        )
    ),
)