package com.noteactive.app.features.authenticator.presentation.biometric

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.authenticator.data.repositoryimpl.AuthenticatorRepositoryImpl
import com.noteactive.app.features.authenticator.domain.state.BiometricAuthState
import com.noteactive.app.features.authenticator.presentation.signature.SignatureScreenBottomSheet

@Composable
fun BiometricScreenRoot() {
    val viewModel: BiometricViewModel = hiltViewModel()

    val context = LocalContext.current

    val activity = context as? FragmentActivity

    val state by viewModel.authState

    BiometricScreen(state) {
        activity?.let { viewModel.authenticate(it) }
    }
}

@Composable
private fun BiometricScreen(
    state: BiometricAuthState,
    startBioMetricAction: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        val showSignatureBottomSheet = remember { mutableStateOf(false) }
        Text("Biometric Authentication", style = MaterialTheme.typography.headlineSmall)
        Spacer(modifier = Modifier.height(16.dp))

        Button(onClick = {
            startBioMetricAction.invoke()
        }) {
            Text("Authenticate")
        }
        when (state) {
            is BiometricAuthState.Success -> Text("Authentication succeeded ")
            is BiometricAuthState.Error -> Text("Error: ${(state as BiometricAuthState.Error).message}")
            is BiometricAuthState.Failed -> Text("Authentication failed ")
            else -> {}
        }

        Text(
            "Signature Authentication",
            modifier = Modifier.padding(8.dp),
            style = MaterialTheme.typography.headlineSmall
        )

        Button(onClick = {
            showSignatureBottomSheet.value = true
        }) {
            Text("Signature")
        }
        if (showSignatureBottomSheet.value) {
            SignatureScreenBottomSheet {
                showSignatureBottomSheet.value = false
            }
        }
        Spacer(modifier = Modifier.height(16.dp))

    }
}

@PreviewScreenSizes
@Preview
@Composable
private fun BiometricScreenPreview() {
    MyAppTheme {
        val context = LocalContext.current
        val mockViewModel = object : BiometricViewModel(AuthenticatorRepositoryImpl(context)) {

        }
        BiometricScreen(mockViewModel.authState.value) {}
    }
}