package com.noteactive.app.features.login.data.repositoryimpl

import com.noteactive.app.core.data.repositoryImpl.BaseRepositoryImpl
import com.noteactive.app.core.domain.entity.location.DeviceLocation
import com.noteactive.app.core.util.preferences.AppPreferences
import com.noteactive.app.core.util.network.ApiResponse
import com.noteactive.app.core.util.network.NetworkBoundResource
import com.noteactive.app.core.util.network.Resource
import com.noteactive.app.features.login.data.model.dao.activation_key_dao.ActivationKeyResponseDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.FacilityDataSaveSource
import com.noteactive.app.features.login.data.model.entity.request.EnterActivationKeyRequest
import com.noteactive.app.core.data.model_entity_with_dao.request.FacilityApiRequest
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponse
import com.noteactive.app.features.login.data.network.api.LicenceApiService
import com.noteactive.app.features.login.data.network.api.CommonApiService
import com.noteactive.app.features.login.domain.repository.AuthRepository
import com.noteactive.app.core.data.model_entity_with_dao.request.CommonApiRequest
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.FacilityApiResponse
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntrySource
import kotlinx.coroutines.flow.Flow
import retrofit2.Response

class AuthRepositoryImpl(
    private val apiService: LicenceApiService,
    private val commonApiService: CommonApiService,
    private val activationKeyDao: ActivationKeyResponseDao,
    private val facilityDataSaveSource: FacilityDataSaveSource,
    private val userDataSource: UserEntrySource,
    private val appPreferences: AppPreferences,
    private val baseRepositoryImpl: BaseRepositoryImpl
) : AuthRepository {

    override fun isLoggedIn() = appPreferences.isLoggedIn()

    override fun getActivationUserData() = activationKeyDao.getActivationUserData()


    override fun loadAllUsers(request: CommonApiRequest): Flow<Resource<ApiResponse<List<UserEntity>>>> {
        return object : NetworkBoundResource<ApiResponse<List<UserEntity>>>() {
            override suspend fun createCall(): Response<ApiResponse<List<UserEntity>>> =
                commonApiService.users(request)

            override suspend fun saveToDb(result: ApiResponse<List<UserEntity>>) =
                userDataSource.saveUserData(result)
        }.asFlow()
    }

    override suspend fun getLastKnownLocation(): DeviceLocation? =
        baseRepositoryImpl.getLastKnownLocation()

    override suspend fun getAdId(): String = baseRepositoryImpl.getAdId()

    fun enableLoginAfterActivation() = appPreferences.isLoggedIn(true)

    override suspend fun postActivationKey(
        activationRequest: EnterActivationKeyRequest
    ): Flow<Resource<ActivationKeyResponse>> {
        return object : NetworkBoundResource<ActivationKeyResponse>() {
            override suspend fun createCall(): Response<ActivationKeyResponse> =
                apiService.postActivationKey(
                    activationKey = activationRequest.activationKey,
                    phoneDeviceId = activationRequest.phoneDeviceId,
                )

            override suspend fun saveToDb(response: ActivationKeyResponse) {
                val facilityId = response.result.first().facilities.split(",")[0]
                appPreferences.singleFacilityIdToLoadFirstTimeData = facilityId
                activationKeyDao.replaceActivationKeys(response.result)
            }

        }.asFlow()
    }


    override suspend fun getFacilities(getFacilityRequest: FacilityApiRequest): Flow<Resource<ApiResponse<List<FacilityApiResponse>>>> {
        return object : NetworkBoundResource<ApiResponse<List<FacilityApiResponse>>>() {
            override suspend fun createCall(): Response<ApiResponse<List<FacilityApiResponse>>> =
                commonApiService.facilities(getFacilityRequest)

            override suspend fun saveToDb(result: ApiResponse<List<FacilityApiResponse>>) {
                val facilityResponse = result.data ?: return
                saveFacilityInfoToDatabase(facilityResponse)
                enableLoginAfterActivation()
                if (facilityResponse.size == 1) {
                    appPreferences.selectedCustomer = facilityResponse.firstOrNull()?.customerKey
                }
                appPreferences.accessToken = result.token
            }

        }.asFlow()
    }

    private suspend fun saveFacilityInfoToDatabase(facilityResponse: List<FacilityApiResponse>) =
        facilityDataSaveSource.saveFacilityData(facilityResponse)
}
