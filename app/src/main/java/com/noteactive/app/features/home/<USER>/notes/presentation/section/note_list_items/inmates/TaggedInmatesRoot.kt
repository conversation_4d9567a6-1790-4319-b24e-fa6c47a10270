package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.inmates

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteChip
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon
import com.noteactive.app.features.home.shownotes.notes.presentation.section.numberOfHiddenItemsCountGradient

@Composable
fun TaggedInmatesRoot(
    noteHeader: NoteHeader, noteContent: NoteContent, modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {

        NoteItemIcon(imageId = R.drawable.ic_bottom_nav_offender)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())
        Column(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            if (noteHeader.expanded) {
                noteContent.inmates.forEach { inmate ->
                    NoteChip(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 4.dp, vertical = 2.5.dp), text = inmate
                    )
                }
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    NoteChip(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 4.dp, vertical = 2.5.dp),
                        text = noteContent.inmates.first()
                    )

                    if (noteContent.nonVisibleInmateCount > 0) {
                        Box(
                            modifier = Modifier
                                .background(brush = numberOfHiddenItemsCountGradient)
                                .fillMaxHeight()
                                .padding(
                                    horizontal = 26.dp
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                textAlign = TextAlign.Center,
                                text = "+${noteContent.nonVisibleInmateCount}",
                                style = MaterialTheme.typography.bodyMedium,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }

                    }

                }

            }
        }

    }
}

@Preview
@Composable
fun TaggedInmatesRootPreview() {
    TaggedInmatesRoot(
        noteHeader = NoteHeader(time = "1", expanded = true), noteContent = NoteContent(
            inmates = listOf(
                "Ahmed sidqui, 110091, 4F Demo and Training",
                "Karan Gaur, 110491, 2F Demo and Training",
                "Sonal Gaur, 110491, 2F Demo and Training"
            ), type = NoteItemType.TAGGED_INMATE
        ), modifier = Modifier
    )
}

@Preview
@Composable
fun TaggedInmatesRootPreview2() {
    TaggedInmatesRoot(
        noteHeader = NoteHeader(time = "1", expanded = false), noteContent = NoteContent(
            inmates = listOf(
                "Ahmed sidqui, 110091, 4F Demo and Training",
                "Karan Gaur, 110491, 2F Demo and Training",
                "Sonal Gaur, 110491, 2F Demo and Training"
            ), type = NoteItemType.TAGGED_INMATE
        ), modifier = Modifier
    )
}
@PreviewScreenSizes
@Preview
@Composable
fun TaggedInmatesRootPreview3() {
    TaggedInmatesRoot(
        noteHeader = NoteHeader(time = "1", expanded = false), noteContent = NoteContent(
            inmates = listOf(
                "Ahmed sidqui, 110091, 4F Demo and Training",
                "Karan Gaur, 110491, 2F Demo and Training",
                "Sonal Gaur, 110491, 2F Demo and Training"
            ), type = NoteItemType.TAGGED_INMATE
        ), modifier = Modifier
    )
}

