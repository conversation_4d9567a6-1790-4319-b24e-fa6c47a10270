package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.document_info

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon

@Composable
fun DocumentsInfo(modifier: Modifier = Modifier, profile: ClientProfile) {

    Row(
        modifier = modifier
            .heightIn(min = 40.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {

        NoteItemIcon(modifier = Modifier.width(40.dp), imageId = R.drawable.ic_note_test1,isNotesScreen = false)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight().padding(end = 8.dp))

        repeat(3) {
            Box(
                modifier = Modifier
                    .padding(vertical = 2.dp, horizontal = 4.dp)
                    .size(28.dp)
                    .border(
                        BorderStroke(width = 1.dp, color = Color(0xFFE1E6EA)),
                        RoundedCornerShape(4.dp)  // Add shape to border
                    )
                    .clip(RoundedCornerShape(4.dp))  // Clip after border
                , contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = when (it) {
                        0 -> painterResource(R.drawable.ic_notes)
                        1 -> painterResource(R.drawable.ic_bottom_nav_more)
                        else -> painterResource(R.drawable.ic_recreation)
                    },
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = Color(0xFF6B7280)
                )
            }
        }

    }

}

@PreviewScreenSizes
@Composable
fun DocumentsInfoPreview() {
    DocumentsInfo(profile = ClientProfile())
}