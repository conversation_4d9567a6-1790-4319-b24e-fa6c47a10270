package com.noteactive.app.features.login.presentation.screens.select_customer_screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.action.SelectCustomerViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.ChangeCustomerScreenState
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectCustomerNavigationAction

@Composable
fun SelectCustomerScreenRoot(
    modifier: Modifier = Modifier,
    selectFacilityNavigation: (SelectCustomerNavigationAction) -> Unit
) {
    val viewModel: SelectedCustomerScreenViewModel = hiltViewModel()
    val selectFacilityState by viewModel.selectCustomerState.collectAsStateWithLifecycle()
    SelectCustomerScreen(
        modifier = modifier,
        customerState = selectFacilityState,
        action = viewModel::onAction,
        navAction = selectFacilityNavigation
    )
}

@Composable
fun SelectCustomerScreen(
    modifier: Modifier = Modifier,
    customerState: ChangeCustomerScreenState,
    action: (SelectCustomerViewModelAction) -> Unit,
    navAction: (SelectCustomerNavigationAction) -> Unit
) {


    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            ToolbarSearchView(
                title = "Select Facility",
                hint = "Select Facility",
                isSearchEnabled = customerState.isSearchEnabled,
                searchText = customerState.searchText,
                onSearchTextChanged = {
                    action(SelectCustomerViewModelAction.OnSearchCustomerTextAction(it))
                },
                onSearchCrossClicked = {
                    action(SelectCustomerViewModelAction.OnSearchCrossClicked)
                },
                onSearchEnabledClicked = {
                    action(SelectCustomerViewModelAction.OnEnableSearch)
                },
                onBackIconClicked = {
                    navAction(SelectCustomerNavigationAction.NavigateBack)

                }
            )
        }
    ) { innerPadding ->
        LazyColumn(
            modifier = Modifier.padding(innerPadding)
        ) {
            items(customerState.filteredList) { customer ->
                Row(
                    Modifier
                        .background(if (customer.isSelected) Color(0xFFFEEFE7) else Color.White)
                        .clickable {
                            action(SelectCustomerViewModelAction.OnCustomerSelectAction(customer))
                            navAction.invoke(
                                SelectCustomerNavigationAction.NavigateBackWithCustomer(customer)
                            )
                        }
                ) {
                    Text(
                        text = customer.customerName,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        style = MaterialTheme.typography.titleMedium
                    )
                }
                HorizontalDivider()
            }
        }
    }
}


@PreviewScreenSizes
@Preview
@Composable
fun SelectFacilityScreenPreview() {
    MyAppTheme {
        SelectCustomerScreen(
            customerState = ChangeCustomerScreenState(
                allCustomerList = mutableListOf(), filteredList = mutableListOf(), searchText = "2"
            ), action = {}, navAction = {}
        )
    }

}