package com.noteactive.app.features.common.data.repositoryimpl

import android.os.Build
import androidx.annotation.RequiresApi
import com.noteactive.app.features.common.domain.repository.DateTimeRangePickerRepository
import java.time.LocalDateTime
import java.time.ZoneId
import javax.inject.Inject

class DateTimeRangePickerRepositoryImpl @Inject constructor(): DateTimeRangePickerRepository {
    @RequiresApi(Build.VERSION_CODES.O)
    override fun isDateSelectable(millis: Long): Boolean {
        val now = LocalDateTime.now()
        val startMillis = now.minusDays(30).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val endMillis = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        return millis in startMillis..endMillis

    }
}