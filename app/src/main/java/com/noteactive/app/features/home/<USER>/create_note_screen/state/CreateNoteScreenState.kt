package com.noteactive.app.features.home.createnote.create_note_screen.state

import androidx.compose.ui.graphics.Color
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.select_facility.state.SelectFacility
import com.noteactive.app.features.home.createnote.select_location.state.SelectLocation
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.Offender
import com.noteactive.app.features.home.createnote.select_user.state.SelectUser

data class CreateNoteScreenState(
    val title: String,
    val date: String,
    val time: String,
)


data class NewNoteRow(
    val typeOptions: NewNoteRowTypeOptions,
    val activeNoteName: String = "",
    var text: String = "",
    var activeNoteDetails: String = "",
    var listOfInmates: MutableList<Offender> = mutableListOf(),
    var listOfAttachments: MutableList<String> = mutableListOf(),
    var facilities: MutableList<SelectFacility> = mutableListOf(),
    var locations: MutableList<SelectLocation> = mutableListOf(),
    var listOfUsers: MutableList<SelectUser> = mutableListOf(),
    var icon: Int? = null,
)

enum class NewNoteRowTypeOptions {
    TEXT, LOCATION, BUILDING, INMATE, ATTACHMENTS, ACTIVE_NOTE, USERS, FACILITIES, NONE
}

fun getNewNoteItem() = listOf(
    NewNoteRow(
        typeOptions = NewNoteRowTypeOptions.ACTIVE_NOTE,
        listOfAttachments = mutableListOf("File 1.jpg", "File 2.jpg"),
        facilities = mutableListOf(
            SelectFacility(
                "Dorm-L-B-Demo", "1", false,
                tint = Color(0xFF13B4EA),
                background = Color(0xFFE7F9FF)
            ),
            SelectFacility(
                "Building Test", "2", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            ),
            SelectFacility(
                "Building C", "3", false,
                tint = Color(0xFFFF6900),
                background = Color(0xFFFFF3EB)
            ),
            SelectFacility(
                "Block A", "4", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            )
        ),
        activeNoteName = "Fire Active Note",
        activeNoteDetails = "Dorm-4F Demo and Training | Bed Location 1 | Comments Maintenance",
        icon = R.drawable.ic_fire
    ),   NewNoteRow(
        typeOptions = NewNoteRowTypeOptions.ACTIVE_NOTE,
        listOfAttachments = mutableListOf("File 1.jpg", "File 2.jpg"),
        facilities = mutableListOf(
            SelectFacility(
                "Dorm-L-B-Demo", "1", false,
                tint = Color(0xFF13B4EA),
                background = Color(0xFFE7F9FF)
            ),
            SelectFacility(
                "Building Test", "2", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            ),
            SelectFacility(
                "Building C", "3", false,
                tint = Color(0xFFFF6900),
                background = Color(0xFFFFF3EB)
            ),
            SelectFacility(
                "Block A", "4", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            )
        ),
        activeNoteName = "Fire Active Note",
        activeNoteDetails = "Dorm-4F Demo and Training | Bed Location 1 | Comments Maintenance",
        icon = R.drawable.ic_fire
    ),   NewNoteRow(
        typeOptions = NewNoteRowTypeOptions.ACTIVE_NOTE,
        listOfAttachments = mutableListOf("File 1.jpg", "File 2.jpg"),
        facilities = mutableListOf(
            SelectFacility(
                "Dorm-L-B-Demo", "1", false,
                tint = Color(0xFF13B4EA),
                background = Color(0xFFE7F9FF)
            ),
            SelectFacility(
                "Building Test", "2", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            ),
            SelectFacility(
                "Building C", "3", false,
                tint = Color(0xFFFF6900),
                background = Color(0xFFFFF3EB)
            ),
            SelectFacility(
                "Block A", "4", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            )
        ),
        activeNoteName = "Fire Active Note",
        activeNoteDetails = "Dorm-4F Demo and Training | Bed Location 1 | Comments Maintenance",
        icon = R.drawable.ic_fire
    ),   NewNoteRow(
        typeOptions = NewNoteRowTypeOptions.ACTIVE_NOTE,
        listOfAttachments = mutableListOf("File 1.jpg", "File 2.jpg"),
        facilities = mutableListOf(
            SelectFacility(
                "Dorm-L-B-Demo", "1", false,
                tint = Color(0xFF13B4EA),
                background = Color(0xFFE7F9FF)
            ),
            SelectFacility(
                "Building Test", "2", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            ),
            SelectFacility(
                "Building C", "3", false,
                tint = Color(0xFFFF6900),
                background = Color(0xFFFFF3EB)
            ),
            SelectFacility(
                "Block A", "4", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            )
        ),
        activeNoteName = "Fire Active Note",
        activeNoteDetails = "Dorm-4F Demo and Training | Bed Location 1 | Comments Maintenance",
        icon = R.drawable.ic_fire
    ), NewNoteRow(
        typeOptions = NewNoteRowTypeOptions.ACTIVE_NOTE,
        listOfAttachments = mutableListOf("File 1.jpg", "File 2.jpg"),
        listOfUsers = mutableListOf(
            SelectUser(
                "Ahmed Jasmin",
                "1", false,
                tint = Color(0xFF13B4EA),
                background = Color(0xFFE7F9FF)
            ),
            SelectUser(
                "Akarsh ", "2", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            ),
            SelectUser(
                "Test User ", "3", false,
                tint = Color(0xFF1ECA86),
                background = Color(0xFFDCFFF1)
            )
        ),
        activeNoteName = "Chow call",
        activeNoteDetails = "Dorm-4F Demo and Training | Bed Location 1 | Comments Maintenance",
        icon = R.drawable.ic_note_test4
    ), NewNoteRow(
        typeOptions = NewNoteRowTypeOptions.TEXT,
        activeNoteName = "",
    )
)
