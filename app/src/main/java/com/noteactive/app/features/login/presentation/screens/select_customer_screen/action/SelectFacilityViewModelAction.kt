package com.noteactive.app.features.login.presentation.screens.select_customer_screen.action

import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.CustomerItemUi
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi

sealed interface SelectCustomerViewModelAction {

    data object OnSubmitClickedAction : SelectCustomerViewModelAction
    data class OnCustomerSelectAction(val facility: CustomerItemUi) : SelectCustomerViewModelAction
    data class OnSearchCustomerTextAction(var searchText: String) : SelectCustomerViewModelAction
    data object OnEnableSearch : SelectCustomerViewModelAction
    data object OnSearchCrossClicked : SelectCustomerViewModelAction

}