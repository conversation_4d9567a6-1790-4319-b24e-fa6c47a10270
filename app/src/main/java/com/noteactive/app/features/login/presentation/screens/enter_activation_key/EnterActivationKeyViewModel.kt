package com.noteactive.app.features.login.presentation.screens.enter_activation_key

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.core.util.network.Resource
import com.noteactive.app.features.login.data.model.entity.request.EnterActivationKeyRequest
import com.noteactive.app.features.login.domain.repository.AuthRepository
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyViewModelAction
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyApiState
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class EnterActivationKeyViewModel @Inject constructor(
    private val authRepository: AuthRepository,
) : ViewModel() {

    private val _activationKeyState = MutableStateFlow(ActivationKeyState())
    val activationKeyState = _activationKeyState.asStateFlow()

    private val _apiState = MutableStateFlow<ActivationKeyApiState>(ActivationKeyApiState.Idle)
    val apiState: StateFlow<ActivationKeyApiState> = _apiState

    fun onAction(action: EnterActivationKeyViewModelAction) {
        when (action) {
            EnterActivationKeyViewModelAction.OnSubmit -> {
                fetchUserData()
            }

            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                _activationKeyState.update {
                    it.copy(
                        text = action.value,
                        isError = false,
                        isButtonEnabled = action.value.isNotBlank()
                    )
                }
            }

            EnterActivationKeyViewModelAction.OnError -> {
                _activationKeyState.update {
                    it.copy(
                        isError = true, isButtonEnabled = false
                    )
                }
            }
        }
    }


    private fun fetchUserData() {
        viewModelScope.launch {
            val request = EnterActivationKeyRequest(
                activationKey = _activationKeyState.value.text,
                phoneDeviceId = authRepository.getAdId(),
            )
            authRepository.postActivationKey(request).collect {
                when (it) {
                    is Resource.Loading ->
                        _apiState.value = ActivationKeyApiState.Loading

                    is Resource.Success ->
                        it.data?.let {
                            _apiState.value = ActivationKeyApiState.Success(it.result.first())
                        }
                    is Resource.Error ->
                        _apiState.value = ActivationKeyApiState.Error(it.message.toString())
                }
            }
        }
    }

}