package com.noteactive.app.features.authenticator.presentation.signature

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.composable.button.SecondaryButton
import com.noteactive.app.core.presentation.designsystem.composable.signature.SignatureView
import com.noteactive.app.core.presentation.designsystem.composable.signature.rememberSignatureController
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextSubTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import kotlinx.coroutines.CoroutineScope
import androidx.compose.ui.graphics.asAndroidBitmap
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsNavigationAction


@Composable
fun SignatureScreenBottomSheet(modifier: Modifier = Modifier, dismissBottomSheet: () -> Unit) {
    val sheetState = rememberModalBottomSheetState()
    val showSheet = remember {
        mutableStateOf(true)
    }
    SignatureScreen(
        showSheet = showSheet.value,
        sheetState = sheetState,
        modifier = modifier
    ) {
        showSheet.value = false
        dismissBottomSheet.invoke()
    }

}

@Composable
fun SignatureScreen(
    modifier: Modifier = Modifier,
    showSheet: Boolean,
    sheetState: SheetState,
    dismissBottomSheet: () -> Unit
) {
    if (showSheet) {
        LaunchedEffect(Unit) {
            sheetState.show() // <-- This expands it
        }
        ModalBottomSheet(
            onDismissRequest = dismissBottomSheet,
            sheetState = sheetState,
            containerColor = Color.White,
            scrimColor = Color.Black.copy(alpha = 0.32f),
            modifier = modifier
        ) {
            BottomSheetSignatureUI()
        }
    }
}


@Composable
private fun BottomSheetSignatureUI(
    onSignatureCaptured: (Bitmap) -> Unit = {}
) {
    val signatureController = rememberSignatureController()

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header
        Image(
            painter = painterResource(R.drawable.ic_signature_bottom_sheet_header_icon),
            contentDescription = null
        )
        Text(
            "User verification",
            style = MaterialTheme.typography.titleSmall.copy(
                fontSize = 18.sp,
                color = FocusTextTitleColor
            )
        )
        Text(
            "You must have signature authentication.\n Sign below",
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium.copy(color = FocusTextSubTitleColor)
        )

        SignatureView(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 24.dp, horizontal = 16.dp)
                .height(115.dp)
                .border(1.dp, MaterialTheme.colorScheme.outline),
            signatureController = signatureController
        )

        MultiButtonBottomRow(
            secondaryButtonTitle = R.string.close,
            primaryButtonTitle = R.string.verify,
            secondaryButtonClickListener = {
                signatureController.clear()
            },
            primaryButtonClickListener = {
                val bitmap = signatureController
                    .toImageBitmap()
                    .asAndroidBitmap()
                onSignatureCaptured(bitmap)
            })

    }
}

@PreviewScreenSizes
@Preview
@Composable
fun BottomSheetSignatureScreenRootPreview(modifier: Modifier = Modifier) {
    MyAppTheme {
        BottomSheetSignatureUI()
    }
}