package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.comment

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.shownotes.notes.data.model.Note
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteBoldTextStyle
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteTextStyle
import java.util.UUID

@Composable
fun NoteCommentRoot(
    noteHeader: NoteHeader,
    noteContent: NoteContent,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        NoteItemIcon(imageId = R.drawable.ic_note_test6)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())
        CommentSection(modifier, noteContent, noteHeader)
    }
}

@Composable
private fun CommentSection(modifier: Modifier, noteContent: NoteContent, noteHeader: NoteHeader) {
    Column(
        modifier = modifier.padding(vertical = 8.dp, horizontal = 4.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        val title = buildAnnotatedString {
            withStyle(
                getNoteBoldTextStyle()
            ) {
                if (noteHeader.expanded)
                    append("${noteContent.comment.size} Comments: ")
            }
            withStyle(
                getNoteTextStyle()
            ) {
                append("Late entry  Sachin Rathi (12/04/2024)")
            }
//            withStyle(SpanStyle(color = Color.Blue)) { append("World!") }
        }
        Text(
            title,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        if (noteHeader.expanded) {
            noteContent.comment.forEach {
                Row {
                    Image(
                        painter = painterResource(R.drawable.ic_note_test1),
                        contentDescription = null,
                        Modifier.padding(start = 16.dp, end = 16.dp)
                    )
                    val title = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color.Black, fontSize = 14.sp, fontWeight = FontWeight.Bold

                            )
                        ) {
                            append("Post Acceptance:: ")
                        }
                        append(it)
                    }
                    Text(
                        title, style = MaterialTheme.typography.titleMedium
                    )
                }
            }

        }
    }
}

@PreviewScreenSizes
@Preview
@Composable
fun CommentSectionPreview(modifier: Modifier = Modifier) {
    val note = Note(
        header = NoteHeader("12:27", id = UUID.randomUUID().hashCode()), listOf(
            NoteContent(
                files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                comment = listOf(
                    "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024",
                    "Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024"
                ),
                type = NoteItemType.COMMENTS
            )
        )
    )
    MaterialTheme {
        NoteCommentRoot(
            modifier = Modifier, noteContent = note.content[0], noteHeader = note.header
        )
    }
}