package com.noteactive.app.features.common.di

import com.noteactive.app.features.common.data.repositoryimpl.DateTimeRangePickerRepositoryImpl
import com.noteactive.app.features.common.domain.repository.DateTimeRangePickerRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object CommonModule {

    @Provides
    fun getDateTimeRangePicker() :DateTimeRangePickerRepository {
        return DateTimeRangePickerRepositoryImpl()
    }
}