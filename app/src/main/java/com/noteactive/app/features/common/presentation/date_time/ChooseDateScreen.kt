package com.noteactive.app.features.common.presentation.date_time

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.common.data.repositoryimpl.DateTimeRangePickerRepositoryImpl

@Composable
fun ChooseDateScreen(
    onAction: (DateTimeSelectionNavigationAction) -> Unit,
) {
    val viewModel: ChooseDateScreenViewModel = hiltViewModel()
    DateScreen(viewModel, onAction::invoke)
}

@Composable
private fun DateScreen(
    viewModel: ChooseDateScreenViewModel,
    onAction: (DateTimeSelectionNavigationAction) -> Unit
) {
    val customColors = DatePickerDefaults.colors(
        containerColor = Color.White,
        weekdayContentColor = Color.Black,
        selectedDayContainerColor = MaterialTheme.colorScheme.primary,
        selectedDayContentColor = Color.White,
        disabledSelectedDayContainerColor = MaterialTheme.colorScheme.onSurface,
        todayContentColor = Color.Black,
        dayContentColor = Color.Gray
    )

    val datePickerState = rememberDateRangePickerState(
        initialDisplayedMonthMillis = System.currentTimeMillis(),
        yearRange = 2023..2026,
        selectableDates = object : SelectableDates {
            override fun isSelectableDate(utcTimeMillis: Long) =
                viewModel.isSelectable(utcTimeMillis)
        }
    )

    // Internal state to track the single selected date
    var selectedDateMillis by remember { mutableStateOf<Long?>(null) }

    // Enforce single-date selection
    LaunchedEffect(datePickerState.selectedStartDateMillis, datePickerState.selectedEndDateMillis) {
        val currentStart = datePickerState.selectedStartDateMillis
        val currentEnd = datePickerState.selectedEndDateMillis

        if (currentStart != null && currentStart != selectedDateMillis) {
            selectedDateMillis = currentStart
            datePickerState.setSelection(
                startDateMillis = currentStart,
                endDateMillis = currentStart  // Set end to same as start for single date
            )

//           onAction.invoke(DateTimeSelectionNavigationAction.OnChooseTimeComplete)
            
        } else if (currentEnd != null && currentEnd != selectedDateMillis && currentEnd != currentStart) {
            // If user tries to select an end date, override to make it the new single date
            selectedDateMillis = currentEnd
            datePickerState.setSelection(
                startDateMillis = currentEnd,
                endDateMillis = currentEnd
            )
        }
    }

    Column {
        Box(modifier = Modifier.weight(1f)) {
            DateRangePicker(
                state = datePickerState,
                modifier = Modifier,
                colors = customColors,
                showModeToggle = false
            )
            AppCommonToolbar(
                title = stringResource(R.string.select_date_title),
                modifier = Modifier.shadow(2.dp),
                onBackIconClicked = {
                    onAction.invoke(DateTimeSelectionNavigationAction.OnDismiss)
                },
            )
        }
        MultiButtonBottomRow(
            secondaryButtonTitle = R.string.cancel,
            primaryButtonTitle = R.string.submit,
            secondaryButtonClickListener = {
                onAction.invoke(DateTimeSelectionNavigationAction.OnDismiss)
            },
            primaryButtonClickListener = {
                // Pass the selected date (use selectedDateMillis here)
                // For example: onAction.invoke(DateTimeSelectionNavigationAction.OnSubmit(selectedDateMillis))
            },
            isSecondaryButtonEnabled = true,
            isPrimaryButtonEnabled = selectedDateMillis != null  // Enable submit only if a date is selected
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DateRangePickerScreenPreview() {
    val mockViewModel = object : ChooseDateScreenViewModel(DateTimeRangePickerRepositoryImpl()) {
        override fun isSelectable(millis: Long): Boolean = true
    }
    MyAppTheme {
        DateScreen(
            mockViewModel,
            onAction = {}
        )
    }
}