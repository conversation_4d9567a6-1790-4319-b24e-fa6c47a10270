package com.noteactive.app.features.home.inmate.inmate_details_screen.section.personalInfo

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AssistChip
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.InmateDetailsState

@Composable
fun PersonalInfoTabContent(inmateDetailsState: InmateDetailsState) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        DetailsRow(stringResource(R.string.name), inmateDetailsState.name, stringResource(R.string.gender), inmateDetailsState.gender)
        DetailsRow(stringResource(R.string.dob), inmateDetailsState.dob, stringResource(R.string.id), inmateDetailsState.id)
        DetailsRow(stringResource(R.string.booking_id), inmateDetailsState.bookingId, stringResource(R.string.ccn), inmateDetailsState.ccn)
        DetailsRow(stringResource(R.string.demo), inmateDetailsState.facility, stringResource(R.string.location), inmateDetailsState.location)
        Text(text = inmateDetailsState.status, style = MaterialTheme.typography.labelMedium.copy(color = Color(0xFF999999)))
        AssistChip(
            onClick = {},
            label = { Text(text = "Cancelled program, 12 sep 2024, 14:00", style = MaterialTheme.typography.labelMedium.copy(Color(0XFFCC1400))) },
            leadingIcon = { Icon(
                    painter = painterResource(R.drawable.ic_note_test1),
                    contentDescription = "",
                tint = Color(0XFFCC1400),
                    modifier = Modifier.size(16.dp)
                ) },
            border = BorderStroke(width = 1.dp, color = Color(0XFFFFECEA)),
        )
        PrimaryButton(text = stringResource(R.string.edit), modifier = Modifier.padding(top = 16.dp), buttonClick = {})
    }
}
@Composable
fun DetailsRow(label1: String, value1: String, label2: String, value2: String) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(text = label1, style = MaterialTheme.typography.labelMedium.copy(color = Color(0xFF999999)))
            Text(text = value1, style = MaterialTheme.typography.labelMedium)
        }
        Column(modifier = Modifier.weight(1f)) {
            Text(text = label2, style = MaterialTheme.typography.labelMedium.copy(color = Color(0xFF999999)))
            Text(text = value2, style = MaterialTheme.typography.labelMedium)
        }
    }
}

@PreviewScreenSizes
@Composable
private fun PersonalInfoTabContentPreview() {
    MyAppTheme {
        PersonalInfoTabContent(inmateDetailsState = InmateDetailsState())
    } }