package com.noteactive.app.features.home.createnote.select_facility

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.createnote.select_facility.action.NoteSelectFacilityScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_facility.state.NoteSelectFacilityState
import com.noteactive.app.features.home.createnote.select_facility.state.SelectFacility
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class NoteSelectFacilityScreenViewModel @Inject constructor() : ViewModel() {
    private val _noteSelectFacilityScreenState = MutableStateFlow(NoteSelectFacilityState())
    val noteSelectFacilityScreenState = _noteSelectFacilityScreenState.asStateFlow()

    fun action(viewModelAction: NoteSelectFacilityScreenViewModelAction) {
        when (viewModelAction) {

            NoteSelectFacilityScreenViewModelAction.OnEnableSearch -> {
                _noteSelectFacilityScreenState.update { it.copy(isSearchEnabled = true) }
            }

            NoteSelectFacilityScreenViewModelAction.OnSearchCrossClicked -> {
                val selectedCount = _noteSelectFacilityScreenState.value.facilities.count { it.isSelected }
                val countLabel = if (selectedCount > 0) "$selectedCount facility" else "Select facility"

                _noteSelectFacilityScreenState.update {
                    it.copy(
                        isSearchEnabled = false,
                        searchText = "",
                        filteredFacilities = it.facilities
                    )
                }
            }

            is NoteSelectFacilityScreenViewModelAction.OnSearchTextChanged -> {
                val searchText = viewModelAction.text
                val filteredList = _noteSelectFacilityScreenState.value.facilities.filter {
                    it.facilityName.contains(searchText, ignoreCase = true)
                }
                val selectedCount = filteredList.count { it.isSelected }
                val countLabel = if (selectedCount > 0) "$selectedCount facility" else "Select facility"

                _noteSelectFacilityScreenState.update {
                    it.copy(
                        searchText = searchText,
                        filteredFacilities = filteredList,
                        count = countLabel
                    )
                }
            }

            is NoteSelectFacilityScreenViewModelAction.OnSelectToggle -> {
                val currentState = _noteSelectFacilityScreenState.value
                val updatedList = currentState.filteredFacilities.mapIndexed { index, facility ->
                    if (index == viewModelAction.index) {
                        facility.copy(isSelected = !facility.isSelected)
                    } else facility
                }
                updateSelectedFacilityState(updatedList)
            }

            NoteSelectFacilityScreenViewModelAction.OnSelectAllToggle -> {
                val currentState = _noteSelectFacilityScreenState.value
                val toggle = !currentState.isSelectAllToggle
                val updatedList = currentState.filteredFacilities.map {
                    it.copy(isSelected = toggle)
                }
                _noteSelectFacilityScreenState.update {
                    it.copy(
                        isSelectAllToggle = toggle
                    )
                }
                updateSelectedFacilityState(updatedList)
            }
        }
    }

    private fun updateSelectedFacilityState(updatedFilteredList: List<SelectFacility>) {

        // Replace matching items in the full list
        val fullList = _noteSelectFacilityScreenState.value.facilities.map { original ->
            updatedFilteredList.find { it.facilityId == original.facilityId } ?: original
        }
        val selectedCount = updatedFilteredList.count { it.isSelected }
        val countLabel = if (selectedCount > 0) "$selectedCount facility" else "Select facility"

        _noteSelectFacilityScreenState.update {
            it.copy(
                facilities = fullList,
                filteredFacilities = updatedFilteredList,
                count = countLabel
            )
        }
    }

}

