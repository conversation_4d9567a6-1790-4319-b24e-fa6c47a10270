package com.noteactive.app.features.home.inmate.inmate_details_screen.section.document

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.FabPosition
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextSubTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NoteRowDivider
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.headerToolbar.CommonHeaderBar
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.DocumentCard
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.InmateDetailsState

@Composable
fun DocTabContent(inmateDetailsState: InmateDetailsState) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = { CommonHeaderBar(
            title = "${inmateDetailsState.document.size} Documents",
            showFilter = true,
            showSort = true,
            showSearch = true,
            modifier = Modifier.background(Color.White).padding(horizontal = 16.dp),
            onSearchChange = {},
            searchText = ""
        ) },
        floatingActionButton = {
            FloatingActionButton(onClick = {},
                shape = CircleShape,
                containerColor = Color.White,
                contentColor = MaterialTheme.colorScheme.primary,
                modifier = Modifier.border(width = 1.dp, color = MaterialTheme.colorScheme.primary, shape = CircleShape),
                elevation = FloatingActionButtonDefaults.elevation(
                    defaultElevation = 0.dp,
                    pressedElevation = 0.dp
                )
                ) {
                Icon(
                    painter = painterResource(R.drawable.ic_add_inmate_details_screen),
                    contentDescription = null
                )
            }
        },
        floatingActionButtonPosition = FabPosition.End
    ) { innerPadding ->
            LazyColumn(
                modifier = Modifier.padding(innerPadding)
                    .background(Color.White)
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
            ) {
                items(inmateDetailsState.document) { card ->
                    DocCard(card)
            }
        }
    }
}

@Composable
fun DocCard(card: DocumentCard) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 12.dp)
            .border(width = 1.dp, color = NoteRowDivider, RoundedCornerShape(4.dp))
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = card.documentType,
                style = MaterialTheme.typography.labelLarge,
                modifier = Modifier.padding(bottom = 2.dp)
            )
            Text(
                text = card.description,
                style = MaterialTheme.typography.labelMedium.copy(color = FocusTextSubTitleColor),
                modifier = Modifier.padding(bottom = 13.dp)
            )
            Row {
                Icon(
                    painter = painterResource(R.drawable.ic_signature_bottom_sheet_header_icon),
                    contentDescription = "",
                    tint = Color.Gray,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = card.nameDateTime,
                    style = MaterialTheme.typography.labelMedium
                )
            }
        }
    }
}

@PreviewScreenSizes
@Composable
private fun DocTabContentPreview() {
    MyAppTheme {
        DocTabContent(inmateDetailsState = InmateDetailsState())
    }
}