package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.text

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteBoldTextStyle
import kotlin.random.Random

@Composable
fun NoteItemTitle(noteHeader: NoteHeader, noteContent: NoteContent, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {

        val title = buildAnnotatedString {
            withStyle(
                style = getNoteBoldTextStyle()

            ) {
                if(noteHeader.expanded){
                    append("Post Acceptance: ")
                }
            }
            withStyle(style = SpanStyle(
                fontSize = 14.sp,
                fontFamily = CustomFontNunitoRegular,
                fontWeight = FontWeight(400),
                color = Color(0xFF2A333C),

                )) {
                append(noteContent.title)
            }
        }

        NoteItemIcon(imageId = R.drawable.ic_note_test1)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())

        noteContent.title?.let {
            val maxLines = if (noteHeader.expanded) {
                Int.MAX_VALUE
            } else {
                1
            }
            Text(
                text = title,
                modifier = Modifier
                    .padding(horizontal = 8.dp, vertical = 4.dp)
                    .background(Color(Random.nextInt()).copy(alpha = 0.1f))
                    .padding(4.dp),
                maxLines = maxLines,
                overflow = TextOverflow.Ellipsis
            )
        }

    }
}

@PreviewScreenSizes
@Preview
@Composable
private fun NoteItemLocation() {
    MaterialTheme {
        NoteItemTitle(
            noteHeader = NoteHeader(time ="1", expanded = false),
            noteContent = NoteContent(
                title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                type = NoteItemType.TITLE
            )
        )
    }
}