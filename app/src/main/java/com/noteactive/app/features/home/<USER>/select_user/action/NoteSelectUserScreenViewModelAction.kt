package com.noteactive.app.features.home.createnote.select_user.action


sealed interface NoteSelectUserScreenViewModelAction {
    data object OnEnableSearch : NoteSelectUserScreenViewModelAction
    data object OnSearchCrossClicked : NoteSelectUserScreenViewModelAction
    data object OnSelectAllToggle : NoteSelectUserScreenViewModelAction
    data class OnSelectToggle(val index: Int) : NoteSelectUserScreenViewModelAction
    data class OnSearchTextChanged(val text: String) : NoteSelectUserScreenViewModelAction
}