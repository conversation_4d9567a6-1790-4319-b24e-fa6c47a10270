package com.noteactive.app.features.common.presentation.date_time

import com.noteactive.app.features.common.presentation.date_picker.actions.DateRangeSelectionNavigationAction

sealed interface DateTimeSelectionNavigationAction {
    data class OnDateTimeComplete(val start: Long, val time: Long) :
        DateTimeSelectionNavigationAction

    data object OnChooseTimeComplete :
        DateTimeSelectionNavigationAction

    object OnDismiss : DateTimeSelectionNavigationAction

}