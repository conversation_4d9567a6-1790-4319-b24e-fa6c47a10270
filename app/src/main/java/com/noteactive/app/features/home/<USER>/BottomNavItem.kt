package com.noteactive.app.features.home.bottom_nav

import com.noteactive.app.R

sealed class BottomNavItem(
    val route: String,
    val icon: Int,
    val label: String
) {
    object Home : BottomNavItem("notes", R.drawable.ic_bottom_nav_notes, "Notes")
    object Search : BottomNavItem("offenders", R.drawable.ic_bottom_nav_offender, "Offenders")
    object Notifications : BottomNavItem("tasks", R.drawable.ic_bottom_nav_task, "Tasks")
    object Profile : BottomNavItem("more", R.drawable.ic_bottom_nav_more, "More")
}


