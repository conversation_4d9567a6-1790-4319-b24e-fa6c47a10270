package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.user.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import javax.inject.Inject

class UserFilter<PERSON>andler @Inject constructor() : FilterHandler {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val users = state.users
        val selectAll = !users.isSelectAllEnable
        val updatedFiltered = users.filteredItems.map { it.copy(isSelected = selectAll) }
        val updatedList = users.listItems.map {
            if (updatedFiltered.any { u -> u.userId == it.userId })
                it.copy(isSelected = selectAll) else it
        }
        return state.copy(
            users = users.copy(
                listItems = updatedList,
                filteredItems = updatedFiltered,
                isSelectAllEnable = selectAll
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val users = state.users
        val updatedFiltered = users.filteredItems.toMutableList()
        val item = updatedFiltered[index]
        val updatedItem = item.copy(isSelected = !item.isSelected)
        updatedFiltered[index] = updatedItem

        val updatedList = users.listItems.map {
            if (it.userId == updatedItem.userId) it.copy(isSelected = updatedItem.isSelected) else it
        }
        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            users = users.copy(
                listItems = updatedList,
                filteredItems = updatedFiltered,
                isSelectAllEnable = allSelected
            )
        )
    }

    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val users = state.users
        val selectedIds =
            (users.listItems + users.filteredItems).filter { it.isSelected }.map { it.userId }
                .toSet()
        val filtered =
            users.listItems.filter { it.userName.contains(searchText, ignoreCase = true) }
                .map { it.copy(isSelected = selectedIds.contains(it.userId)) }
        return state.copy(
            users = users.copy(
                searchText = searchText,
                filteredItems = filtered
            )
        )
    }
}
