package com.noteactive.app.features.login.data.di

import com.noteactive.app.core.util.preferences.AppPreferences
import com.noteactive.app.features.login.data.model.dao.activation_key_dao.ActivationKeyResponseDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.FacilityDataSaveSource
import com.noteactive.app.features.login.data.repositoryimpl.CustomerRepositoryImpl
import com.noteactive.app.features.login.domain.repository.CustomerRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object CustomerRepositoryModule {
    @Provides
    fun provideUserRepository(
        activationKeyDao: ActivationKeyResponseDao,
        dataSource: FacilityDataSaveSource,
        appPreferences: AppPreferences,
    ): CustomerRepository {
        return CustomerRepositoryImpl(
            activationKeyResponseDao = activationKeyDao,
            facilityDataSaveSource = dataSource,
            appPreferences = appPreferences
        )
    }
}
