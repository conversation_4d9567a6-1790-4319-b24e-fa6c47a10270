package com.noteactive.app.features.home.createnote.active_note_form

import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke

/*
@Composable
fun ActiveNoteFormRoot(modifier: Modifier = Modifier) {
    val viewModel: ActiveNoteFormViewModel = hiltViewModel()

}

@PreviewScreenSizes
@Composable
fun ActiveNoteFormPreview(modifier: Modifier = Modifier) {

}*/


import androidx.compose.foundation.*
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.*
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.Surface
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.*
import androidx.compose.ui.*
import androidx.compose.ui.graphics.*
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.painterResource
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesListChipContainerColor
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderTextBlue
import com.noteactive.app.features.home.createnote.active_note_form.sections.form_text_field.FormTextField
import com.noteactive.app.features.home.createnote.active_note_form.sections.header.NotesFormHeader
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon

@Composable
fun FillInfoScreen(modifier: Modifier = Modifier) {
    var incident by remember { mutableStateOf("") }
    var inmate by remember { mutableStateOf("") }
    var location by remember { mutableStateOf("") }
    var comment by remember { mutableStateOf("") }
    var attachments by remember {
        mutableStateOf(listOf("Filename1.jpg", "Filename2.jpg", "Filename3.jpg"))
    }
    var isIncidentDropdownExpanded by remember { mutableStateOf(false) }

    val incidentOptions = listOf("Fighting", "Contraband", "Medical Emergency", "Escape Attempt")


    val incidentOptions2 = listOf(
        "Begins",
        "Ends",
        "Commissary call",
        "Ice call",
        "Property call",
    )

    var selectedOption by remember { mutableStateOf("Commissary call") }
    var dorm by remember { mutableStateOf("") }

    Column(
        modifier = modifier
            .background(Color.White)
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // Header
        NotesFormHeader(headerTitle = "Fill info")

        // Form
        Card(
            shape = RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
            ) {
                incidentOptions2.forEach { option ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                if (selectedOption == option) Color(0xFFFFF3E0) else Color.Transparent,
                                shape = RoundedCornerShape(4.dp)
                            )
                            .padding(vertical = 12.dp)
                            .clickable { selectedOption = option },
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            modifier = Modifier.padding(start = 16.dp),
                            painter = painterResource(getCheckBoxIcon(selectedOption == option)),
                            contentDescription = null
                        )
                        Text(
                            text = option, style = MaterialTheme.typography.bodyMedium.copy(
                                color = Color.Black, fontWeight = FontWeight.W400
                            )
                        )
                    }
                    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
                }

                // Incident Dropdown
                Column(
                    modifier = Modifier.padding(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {


                    Text(
                        "Incident",
                        style = MaterialTheme.typography.bodyMedium.copy(color = Color.Black)
                    )
                    ExposedDropdownMenuBox(
                        expanded = isIncidentDropdownExpanded, onExpandedChange = {
                            isIncidentDropdownExpanded = !isIncidentDropdownExpanded
                        }) {
                        PrimaryTextField(
                            value = incident,
                            onValueChange = {},
                            trailingIcon = {
                                ExposedDropdownMenuDefaults.TrailingIcon(expanded = isIncidentDropdownExpanded)

                            },
                            singleLine = true,
                            hintText = "Select",
                            modifier = Modifier
                                .menuAnchor()
                                .fillMaxWidth()
                        )
                        ExposedDropdownMenu(
                            modifier = Modifier.background(Color.White),
                            expanded = isIncidentDropdownExpanded,
                            onDismissRequest = { isIncidentDropdownExpanded = false }) {
                            incidentOptions.forEach { option ->
                                DropdownMenuItem(text = {
                                    Text(
                                        option, style = MaterialTheme.typography.bodySmall
                                    )
                                }, onClick = {
                                    incident = option
                                    isIncidentDropdownExpanded = false
                                })
                            }
                        }
                    }
                }

                // Inmate
                FormTextField(
                    label = "Inmate",
                    value = inmate,
                    placeholder = "Select inmate",
                    onValueChange = { inmate = it })

                // Location
                FormTextField(
                    label = "Location",
                    value = location,
                    placeholder = "Select location",
                    onValueChange = { location = it })

                // Comment
                Column(
                    modifier = Modifier.padding(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        "Comment",
                        style = MaterialTheme.typography.bodyMedium.copy(color = Color.Black)
                    )
                    Box {
                        OutlinedTextField(
                            value = comment,
                            onValueChange = { if (it.length <= 100) comment = it },
                            placeholder = {
                                Text(
                                    "Write here",
                                    color = MaterialTheme.colorScheme.outline,
                                    style = MaterialTheme.typography.labelLarge
                                )
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(120.dp),
                            maxLines = 6
                        )
                        Text(
                            "${comment.length}/100",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray,
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(8.dp)
                        )
                    }
                }

                // Upload Section
                UploadSection(attachments) { index ->
                    attachments = attachments.toMutableList().apply { removeAt(index) }
                }
            }
        }

    }
}

@Composable
fun UploadSection(
    attachments: List<String>, onRemove: (Int) -> Unit
) {
    Column(modifier = Modifier.padding(12.dp), verticalArrangement = Arrangement.spacedBy(12.dp)) {
        // Upload Box
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .clickable { /* Upload handler */ }
                .drawBehind {
                    val stroke = Stroke(
                        width = 1.dp.toPx(),
                        pathEffect = PathEffect.dashPathEffect(floatArrayOf(20f, 20f))
                    )
                    drawRoundRect(
                        Color.Gray, style = stroke, cornerRadius = CornerRadius(8.dp.toPx())
                    )
                }) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_upload_image),
                    contentDescription = null,
                    modifier = Modifier.size(40.dp)
                )
                Text(
                    "Upload image here",
                    color = NotesScreenHeaderTextBlue,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // Attachments
        if (attachments.isNotEmpty()) {
            LazyRow(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                itemsIndexed(attachments) { index, filename ->
                    AttachmentChip(filename) { onRemove(index) }
                }
            }
        }
    }
}

@Composable
fun AttachmentChip(filename: String, onRemove: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp, vertical = 8.dp)
            .background(NotesListChipContainerColor)
            .border(1.dp, MaterialTheme.colorScheme.onSurface, shape = RoundedCornerShape(4.dp))
            .padding(4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_attachment),
            contentDescription = null,
            tint = Color.Gray,
            modifier = Modifier.size(16.dp)
        )
        Text(filename, style = MaterialTheme.typography.bodySmall, color = Color.Black)
        IconButton(
            onClick = onRemove, modifier = Modifier.size(20.dp)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cross),
                contentDescription = "Remove",
                tint = Color.Gray,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun FillInfoScreenPreview() {
    MyAppTheme {
        FillInfoScreen()
    }
}
