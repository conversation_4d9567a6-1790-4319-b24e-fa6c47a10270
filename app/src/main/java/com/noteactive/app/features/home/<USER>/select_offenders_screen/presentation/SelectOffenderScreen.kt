package com.noteactive.app.features.home.createnote.select_offenders_screen.presentation

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.AssistChip
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.theme.LightColorPalette
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.OffenderItemBackgroundColor
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.createnote.select_offenders_screen.action.SelectOffenderViewModelAction
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.Offender
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.SelectOffenderState

@Composable
fun SelectOffenderScreen(onBackPressed: () -> Unit, selectedOffenders: (List<Offender>) -> Unit) {
    val viewModel: SelectOffenderViewModel = hiltViewModel()
    val state by viewModel.selectOffenderState.collectAsStateWithLifecycle()

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            ToolbarSearchView(
                title = "Offender",
                hint = "Offender",
                isSearchEnabled = state.isSearchEnabled,
                searchText = state.searchText,
                onSearchTextChanged = {
                    viewModel.onAction(
                        SelectOffenderViewModelAction.OnSearchTextChanged(
                            it
                        )
                    )
                },
                onSearchCrossClicked = { viewModel.onAction(SelectOffenderViewModelAction.OnSearchCrossClicked) },
                onSearchEnabledClicked = { viewModel.onAction(SelectOffenderViewModelAction.OnEnableSearch) },
                onBackIconClicked = { onBackPressed.invoke() },
            )
        },
        bottomBar = {
            FooterButtonItem(
                onPrimaryButtonClick = {
                selectedOffenders.invoke(state.selectedOffenders)
            }, onSecondaryButtonClick = {
                onBackPressed.invoke()
            }, isPrimaryButtonEnabled = state.selectedOffenders.isNotEmpty()
            )
        },

        ) { innerPadding ->
        BodyOfSelectOffenderScreen(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(Color.White),
            state = state,
            onViewModelAction = viewModel::onAction
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun BodyOfSelectOffenderScreen(
    modifier: Modifier,
    state: SelectOffenderState,
    onViewModelAction: (SelectOffenderViewModelAction) -> Unit
) {
    LazyColumn(modifier = modifier) {
        stickyHeader {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                IconHelper(
                    imageRes = getCheckBoxIcon(state.isAllChecked),
                    modifier = Modifier
                        .clickable {
                            onViewModelAction(SelectOffenderViewModelAction.OnSelectAllToggle)
                        }
                        .padding(end = 9.dp))

                Text(text = state.headerTitle, style = MaterialTheme.typography.labelLarge)
            }
            HorizontalDivider(color = NotesScreenHeaderDevider)

            LazyRow(
                modifier = Modifier
                    .background(Color.White)
                    .padding(start = 16.dp)
            ) {
                items(state.selectedOffenders) { offender ->
                    SelectedOffenderChip(
                        offender = offender, onRemove = {
                            onViewModelAction(
                                SelectOffenderViewModelAction.OnOffenderItemClicked(
                                    offender
                                )
                            )
                        })
                }
            }
            if (state.selectedOffenders.isNotEmpty()) {
                HorizontalDivider(color = NotesScreenHeaderDevider)
            }
        }
        items(state.filteredListOfOffender) { offenderItem ->
            OffenderItem(
                offender = offenderItem, onClick = {
                    onViewModelAction(
                        SelectOffenderViewModelAction.OnOffenderItemClicked(
                            offenderItem
                        )
                    )
                })
        }
    }
    HorizontalDivider(color = NotesScreenHeaderDevider)
}

@Composable
fun OffenderItem(offender: Offender, onClick: () -> Unit) {
    val backgroundColor = if (offender.isChecked) OffenderItemBackgroundColor else Color.White

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp)

    ) {
        IconHelper(
            imageRes = getCheckBoxIcon(isChecked = offender.isChecked),
            modifier = Modifier.padding(end = 17.dp)
        )

        Image(
            painter = painterResource(offender.image),
            contentDescription = "",
            modifier = Modifier
                .width(32.dp)
                .height(40.dp)
        )
        Column(modifier = Modifier.padding(start = 10.dp)) {
            Text(text = offender.name, style = MaterialTheme.typography.labelLarge)
            Text(
                text = "${offender.id} | ${offender.location}",
                style = MaterialTheme.typography.labelMedium.copy(color = Color(0xFF999999))
            )
        }
    }
    HorizontalDivider(color = NotesScreenHeaderDevider)
}

@Composable
fun SelectedOffenderChip(
    offender: Offender, onRemove: () -> Unit
) {
    AssistChip(
        onClick = {}, label = {
        Text(
            "${offender.name}, ${offender.id}",
            style = MaterialTheme.typography.labelMedium.copy(Color.Black),
            fontWeight = FontWeight.W400
        )
    }, leadingIcon = {
        Image(
            painter = painterResource(id = offender.image),
            contentDescription = "Offender Image",
            modifier = Modifier
                .size(24.dp)
                .clip(CircleShape)
        )
    }, trailingIcon = {
        Icon(
            painter = painterResource(R.drawable.ic_cross),
            contentDescription = "Remove",
            tint = LightColorPalette.outlineVariant,
            modifier = Modifier.clickable { onRemove() })
    }, modifier = Modifier.padding(end = 4.dp, bottom = 4.dp)
    )
}

@Composable
fun FooterButtonItem(
    onPrimaryButtonClick: () -> Unit,
    onSecondaryButtonClick: () -> Unit,
    isPrimaryButtonEnabled: Boolean
) {
    MultiButtonBottomRow(
        modifier = Modifier.background(Color.White),
        secondaryButtonTitle = R.string.cancel,
        primaryButtonTitle = R.string.save,
        secondaryButtonClickListener = { onSecondaryButtonClick.invoke() },
        primaryButtonClickListener = { onPrimaryButtonClick.invoke() },
        isPrimaryButtonEnabled = isPrimaryButtonEnabled
    )
}

@PreviewScreenSizes
@Preview
@Composable
fun SelectActiveNoteScreenPreview() {
    MyAppTheme {
        SelectOffenderScreen(
            onBackPressed = {}) {

        }
    }
}