package com.noteactive.app.features.common.presentation.date_picker

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.common.domain.repository.DateTimeRangePickerRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
open class DateRangePickerViewModel @Inject constructor(
    private val dateTimeRangePickerRepository: DateTimeRangePickerRepository
) : ViewModel() {

    open fun isSelectable(millis: Long): Boolean = dateTimeRangePickerRepository.isDateSelectable(millis)
}