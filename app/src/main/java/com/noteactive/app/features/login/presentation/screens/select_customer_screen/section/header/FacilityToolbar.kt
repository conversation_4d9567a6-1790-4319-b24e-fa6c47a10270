package com.noteactive.app.features.login.presentation.screens.select_customer_screen.section.header

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.action.SelectCustomerViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectCustomerNavigationAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityNavigationAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction

@Composable
fun CustomerSearchToolbar(
    modifier: Modifier,
    selectFacilityNavigation: (SelectCustomerNavigationAction) -> Unit,
    action: (SelectCustomerViewModelAction) -> Unit
) {
    TopAppBar(
        modifier = modifier.shadow(20.dp),
        title = {
            Text(
                text = "Select Customer",
                style = MaterialTheme.typography.titleSmall.copy(color = Color.Black)
            )
        },
        navigationIcon = {
            Icon(
                painter = painterResource(R.drawable.ic_back),
                contentDescription = null,
                modifier = Modifier
                    .clickable {
                        selectFacilityNavigation.invoke(
                            SelectCustomerNavigationAction.NavigateBack
                        )
                    }
                    .padding(horizontal = 16.dp),
                tint = Color.Black
            )
        },
        actions = {
            Icon(
                painter = painterResource(id = R.drawable.ic_search),
                contentDescription = "Search",
                modifier = Modifier
                    .clickable {
                        action(SelectCustomerViewModelAction.OnEnableSearch)
                    }
                    .padding(horizontal = 16.dp)
            )
        }
    )
}