package com.noteactive.app.features.authenticator.di

import android.content.Context
import com.noteactive.app.features.authenticator.data.repositoryimpl.AuthenticatorRepositoryImpl
import com.noteactive.app.features.authenticator.domain.repository.AuthenticatorRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.qualifiers.ApplicationContext

@Module
@InstallIn(ViewModelComponent::class)
object AuthenticatorModule {

    @Provides
    fun provideAuthenticator(
        @ApplicationContext context: Context,
    ): AuthenticatorRepository {
        return AuthenticatorRepositoryImpl(context)
    }
}