package com.noteactive.app.features.home.createnote.select_active_note_screen.action

import com.noteactive.app.features.home.createnote.select_active_note_screen.state.ActiveNote

sealed interface SelectActiveNoteViewModelAction {
    data object OnEnableSearch : SelectActiveNoteViewModelAction
    data object OnSearchCrossClicked : SelectActiveNoteViewModelAction
    data class OnSearchTextChanged(val text: String) : SelectActiveNoteViewModelAction
    data class OnActiveNoteClicked(val activeNote: ActiveNote) : SelectActiveNoteViewModelAction
}