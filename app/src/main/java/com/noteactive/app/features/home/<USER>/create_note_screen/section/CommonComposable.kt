package com.noteactive.app.features.home.createnote.create_note_screen.section

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp

@Composable
fun CreateNoteItemChip(
    title: String,
    onRemove: () -> Unit,
    modifier: Modifier =  Modifier
        .padding(2.dp)
        .border(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline,
            shape = RoundedCornerShape(4.dp)
        )
        .padding(horizontal = 8.dp, vertical = 4.dp),
    leadingIcon: Int? = null,
    trailingIcon: Int? = null
) {
    Row(
        modifier =modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        leadingIcon?.let {
            Image(
                painter = painterResource(it),
                contentDescription = null,
                modifier = Modifier
                    .size(20.dp)
                    .padding(end = 4.dp)
            )
        }


        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall.copy(color = Color.Black)
        )

        trailingIcon?.let {
            Image(
                painter = painterResource(it),
                contentDescription = "",
                modifier = Modifier
                    .size(16.dp)
                    .clickable { onRemove() }
                    .padding(start = 2.dp)
            )
        }

    }
}
