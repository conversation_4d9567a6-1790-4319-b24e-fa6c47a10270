package com.noteactive.app.features.home.createnote.create_note_screen

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.material3.adaptive.currentWindowAdaptiveInfo
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.empty_note_row.addEmptyViews
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.composable.textfield.TransparentHintTextField
import com.noteactive.app.core.presentation.designsystem.theme.DeviceConfiguration
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NoteRowDivider
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.createnote.create_note_screen.action.CreateNoteNavigationAction
import com.noteactive.app.features.home.createnote.create_note_screen.action.CreateNoteViewModelAction
import com.noteactive.app.features.home.createnote.create_note_screen.section.active_note_header.CreateActiveNoteHeader
import com.noteactive.app.features.home.createnote.create_note_screen.section.create_note_location.CreateNoteLocation
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_facility.AddNoteFacility
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_inmate.CreateNoteInmate
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_popup_options.CreateNoteOptionPopup
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_popup_options.NotePopupOptionsActions
import com.noteactive.app.features.home.createnote.create_note_screen.section.note_users.CreateNoteUsers
import com.noteactive.app.features.home.createnote.create_note_screen.section.toolbar.CreateNoteToolbar
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions
import com.noteactive.app.features.home.createnote.create_note_screen.state.getNewNoteItem

@Composable
fun CreateNoteScreenRoot(
    viewModel: CreateNewScreenViewModel,
    navigationAction: (CreateNoteNavigationAction) -> Unit,
    navigationPopupAction: (NotePopupOptionsActions) -> Unit
) {
    val viewModel: CreateNewScreenViewModel = viewModel
    val newNoteItems by viewModel.noteDetails.collectAsStateWithLifecycle()
    CreateNoteScreen(
        newNoteItems = newNoteItems,
        navigationAction = navigationAction::invoke,
        viewModelAction = viewModel::onAction,
        navigationPopupAction = {
            navigationPopupAction.invoke(it)
        })
}

@Composable
fun CreateNoteScreen(
    modifier: Modifier = Modifier,
    newNoteItems: List<NewNoteRow>,
    viewModelAction: (CreateNoteViewModelAction) -> Unit,
    navigationAction: (CreateNoteNavigationAction) -> Unit,
    navigationPopupAction: (NotePopupOptionsActions) -> Unit
) {
    val windowSizeClass = currentWindowAdaptiveInfo().windowSizeClass
    val deviceConfiguration = DeviceConfiguration.fromWindowSizeClass(windowSizeClass)
    var chipsSpan = 2
    var bottomPopupPadding = 80.dp
    when(deviceConfiguration){
        DeviceConfiguration.MOBILE_PORTRAIT -> {
            chipsSpan = 2
        }
        DeviceConfiguration.MOBILE_LANDSCAPE -> {
            chipsSpan = 4

        }
        DeviceConfiguration.TABLET_PORTRAIT -> {
            bottomPopupPadding = 120.dp
            chipsSpan = 5

        }
        DeviceConfiguration.TABLET_LANDSCAPE -> {
            bottomPopupPadding = 120.dp
            chipsSpan = 6

        }
        DeviceConfiguration.DESKTOP -> {
            chipsSpan = 6

        }
    }
    Scaffold(topBar = {
        CreateNoteToolbar()
    }, bottomBar = {
        FooterButtonItem(onPrimaryButtonClick = {}, onSecondaryButtonClick = {})
    }) { padding ->
        ContentDetails(
            modifier = modifier
                .padding(padding)
                .fillMaxSize(),
            newNoteItems,
            viewModelAction = viewModelAction,
            chipSpan = chipsSpan,
            action = { navigationPopupAction.invoke(it) },
            bottomPopupPadding = bottomPopupPadding)
    }
}

@Composable
fun ContentDetails(
    modifier: Modifier = Modifier,
    newNoteItems: List<NewNoteRow>,
    action: (NotePopupOptionsActions) -> Unit,
    viewModelAction: (CreateNoteViewModelAction) -> Unit,
    chipSpan: Int,
    bottomPopupPadding : Dp
) {

    Box {
        Column(
            modifier = modifier.verticalScroll(rememberScrollState())
        ) {
            newNoteItems.forEachIndexed { index, note ->
                Row(
                    modifier = Modifier
                        .heightIn(min = 36.dp)
                        .height(IntrinsicSize.Min)
                        .fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
                ) {

                    Box(
                        modifier = Modifier
                            .width(40.dp)
                            .fillMaxHeight()
                            .background(Color.White),
                        contentAlignment = Alignment.Center
                    ) {
                        note.icon?.let {
                            Icon(
                                painter = painterResource(it),
                                contentDescription = null,
                                tint = Color.Black
                            )

                        }
                    }

                    VerticalDivider(
                        color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight()
                    )

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .fillMaxHeight()
                            .background(Color.White)
                            .padding(top = 6.dp),
                    ) {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(4.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .verticalScroll(rememberScrollState())
                        ) {
                            if (note.activeNoteName.isNotEmpty()) {
                                CreateActiveNoteHeader(
                                    note = note,
                                    onEditClicked = {},
                                    onDeleteClicked = {
                                        viewModelAction.invoke(
                                            CreateNoteViewModelAction.OnActiveNoteRemoved(
                                                index
                                            )
                                        )
                                    },
                                )
                            }

                            if (note.activeNoteDetails.isNotEmpty()) {
                                Text(
                                    text = note.activeNoteDetails,
                                    modifier = Modifier
                                        .wrapContentHeight()
                                        .padding(
                                            start = 16.dp
                                        ),
                                    style = MaterialTheme.typography.bodySmall.copy(
                                        color = MaterialTheme.colorScheme.outlineVariant
                                    ),
                                )
                            }

                            val flowRowChipModifier =
                                Modifier
                                    .horizontalScroll(rememberScrollState())
                                    .fillMaxWidth()
                                    .padding(start = 16.dp)




                            if (note.listOfInmates.isNotEmpty()) {

                                val typeToBeRemovedFromActiveNote = getTypeToBeRemoved(
                                    note = note,
                                    typeToBeRemoved = NewNoteRowTypeOptions.INMATE
                                )

                                CreateNoteInmate(
                                    modifier = flowRowChipModifier, note = note, onCrossClicked = {
                                        viewModelAction.invoke(
                                            CreateNoteViewModelAction.OnNoteItemOptionRemoved(
                                                index = index,
                                                rowTypeOptions = note.typeOptions,
                                                typeTobeRemoved = typeToBeRemovedFromActiveNote,
                                                id = it
                                            )
                                        )
                                    },
                                    chipsSpan = chipSpan)
                            }
                            if (note.listOfUsers.isNotEmpty()) {

                                val typeToBeRemovedFromActiveNote = getTypeToBeRemoved(
                                    note = note,
                                    typeToBeRemoved = NewNoteRowTypeOptions.USERS
                                )

                                CreateNoteUsers(
                                    modifier = flowRowChipModifier,
                                    note = note,
                                    onCrossClicked = { userId ->

                                        viewModelAction.invoke(
                                            CreateNoteViewModelAction.OnNoteItemOptionRemoved(
                                                index = index,
                                                rowTypeOptions = note.typeOptions,
                                                typeTobeRemoved = typeToBeRemovedFromActiveNote,
                                                id = userId
                                            )
                                        )

                                    })

                            }
                            if (note.facilities.isNotEmpty()) {
                                val typeToBeRemovedFromActiveNote = getTypeToBeRemoved(
                                    note = note,
                                    typeToBeRemoved = NewNoteRowTypeOptions.FACILITIES
                                )
                                AddNoteFacility(
                                    modifier = flowRowChipModifier,
                                    note = note,
                                    onCrossClicked = { facility ->
                                        viewModelAction.invoke(
                                            CreateNoteViewModelAction.OnNoteItemOptionRemoved(
                                                index = index,
                                                rowTypeOptions = note.typeOptions,
                                                typeTobeRemoved = typeToBeRemovedFromActiveNote,
                                                id = facility
                                            )
                                        )
                                    })
                            }

                            if (note.locations.isNotEmpty()) {
                                val typeToBeRemovedFromActiveNote = getTypeToBeRemoved(
                                    note = note,
                                    typeToBeRemoved = NewNoteRowTypeOptions.LOCATION
                                )
                                CreateNoteLocation(
                                    modifier = flowRowChipModifier,
                                    note = note,
                                    onCrossClicked = { locationId ->
                                        viewModelAction.invoke(
                                            CreateNoteViewModelAction.OnNoteItemOptionRemoved(
                                                index = index,
                                                rowTypeOptions = note.typeOptions,
                                                typeTobeRemoved = typeToBeRemovedFromActiveNote,
                                                id = locationId
                                            )
                                        )
                                    })
                            }
                            if (note.typeOptions == NewNoteRowTypeOptions.TEXT) {
                                TransparentHintTextField(
                                    modifier = Modifier.padding(start = 16.dp),
                                    text = note.text,
                                    onValueChange = {
                                        viewModelAction(CreateNoteViewModelAction.OnTextChange(it))
                                    },
                                    hintText = "Start typing..",
                                )
                            }
                        }
                    }
                }
                HorizontalDivider(color = NoteRowDivider)
            }
            addEmptyViews(10)
        }
        CreateNoteOptionPopup (
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = bottomPopupPadding),
            action = { action.invoke(it) })
    }
}

@Composable
private fun getTypeToBeRemoved(note: NewNoteRow, typeToBeRemoved: NewNoteRowTypeOptions) =
    if (note.typeOptions == NewNoteRowTypeOptions.ACTIVE_NOTE) typeToBeRemoved else NewNoteRowTypeOptions.NONE

@Composable
fun FooterButtonItem(onPrimaryButtonClick: () -> Unit, onSecondaryButtonClick: () -> Unit) {
    MultiButtonBottomRow(
        modifier = Modifier.background(Color.White),
        secondaryButtonTitle = R.string.close,
        primaryButtonTitle = R.string.verify,
        secondaryButtonClickListener = {
            onSecondaryButtonClick.invoke()
        },
        primaryButtonClickListener = {
            onPrimaryButtonClick.invoke()
        })
}

@PreviewScreenSizes
@Composable
fun CreateNoteScreenPreview() {
    MyAppTheme {
        CreateNoteScreen(
            newNoteItems = getNewNoteItem(),
            navigationAction = {},
            viewModelAction = {}) {}
    }
}