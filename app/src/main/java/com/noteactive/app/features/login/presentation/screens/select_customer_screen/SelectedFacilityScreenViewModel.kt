package com.noteactive.app.features.login.presentation.screens.select_customer_screen

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.features.login.domain.repository.AuthRepository
import com.noteactive.app.features.login.domain.repository.CustomerRepository
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.action.SelectCustomerViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.ChangeCustomerScreenState
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class SelectedCustomerScreenViewModel @Inject constructor(private val customerRepository: CustomerRepository) :
    ViewModel() {
    private val _selectCustomerState = MutableStateFlow(
        ChangeCustomerScreenState(
            allCustomerList = emptyList(),
            filteredList = emptyList()
        )
    )

    val selectCustomerState = _selectCustomerState.asStateFlow()

    init {
        getSelectedCustomer()
    }

    private fun getSelectedCustomer() {
        viewModelScope.launch {
            val facilities = withContext(Dispatchers.IO) {
                customerRepository.getAllCustomer()
            }
            _selectCustomerState.value = _selectCustomerState.value.copy(
                allCustomerList = facilities,
                filteredList = facilities
            )
        }
    }

    fun onAction(action: SelectCustomerViewModelAction) {
        when (action) {

            is SelectCustomerViewModelAction.OnCustomerSelectAction -> {
                val updatedAllFacilities = _selectCustomerState.value.allCustomerList.map {
                    it.copy(isSelected = it == action.facility)
                }

                val updatedFilteredList = updatedAllFacilities.filter {
                    it.customerName.contains(
                        _selectCustomerState.value.searchText,
                        ignoreCase = true
                    )
                }
                _selectCustomerState.update {
                    it.copy(
                        allCustomerList = updatedAllFacilities,
                        filteredList = updatedFilteredList
                    )
                }
            }

            SelectCustomerViewModelAction.OnEnableSearch -> {
                _selectCustomerState.update {
                    it.copy(
                        isSearchEnabled = true
                    )
                }
            }

            SelectCustomerViewModelAction.OnSearchCrossClicked -> {
                _selectCustomerState.update {
                    it.copy(
                        isSearchEnabled = false
                    )
                }
                filterFacilities(filterText = "")
            }

            is SelectCustomerViewModelAction.OnSearchCustomerTextAction -> {
                filterFacilities(action.searchText)
            }

            SelectCustomerViewModelAction.OnSubmitClickedAction -> {

            }
        }
    }

    private fun filterFacilities(filterText: String) {
        val filteredList = _selectCustomerState.value.allCustomerList.filter {
            it.customerName.contains(filterText, ignoreCase = true)
        }
        _selectCustomerState.update {
            it.copy(
                searchText = filterText,
                filteredList = filteredList
            )
        }
    }

}