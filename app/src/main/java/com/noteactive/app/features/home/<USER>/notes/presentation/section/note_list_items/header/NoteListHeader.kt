package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.header

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesListHeaderBackground
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderTextBlue
import com.noteactive.app.features.home.shownotes.notes.data.model.Note
import com.noteactive.app.features.home.shownotes.notes.data.model.listOfNote
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenNavigationAction
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenViewModelAction

@Composable
fun NoteListHeaderRoot(
    note: Note,
    modifier: Modifier = Modifier,
    viewModelAction: (NotesScreenViewModelAction) -> Unit,
    navigationAction: (NotesScreenNavigationAction?) -> Unit,
) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .background(NotesListHeaderBackground)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,

        ) {
        Text(
            note.header.time,
            modifier = Modifier
                .width(60.dp)
                .clickable {
                    navigationAction.invoke(NotesScreenNavigationAction.OnSelectUpdateTime)
                },
            style = MaterialTheme.typography.labelLarge,
            textAlign = TextAlign.Center,
        )
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = if (note.header.expanded) {
                "Collapse"
            } else {
                "Read All"
            },
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .clickable {
                    viewModelAction.invoke(NotesScreenViewModelAction.OnToggleSeeAll(note.header))
                },
            style = MaterialTheme.typography.labelMedium.copy(color = NotesScreenHeaderTextBlue, fontWeight = FontWeight.Bold)
        )
    }
}

@PreviewScreenSizes
@Preview(showBackground = true)
@Composable
fun NoteListHeaderPreview() {
    MyAppTheme {
        NoteListHeaderRoot(note = listOfNote().first(), viewModelAction = {}) {

        }
    }
}


