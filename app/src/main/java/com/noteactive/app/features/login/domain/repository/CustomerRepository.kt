package com.noteactive.app.features.login.domain.repository

import com.noteactive.app.core.domain.repository.BaseRepository
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.CustomerItemUi
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi

interface CustomerRepository {

    fun getCustomerFacilities(): String

    fun getCurrentCustomerKey(): String?

    fun getFacilities(customerKey: String): List<FacilityItemUi>

    suspend fun selectCustomer(customerKey: String)

    suspend fun getAllCustomer(): List<CustomerItemUi>

}
