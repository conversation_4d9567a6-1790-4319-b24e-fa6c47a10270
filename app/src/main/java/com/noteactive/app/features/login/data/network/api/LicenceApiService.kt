package com.noteactive.app.features.login.data.network.api

import com.noteactive.app.core.util.network.ApiEndpoints
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponse
import retrofit2.Response

import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface LicenceApiService {

    @FormUrlEncoded
    @POST(ApiEndpoints.Licence.LOGIN)
    suspend fun postActivationKey(
        @Field("activitation_key") activationKey: String,
        @Field("phone_device_id") phoneDeviceId: String,
    ): Response<ActivationKeyResponse>

}
