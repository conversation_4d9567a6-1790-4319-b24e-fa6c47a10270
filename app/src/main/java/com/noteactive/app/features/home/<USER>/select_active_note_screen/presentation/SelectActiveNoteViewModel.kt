package com.noteactive.app.features.home.createnote.select_active_note_screen.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.createnote.select_active_note_screen.action.SelectActiveNoteViewModelAction
import com.noteactive.app.features.home.createnote.select_active_note_screen.state.ActiveNoteCollection
import com.noteactive.app.features.home.createnote.select_active_note_screen.state.SelectActiveNoteState
import com.noteactive.app.features.home.createnote.select_active_note_screen.state.getActiveNoteCollection
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class SelectActiveNoteViewModel @Inject constructor() : ViewModel() {

    private val _selectActiveNoteState = MutableStateFlow(
        SelectActiveNoteState(
            activeNoteCollection = getActiveNoteCollection(),
            filteredActiveNote = getActiveNoteCollection()
        )
    )
    val selectActiveNoteState = _selectActiveNoteState.asStateFlow()

    fun onAction(action: SelectActiveNoteViewModelAction) {
        when (action) {
            is SelectActiveNoteViewModelAction.OnEnableSearch -> {
                _selectActiveNoteState.update { it.copy(isSearchEnabled = true) }
            }

            is SelectActiveNoteViewModelAction.OnSearchCrossClicked -> {
                _selectActiveNoteState.update {
                    it.copy(
                        isSearchEnabled = false,
                        searchText = "",
                        filteredActiveNote = _selectActiveNoteState.value.filteredActiveNote
                    )
                }
            }

            is SelectActiveNoteViewModelAction.OnSearchTextChanged -> {
                val searchText = action.text
                val filteredCollection = filteredActiveNoteCollection(searchText)
                _selectActiveNoteState.update {
                    it.copy(
                        searchText = searchText,
                        filteredActiveNote = filteredCollection
                    )
                }
            }

            is SelectActiveNoteViewModelAction.OnActiveNoteClicked -> {

            }
        }
    }

    private fun filteredActiveNoteCollection(text: String): List<ActiveNoteCollection> {
        val filteredListOfActiveNoteCollection = mutableListOf<ActiveNoteCollection>()

        getActiveNoteCollection().forEach { collection ->
            val searchedItem =
                collection.activeNotes.filter { it.name.contains(text, ignoreCase = true) }
            if (searchedItem.isNotEmpty()) {
                val searchResult = ActiveNoteCollection(collection.title, searchedItem)
                filteredListOfActiveNoteCollection.add(searchResult)
            }
        }
        return filteredListOfActiveNoteCollection
    }

}