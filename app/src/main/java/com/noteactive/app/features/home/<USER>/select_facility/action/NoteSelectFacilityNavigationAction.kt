package com.noteactive.app.features.home.createnote.select_facility.action

import com.noteactive.app.features.home.createnote.select_facility.state.SelectFacility

sealed interface NoteSelectFacilityNavigationAction {
    data class OnSelectFacilityAction(val facilities: List<SelectFacility>) :
        NoteSelectFacilityNavigationAction

    data object OnBackPressed : NoteSelectFacilityNavigationAction

}