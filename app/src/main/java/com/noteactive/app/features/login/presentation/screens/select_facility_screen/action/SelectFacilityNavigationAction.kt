package com.noteactive.app.features.login.presentation.screens.select_facility_screen.action

import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi

sealed interface SelectFacilityNavigationAction {
    data class NavigateBackWithFacility(val facility: FacilityItemUi) : SelectFacilityNavigationAction
    object  NavigateBack : SelectFacilityNavigationAction
}