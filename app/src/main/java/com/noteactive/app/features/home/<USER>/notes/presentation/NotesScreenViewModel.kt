package com.noteactive.app.features.home.shownotes.notes.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.core.util.formatDate
import com.noteactive.app.features.home.shownotes.notes.data.model.listOfNote
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenViewModelAction
import com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header.action.NotesScreenToolbarViewModelAction
import com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header.state.NotesScreenHeaderState
import com.noteactive.app.features.home.shownotes.notes.presentation.state.NoteScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class NotesScreenViewModel : ViewModel() {

    private val _notes = MutableStateFlow(
        NoteScreenState(
            notesScreenHeaderState = NotesScreenHeaderState(
                name = "SMU",
                parentFacility = "Parent facility",
                childFacility = "Child Facility login Facility Center",
                isSearchEnabled = false,
                date = "12/09/2026"
            ),
            notes = listOfNote()
        )
    )
    val notes = _notes.asStateFlow()


    fun onAction(onHeaderAction: NotesScreenToolbarViewModelAction) {
        when (onHeaderAction) {
            NotesScreenToolbarViewModelAction.OnSearchClicked -> {
                _notes.value = _notes.value.copy(
                    notesScreenHeaderState = _notes.value.notesScreenHeaderState.copy(
                        isSearchEnabled = true
                    )
                )
            }

            NotesScreenToolbarViewModelAction.OnSearchCrossClicked -> {
                _notes.value = _notes.value.copy(
                    notesScreenHeaderState = _notes.value.notesScreenHeaderState.copy(
                        searchText = "",
                        isSearchEnabled = false
                    )
                )
            }

            is NotesScreenToolbarViewModelAction.OnSearchTextChanged -> {
                _notes.value = _notes.value.copy(
                    notesScreenHeaderState = _notes.value.notesScreenHeaderState.copy(searchText = onHeaderAction.text)
                )
            }
        }

    }

    fun onAction(onHeaderAction: NotesScreenViewModelAction) {
        when (onHeaderAction) {
            is NotesScreenViewModelAction.OnToggleSeeAll -> {
                val updatedList1 = _notes.value.notes.map { note ->
                    if (note.header.id == onHeaderAction.noteHeader.id) {
                        note.copy(header = note.header.copy(expanded = !note.header.expanded))
                    } else {
                        note
                    }
                }
                _notes.value = _notes.value.copy(notes = updatedList1)
            }

            is NotesScreenViewModelAction.OnDateRangeSelected -> {
                val startDate: String = formatDate(onHeaderAction.start)
                val endDate: String? = onHeaderAction.end?.let { "-" + formatDate(it) }
               val date =  endDate?.let {
                    startDate + it
                }?:run{
                    startDate
               }
                _notes.value = _notes.value.copy(
                    notesScreenHeaderState = _notes.value.notesScreenHeaderState.copy(
                        date = date
                    )
                )
            }

            is NotesScreenViewModelAction.OnUpdateTimeSelected -> {

            }
        }

    }

}