package com.noteactive.app.features.home.createnote.select_facility

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.IconWithBackgroundContainer
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.createnote.select_facility.action.NoteSelectFacilityNavigationAction
import com.noteactive.app.features.home.createnote.select_facility.action.NoteSelectFacilityScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_facility.state.NoteSelectFacilityState

@Composable
fun NoteSelectFacilityScreenRoot(navAction: (NoteSelectFacilityNavigationAction) -> Unit) {
    val viewModel: NoteSelectFacilityScreenViewModel = hiltViewModel()
    val facilityScreenState by viewModel.noteSelectFacilityScreenState.collectAsStateWithLifecycle()
    NoteSelectFacilityScreen(
        facilityScreenState,
        viewModel::action,
        navAction::invoke)
}

@Composable
fun NoteSelectFacilityScreen(
    facilityScreenState: NoteSelectFacilityState,
    action: (NoteSelectFacilityScreenViewModelAction) -> Unit,
    navAction: (NoteSelectFacilityNavigationAction) -> Unit
) {
    Scaffold(
        topBar = {
            ToolbarSearchView(
                title = facilityScreenState.toolbarTitle,
                hint = facilityScreenState.hint,
                isSearchEnabled = facilityScreenState.isSearchEnabled,
                searchText = facilityScreenState.searchText,
                onSearchTextChanged = {
                    action(
                        NoteSelectFacilityScreenViewModelAction.OnSearchTextChanged(
                            it
                        )
                    )
                },
                onSearchCrossClicked = { action(NoteSelectFacilityScreenViewModelAction.OnSearchCrossClicked) },
                onSearchEnabledClicked = { action(NoteSelectFacilityScreenViewModelAction.OnEnableSearch) },
                onBackIconClicked = {
                    navAction(NoteSelectFacilityNavigationAction.OnBackPressed)
                },
            )
        },
        bottomBar = {
            MultiButtonBottomRow(
                modifier = Modifier.background(Color.White),
                secondaryButtonTitle = R.string.cancel,
                primaryButtonTitle = R.string.submit,
                secondaryButtonClickListener = {
                    navAction(NoteSelectFacilityNavigationAction.OnBackPressed)
                },
                primaryButtonClickListener = {
                    val facilitiesList = facilityScreenState.facilities.filter { it.isSelected }
                    navAction(NoteSelectFacilityNavigationAction.OnSelectFacilityAction(facilitiesList))

                })
        }) { innerPadding ->
        FacilityItems(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(color = Color.White),

            state = facilityScreenState,
            action = action
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun FacilityItems(
    modifier: Modifier,
    state: NoteSelectFacilityState,
    action: (NoteSelectFacilityScreenViewModelAction) -> Unit
) {

    LazyColumn(modifier = modifier) {
        stickyHeader {
            SelectAllHeader(state, action)
        }

        itemsIndexed(state.filteredFacilities) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        action.invoke(
                            NoteSelectFacilityScreenViewModelAction.OnSelectToggle(
                                index
                            )
                        )
                    }
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(18.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))

                IconWithBackgroundContainer(
                    imageRes = R.drawable.ic_location_building,
                    tint = item.tint,
                    bgColor = item.background,
                )
                Text(text = item.facilityName, style = MaterialTheme.typography.labelLarge)

            }
            HorizontalDivider(color = NotesScreenHeaderDevider)
        }
    }
}

@Composable
private fun SelectAllHeader(
    state: NoteSelectFacilityState,
    action: (NoteSelectFacilityScreenViewModelAction) -> Unit
) {
    Row(
        modifier = Modifier.padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconHelper(
            imageRes = getCheckBoxIcon(state.isSelectAllToggle),
            modifier = Modifier.clickable {
                action(NoteSelectFacilityScreenViewModelAction.OnSelectAllToggle)
            })
        Text(text = state.count, style = MaterialTheme.typography.labelLarge)
    }
    HorizontalDivider(color = NotesScreenHeaderDevider)
}


@PreviewScreenSizes
@Composable
fun NoteSelectFacilityScreenRootPreview() {
    NoteSelectFacilityScreen(
        facilityScreenState = NoteSelectFacilityState(),
        action = {}
    ) {}
}