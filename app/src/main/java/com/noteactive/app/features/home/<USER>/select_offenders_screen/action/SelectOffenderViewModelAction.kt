package com.noteactive.app.features.home.createnote.select_offenders_screen.action

import com.noteactive.app.features.home.createnote.select_offenders_screen.state.Offender

sealed interface SelectOffenderViewModelAction {
    data object OnEnableSearch : SelectOffenderViewModelAction
    data object OnSearchCrossClicked : SelectOffenderViewModelAction
    data class OnSearchTextChanged(val text: String) : SelectOffenderViewModelAction
    data class OnOffenderItemClicked(val offender: Offender) : SelectOffenderViewModelAction
    data object OnSelectAllToggle : SelectOffenderViewModelAction
}