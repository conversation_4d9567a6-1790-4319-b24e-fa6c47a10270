package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note

import com.noteactive.app.R

data class ActiveNoteState(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<AdvanceSearchActiveNote> = formState(),
    var filteredListState: List<AdvanceSearchActiveNote> = formState(),
    var searchText: String = ""
)

data class AdvanceSearchActiveNote(
    val icon: Int,
    val title: String,
    val id :String,
    val isSelected: Boolean
)


private fun formState() = listOf(
    AdvanceSearchActiveNote(
        icon = R.drawable.ic_fire,
        title = "Fire ",
        isSelected = false,
        id = "1"
    ),
    AdvanceSearchActiveNote(
        icon = R.drawable.ic_note_test1,
        title = "Attorney visit",
        isSelected = false,
        id = "2"

    ),
    AdvanceSearchActiveNote(
        icon = R.drawable.ic_fire,
        title = "Recreation ",
        isSelected = false,
        id = "3"

    ),

    )