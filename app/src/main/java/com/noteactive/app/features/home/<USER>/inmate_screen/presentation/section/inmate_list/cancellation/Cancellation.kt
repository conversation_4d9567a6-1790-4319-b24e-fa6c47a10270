package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.cancellation

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon

@Composable
fun CancellationInfo(modifier: Modifier = Modifier, profile: ClientProfile) {

    Row(
        modifier = modifier
            .heightIn(min = 40.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {


        NoteItemIcon(modifier = Modifier.width(40.dp), imageId = R.drawable.ic_note_test1,isNotesScreen = false)
        VerticalDivider(
            color = NotesListVerticalDivider,
            modifier = Modifier
                .fillMaxHeight()
                .padding(end = 8.dp)
        )

        Row(
            modifier = Modifier
                .padding(4.dp)
                .wrapContentWidth()
                .border(
                    BorderStroke(width = 1.dp, color = Color(0xFFFFECEA)),
                    RoundedCornerShape(4.dp)  // Add shape to border
                )
                .clip(RoundedCornerShape(4.dp))  // Clip after border
                .background(Color(0xFFFFECEA).copy(alpha = 0.2f))
                .padding(vertical = 4.dp, horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_note_test1),
                contentDescription = null,
                tint = Color(0xFFDC2626)
            )
            Text(
                text = profile.cancelledText,
                style = MaterialTheme.typography.labelSmall,
                color = Color(0xFFDC2626),
                fontFamily = CustomFontNunitoRegular
            )
        }

    }

}

@PreviewScreenSizes
@Preview
@Composable
fun CancellationInfoInfoPreview() {
    CancellationInfo(profile = ClientProfile())
}