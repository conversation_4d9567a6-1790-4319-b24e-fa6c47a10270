package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.form

import androidx.compose.ui.graphics.Color


data class SelectFormState(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<FormState> = formState(),
    var filteredItems: List<FormState> = formState(),
    var searchFormText: String = ""
)

data class FormState(
    val isSelected: Boolean = false,
    val title: String = "",
    val tint: Color,
    val background: Color,
    val id: String
)

fun formState() = listOf(
    FormState(
        isSelected = false,
        title = "1st floors pre-shift check",
        tint = Color(0xFF13B4EA),
        background = Color(0xFFE7F9FF),
        id = "1"
    ),
    FormState(
        isSelected = false,
        title = "Intake Form",
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1),
        id = "2"

    ),
    FormState(
        isSelected = false,
        title = "Demo Intake Form",
        tint = Color(0xFFFF6900),
        background = Color(0xFFFFF3EB),
        id = "3"

    ),
    FormState(
        isSelected = false,
        title = "1st floors pre-shift check",
        tint = Color(0xFFD03798),
        background = Color(0xFFFFECF8),
        id = "4"

    ),

    )