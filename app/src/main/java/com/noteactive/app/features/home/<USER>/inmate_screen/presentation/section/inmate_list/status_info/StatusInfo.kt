package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.status_info

import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon

@Composable
fun StatusInfo(modifier: Modifier = Modifier, profile: ClientProfile) {

    Row(
        modifier = modifier
            .heightIn(min = 40.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {

        val title = buildAnnotatedString {
            withStyle(
                style = SpanStyle(
                    color = Color.Black,
                    fontSize = 12.sp,
                    fontFamily = CustomFontNunitoRegular,
                    fontWeight = FontWeight(400),)

            ) {
                append("Work details - ")
            }
            withStyle(
                style = SpanStyle(
                    fontWeight = FontWeight.Bold,
                )
            ) {
                append(profile.workDetails)
            }
        }

        NoteItemIcon(modifier = Modifier.width(40.dp), imageId = R.drawable.ic_note_test1,isNotesScreen = false)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())

        Text(
            text = title,
            modifier = Modifier
                .padding(horizontal = 8.dp, vertical = 4.dp)
                .padding(4.dp),
            style = MaterialTheme.typography.bodySmall,
            overflow = TextOverflow.Ellipsis
        )

    }

}

@PreviewScreenSizes
@Composable
fun StatusInfoPreview() {
    StatusInfo(profile = ClientProfile())
}