package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.keyword

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField

@Composable
fun KeywordSection(keywordName: String, onTextChange: (String) -> Unit) {

    Column(modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(4.dp)) {
        Text(text = "Keywords", style = MaterialTheme.typography.bodyMedium)
        KeywordsTextField(
            text = keywordName,
            onTextChange = { onTextChange.invoke(it) }
        )
    }

}

@Composable
fun KeywordsTextField(
    modifier: Modifier = Modifier
        .fillMaxSize()
        .background(Color.White),
    text: String,
    onTextChange: (String) -> Unit
) {
    val focusRequester = remember { FocusRequester() }

    PrimaryTextField(
        value = text,
        onValueChange = { onTextChange.invoke(it) },
        modifier = modifier.focusRequester(focusRequester),
        borderColor = MaterialTheme.colorScheme.primary
    )

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}

@PreviewScreenSizes
@Composable
fun KeywordSectionPreview() {
    KeywordSection("") {

    }
}