package com.noteactive.app.features.home.createnote.select_offenders_screen.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.createnote.select_offenders_screen.action.SelectOffenderViewModelAction
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.Offender
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.SelectOffenderState
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.getOffenderDetails
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class SelectOffenderViewModel @Inject constructor() : ViewModel() {

    private val _selectOffenderState = MutableStateFlow(
        SelectOffenderState(
            offenders = getOffenderDetails(),
            filteredListOfOffender = getOffenderDetails(),
            selectedOffenders = getOffenderDetails().filter { it.isChecked }
        )
    )
    val selectOffenderState: StateFlow<SelectOffenderState> = _selectOffenderState



    fun onAction(action: SelectOffenderViewModelAction) {
        when (action) {
            is SelectOffenderViewModelAction.OnEnableSearch -> {
                _selectOffenderState.update { it.copy(isSearchEnabled = true) }
            }

            is SelectOffenderViewModelAction.OnSearchCrossClicked -> {
                _selectOffenderState.update {
                    it.copy(
                        isSearchEnabled = false,
                        searchText = "",
                        filteredListOfOffender = _selectOffenderState.value.offenders
                    )
                }
            }

            is SelectOffenderViewModelAction.OnSearchTextChanged -> {
                val searchText = action.text
                val filtered = _selectOffenderState.value.offenders.filter {
                    it.name.contains(searchText, ignoreCase = true)
                }
                _selectOffenderState.update {
                    it.copy(
                        searchText = searchText,
                        filteredListOfOffender = filtered
                    )
                }
            }

            is SelectOffenderViewModelAction.OnOffenderItemClicked -> {
                val currentState = _selectOffenderState.value
                val updatedList = currentState.offenders.map {
                    if (it.id == action.offender.id) {
                        it.copy(isChecked = !it.isChecked)
                    } else it
                }
                updateSelectedOffenderState(updatedList)
            }

            is SelectOffenderViewModelAction.OnSelectAllToggle -> {
                val currentState = _selectOffenderState.value
                val toggle = !currentState.isAllChecked
                val updatedList = currentState.offenders.map {
                    it.copy(isChecked = toggle)
                }
                _selectOffenderState.update {
                    it.copy(isAllChecked = toggle)
                }
                updateSelectedOffenderState(updatedList)
            }
        }
    }
    private fun updateSelectedOffenderState(updatedList: List<Offender>) {
        val searchText = _selectOffenderState.value.searchText

        val filtered = updatedList.filter {
            it.name.contains(searchText, ignoreCase = true)
        }

        val selected = updatedList.filter { it.isChecked }

        val countLabel = if (selected.isEmpty()) "${updatedList.size} Offenders" else "${selected.size} / ${updatedList.size} Offenders selected"

        _selectOffenderState.update {
            it.copy(
                offenders = updatedList,
                filteredListOfOffender = filtered,
                selectedOffenders = selected,
                headerTitle = countLabel
            )
        }
    }

}