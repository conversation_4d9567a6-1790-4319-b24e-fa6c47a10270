package com.noteactive.app.features.home.shownotes.notes.data.model

import java.util.UUID


data class Note(val header: NoteHeader, val content: List<NoteContent>)

data class NoteContent(
    val title: String? = null,
    val location: String? = null,
    val user: String? = null,
    val files: List<String> = listOf(),
    val comment: List<String> = listOf(),
    val inmates: List<String> = listOf(),
    val type: NoteItemType
) {
    val nonVisibleInmateCount = inmates.size.minus(1)
}

data class NoteHeader(
    val time: String,
    val id: Int = 0,
    var expanded: Boolean = false
)

enum class NoteItemType {
    TITLE,
    LOCATION,
    Attachments,
    COMMENTS,
    SIGNATURE,
    TAGGED_INMATE,
}

val text = "Begin shift | Team - D | lorem ipsum dummy..."


fun listOfNote(): List<Note> {
    val list = listOf(
        Note(
            header = NoteHeader("12:10", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy - D | lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(
                    inmates = listOf(
                        "Ahmed sidqui, 110091, 4F Demo and Training",
                        "Karan Gaur, 110491, 2F Demo and Training",
                        "Sonal Gaur, 110491, 2F Demo and Training"
                    ), type = NoteItemType.TAGGED_INMATE
                ),

                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
                NoteContent(user = "Akarsh Pawar", type = NoteItemType.SIGNATURE),
                NoteContent(
                    comment = listOf(
                        "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024",
                        "Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024"
                    ),
                    type = NoteItemType.COMMENTS
                )
            )
        ),
        Note(
            header = NoteHeader("12:11", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy lorem ipsum dummy lorem ipsum dummylorem ipsum dummy lorem ipsum dummylorem ipsum dummylorem ipsum dummy lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),

        Note(
            header = NoteHeader("12:12", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy - D | lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
                NoteContent(
                    inmates = listOf(
                        "Ahmed sidqui, 110091, 4F Demo and Training",
                        "Karan Gaur, 110491, 2F Demo and Training",
                        "Sonal Gaur, 110491, 2F Demo and Training"
                    ), type = NoteItemType.TAGGED_INMATE
                ),
            )
        ),

        Note(
            header = NoteHeader("12:13", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy - D | lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(
                    inmates = listOf(
                        "Ahmed sidqui, 110091, 4F Demo and Training",
                        "Karan Gaur, 110491, 2F Demo and Training",
                        "Sonal Gaur, 110491, 2F Demo and Training"
                    ), type = NoteItemType.TAGGED_INMATE
                ),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(user = "Akarsh Pawar", type = NoteItemType.SIGNATURE),
                NoteContent(
                    comment = listOf(
                        "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024",
                        "Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024"
                    ),
                    type = NoteItemType.COMMENTS
                )
            )
        ),
        Note(
            header = NoteHeader("12:14", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy - D | lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(
                    inmates = listOf(
                        "Ahmed sidqui, 110091, 4F Demo and Training",
                        "Karan Gaur, 110491, 2F Demo and Training",
                        "Sonal Gaur, 110491, 2F Demo and Training"
                    ), type = NoteItemType.TAGGED_INMATE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),

                )
        ),

        Note(
            header = NoteHeader("12:15", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 2 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),
        Note(
            header = NoteHeader("12:16", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "ABCajun 2 - AB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:17 ", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 2RD - AB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:18", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 21 - ACB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:18", id = UUID.randomUUID().hashCode()), listOf(

                NoteContent(location = "Cajun 2 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    comment = listOf(
                        "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024",
                        "Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024"
                    ),
                    type = NoteItemType.COMMENTS
                )
            )
        ),
        Note(
            header = NoteHeader("12:19", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),

        Note(
            header = NoteHeader("12:20", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),

        Note(
            header = NoteHeader("12:21", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 2 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),
        Note(
            header = NoteHeader("12:22", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "ABCajun 2 - AB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:22 ", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 2RD - AB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:25", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 21 - ACB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:27", id = UUID.randomUUID().hashCode()), listOf(

                NoteContent(location = "Cajun 2 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    comment = listOf(
                        "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024",
                        "Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024"
                    ),
                    type = NoteItemType.COMMENTS
                )
            )
        ),
        Note(
            header = NoteHeader("12:00", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),

        Note(
            header = NoteHeader("12:12", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Begin shift | Team - D | lorem ipsum dummy lorem ipsum dummy",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 1 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),

        Note(
            header = NoteHeader("12:03", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "Orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 2 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    type = NoteItemType.Attachments
                ),
            )
        ),
        Note(
            header = NoteHeader("12:20", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "ABCajun 2 - AB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:12 ", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 2RD - AB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:19", id = UUID.randomUUID().hashCode()), listOf(
                NoteContent(
                    title = "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.",
                    type = NoteItemType.TITLE
                ),
                NoteContent(location = "Cajun 21 - ACB", type = NoteItemType.LOCATION),
            )
        ),
        Note(
            header = NoteHeader("12:27", id = UUID.randomUUID().hashCode()), listOf(

                NoteContent(location = "Cajun 2 - AB", type = NoteItemType.LOCATION),
                NoteContent(
                    files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                    comment = listOf(
                        "orders read and Understood | Shift Change Inspection | Inventory | Checked and cleared  | TANNER TESSLING - Correction cadet.Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024",
                        "Comment:  Lorem ipsum dummy text Sachin Rathi 12/04/2024"
                    ),
                    type = NoteItemType.COMMENTS
                )
            )
        ),
    )

//    val new = List(100) { list }.flatten()
    return list.reversed()
}