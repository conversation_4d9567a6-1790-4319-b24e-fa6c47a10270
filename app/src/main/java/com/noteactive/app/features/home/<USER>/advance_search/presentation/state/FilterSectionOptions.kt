package com.noteactive.app.features.home.shownotes.advance_search.presentation.state

import androidx.compose.ui.graphics.Color
import com.noteactive.app.core.presentation.designsystem.theme.Green100
import com.noteactive.app.core.presentation.designsystem.theme.Red100
import com.noteactive.app.core.presentation.designsystem.theme.Yellow100
import com.noteactive.app.features.home.createnote.select_user.state.SelectUser
import com.noteactive.app.features.home.createnote.select_user.state.userSample
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection.Companion.getAdvanceFilterList
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note.ActiveNoteState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.classification.ClassificationState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.form.SelectFormState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.location.SelectLocationState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.offenders.OffenderStatusState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.task_type.TaskTypeStatus
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection.Companion.getFilterList

data class FilterSectionState(
    val filters: List<FilterSection> = getFilterList(),
    val activeNoteState: ActiveNoteState = ActiveNoteState(),
    val taskTypeStatus: TaskTypeStatus = TaskTypeStatus(),
    val classificationState: ClassificationState = ClassificationState(),
    val offenderStatusState: OffenderStatusState = OffenderStatusState(),
    val forms: SelectFormState = SelectFormState(),
    val locationState: SelectLocationState = SelectLocationState(),
    val selectedSection: FilterSection = FilterSection.DATE_TIME,
    val keyword: String = "",
    var users: SelectUserState = SelectUserState(),
    var listOfColor: SelectHighlighterState = SelectHighlighterState()
)

data class SelectUserState(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<SelectUser> = userSample,
    var filteredItems: List<SelectUser> = userSample,
    var searchText: String = ""
)


data class SelectHighlighterState(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<SelectColorState> = colorState(),
    var filteredItems: List<SelectColorState> = colorState(),
    var searchText: String = ""
)

data class SelectColorState(
    val isSelected: Boolean = false,
    val title: String = "",
    val color: Color,
    val id: String
)


enum class FilterSection(val displayName: String) {
    DATE_TIME("Date & Time"),
    KEYWORDS("Keywords"),
    USER("User"),
    LOCATION("Location"),
    OFFENDER("Offender"),
    HIGHLIGHTER("Highlighter"),
    FORM("Form"),
    ACTIVE_NOTES("Active notes"),
    TASK_TYPE("Task type"),
    OFFENDER_STATUS("Offender status"),
    CLASSIFICATION("Classification"),
    TIME("Time"),
    OTHER("Other");

    companion object {
        private val displayNameMap = entries.associateBy { it.displayName }

        fun fromDisplayName(name: String): FilterSection? {
            return displayNameMap[name]
        }

        private val advanceSearchFilter = listOf(
            DATE_TIME,
            KEYWORDS,
            USER,
            LOCATION,
            OFFENDER,
            HIGHLIGHTER,
            FORM,
            ACTIVE_NOTES,
            TASK_TYPE,
            OFFENDER_STATUS,
            CLASSIFICATION
        )
        private val filterOptions = listOf(TIME, ACTIVE_NOTES, USER, OFFENDER, OTHER)
        fun getAdvanceFilterList(): List<FilterSection> = advanceSearchFilter
        fun getFilterList(): List<FilterSection> = filterOptions
    }
}


fun colorState() =
    listOf(
        SelectColorState(
            color = Color(0xFFE9F2FF),
            title = "Blue",
            id = "1"

        ),
        SelectColorState(
            color = Green100,
            title = "Green",
            id = "2"


        ),
        SelectColorState(
            color = Red100,
            title = "Red",
            id = "3"

        ),
        SelectColorState(
            color = Yellow100,
            title = "Yellow", id = "4"


        ),
        SelectColorState(
            color = Color.White,
            title = "White",
            id = "5"


        ),
    )
