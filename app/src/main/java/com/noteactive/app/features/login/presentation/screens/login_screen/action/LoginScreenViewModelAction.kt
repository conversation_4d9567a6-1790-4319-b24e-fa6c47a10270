package com.noteactive.app.features.login.presentation.screens.login_screen.action

sealed interface LoginScreenViewModelAction {
    data class OnFacilityChanged(var facilityName: String) : LoginScreenViewModelAction
    data class OnError(var error : String) : LoginScreenViewModelAction
    data class OnCustomerChanged(var customerName: String, val customerKey: String) : LoginScreenViewModelAction

    data class OnEnterPin(val pin: String) : LoginScreenViewModelAction
    data object OnLoginClicked : LoginScreenViewModelAction
    data object OnLicenceSyncClicked : LoginScreenViewModelAction
}