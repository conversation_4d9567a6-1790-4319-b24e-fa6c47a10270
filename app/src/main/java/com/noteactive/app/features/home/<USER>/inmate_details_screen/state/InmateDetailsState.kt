package com.noteactive.app.features.home.inmate.inmate_details_screen.state

import com.noteactive.app.R

data class InmateDetailsState(
    val toolbarTitle : String = "Inmate details",
    val image: Int = R.drawable.inmate_demo_pic,
    val name: String = "<PERSON>",
    val date: String = "12 Jan 2025",
    val time: String = "15:00",
    val locationDetails: String = "PSP2164 | 4F demo and training | Arnette-R-1fgbdfb",
    val gender: String = "Male",
    val dob: String = "02 Dec 1985",
    val id: String = "2387547239874",
    val bookingId: String = "34293459435",
    val ccn: String = "345235235",
    val facility: String = "4F demo",
    val location: String = "New jersey",
    val status: String = "In the cell",
    val selectedTab: RowTabItem = RowTabItem.PERSONAL_INFO,
    val classification: List<Classification> = allClassifications,
    val document : List<DocumentCard>  = DocumentDetails,
    val timeline: List<Timeline> = timelineData
)

enum class RowTabItem(val tab: String){
    PERSONAL_INFO("Personal Info"),
    CLASSIFICATION("Classification"),
    STICKY_NOTES("Sticky Notes"),
    DOC("Doc"),
    HEALTH("Health"),
    TIMELINE("Timeline");
}

data class Classification(
    val id: Int,
    val label: String,
    val image: Int
)
val allClassifications = listOf(
    Classification(1, "ACA clerk", R.drawable.inmate_demo_pic),
    Classification(2, "Janitor/Cleaner", R.drawable.inmate_demo_pic),
    Classification(3, "Kitchen nights", R.drawable.inmate_demo_pic)
)

data class DocumentCard(
    val documentType: String,
    val description : String,
    val signatureImage : Int,
    val nameDateTime : String
)

val documentDescription = "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,"
val signatureImage = R.drawable.ic_signature_bottom_sheet_header_icon
val nameDateTime = "Mark White | 11 Sep 2024, 05:53"
val DocumentDetails = listOf(
    DocumentCard("1:1 Observation", documentDescription, signatureImage, nameDateTime),
    DocumentCard("DOC disciplinary report", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Segregation/Isolation", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Shift change", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Facility destination log", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Fire drill log", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Daily activity log", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Mental health observation log", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Movement log", documentDescription, signatureImage, nameDateTime),
    DocumentCard("Attachments", documentDescription, signatureImage, nameDateTime)
)

data class Timeline(
    val intakeDate: String,
    val dischargeDate: String,
    val monthlyActivities: List<MonthlyActivityStats> = emptyList()
)

data class MonthlyActivityStats(
    val month: String,
    val activity: ActivityStats
)

data class ActivityStats(
    val notes: Int? = null,
    val forms: Int? = null,
    val chipImage: Int,
    val activeNotes: String? = null,
    val tasks: Int? = null
)
val timelineData = listOf(
    Timeline(
        intakeDate = "10 Sep 2024",
        dischargeDate = "12 Aug 2024",
        monthlyActivities = listOf(
            MonthlyActivityStats("Sep", ActivityStats(notes = 10, forms = 5, R.drawable.ic_note_test1, "Active notes", tasks = 13)),
            MonthlyActivityStats("Aug", ActivityStats(notes = 10,null, R.drawable.ic_note_test1)),
        )
    )
)
