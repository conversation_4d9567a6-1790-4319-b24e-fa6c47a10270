package com.noteactive.app.features.home.createnote.select_facility.action


sealed interface NoteSelectFacilityScreenViewModelAction {
    data object OnEnableSearch : NoteSelectFacilityScreenViewModelAction
    data object OnSearchCrossClicked : NoteSelectFacilityScreenViewModelAction
    data object OnSelectAllToggle : NoteSelectFacilityScreenViewModelAction
    data class OnSelectToggle(val index: Int) : NoteSelectFacilityScreenViewModelAction
    data class OnSearchTextChanged(val text: String) : NoteSelectFacilityScreenViewModelAction
}