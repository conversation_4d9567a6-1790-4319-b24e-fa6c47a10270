package com.noteactive.app.features.home.createnote.create_note_screen.section.note_inmate

import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.create_note_screen.section.CreateNoteItemChip
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow


@Composable
fun CreateNoteInmate(
    modifier: Modifier = Modifier,
    note: NewNoteRow,
    onCrossClicked: (String) -> Unit,
    chipsSpan: Int
) {
    FlowRow(
        maxItemsInEachRow = chipsSpan, modifier = modifier
    ) {
        note.listOfInmates.forEachIndexed { index, users ->
            CreateNoteItemChip(
                title = users.name, onRemove = {
                    onCrossClicked.invoke(users.id)
                }, leadingIcon = R.drawable.inmate_demo_pic, trailingIcon = R.drawable.ic_cross
            )

        }
    }
}

