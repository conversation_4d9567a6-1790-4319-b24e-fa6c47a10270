package com.noteactive.app.features.home.inmate.inmate_details_screen.section.stickyNotes

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.composable.button.SecondaryButton
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextSubTitleColor

@PreviewScreenSizes
@Composable
fun StickyNotesTabContent() {
    Column(
        modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        Text(
            text = stringResource(R.string.notes),
            style = MaterialTheme.typography.labelLarge,
        )
        Text(
            text = "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,",
            style = MaterialTheme.typography.labelMedium.copy(color = FocusTextSubTitleColor)
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.padding(top = 20.dp)
        ) {
            SecondaryButton(text = stringResource(R.string.delete), buttonClick = {})
            PrimaryButton(text = stringResource(R.string.edit), buttonClick = {})
        }
    }
}