package com.noteactive.app.features.login.data.model.entity.response.activation_response.mapper

import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetailTextFieldType
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetails

fun ActivationKeyResponseUserInfo?.toDomain(): List<ActivationDetails> {
    if (this == null) {
        return emptyList()
    } else {
        return listOf(
            ActivationDetails(
                title = "Client name",
                text = this.companyName,
                type = ActivationDetailTextFieldType.TEXT
            ),
            ActivationDetails(
                title = "Password",
                text = this.activationKey,
                type = ActivationDetailTextFieldType.PASSWORD,
            ),
            ActivationDetails(
                title = "User name",
                text = this.firstName + " " + this.lastName,
                type = ActivationDetailTextFieldType.TEXT,
            ),
            ActivationDetails(
                title = "Email",
                text = this.email,
                type = ActivationDetailTextFieldType.EMAIL,
            ),
            ActivationDetails(
                title = "Mobile",
                text = this.contactNumber,
                type = ActivationDetailTextFieldType.MOBILE,
            ),
            ActivationDetails(
                title = "Location",
                text = this.address,
                type = ActivationDetailTextFieldType.TEXT,
            )
        )

    }

}