package com.noteactive.app.features.home.createnote.common_composable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R


@Composable
fun IconWithBackgroundContainer(
    imageRes: Int,
    tint: Color,
    bgColor: Color = Color.White,
    modifier: Modifier = Modifier,
    bgModifier: Modifier = Modifier
        .size(32.dp)
        .background(
            bgColor, CircleShape
        ),
) {

    Box(
        modifier = bgModifier, contentAlignment = Alignment.Center
    ) {
        IconHelper(modifier = modifier, imageRes = imageRes, tint = tint)
    }
}

@Composable
fun IconHelper(modifier: Modifier = Modifier, imageRes: Int, tint: Color = Color.Unspecified) =
    Icon(
        painter = painterResource(imageRes),
        contentDescription = null,
        modifier = modifier,
        tint = tint,
    )


@PreviewScreenSizes
@Composable
fun IconWithBackgroundContainerPreview(modifier: Modifier = Modifier) {
    IconHelper(modifier = modifier, imageRes = R.drawable.ic_location_building)

}

fun getCheckBoxIcon(isChecked: Boolean): Int =
    if (isChecked) R.drawable.ic_selected_checkbox else R.drawable.ic_unselect_checkbox
