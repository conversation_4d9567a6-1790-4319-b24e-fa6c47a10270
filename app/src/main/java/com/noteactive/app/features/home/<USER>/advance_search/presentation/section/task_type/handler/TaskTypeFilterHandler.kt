package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.task_type.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.task_type.TaskTypeStatus
import javax.inject.Inject

class TaskTypeFilterHandler @Inject constructor() : <PERSON><PERSON><PERSON><PERSON><PERSON> {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val taskTypeState = state.taskTypeStatus
        val isSelectAllEnabledReverse = !taskTypeState.isSelectAllEnable
        val selectAllItems = taskTypeState.listItems.map {
            it.copy(isSelected = isSelectAllEnabledReverse)
        }
        return state.copy(
            taskTypeStatus = TaskTypeStatus(
                isSelectAllEnable = isSelectAllEnabledReverse,
                listItems = selectAllItems,
                filteredListState = selectAllItems,
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val taskTypeState = state.taskTypeStatus
        val filteredList = taskTypeState.filteredListState.toMutableList()
        val item = filteredList[index]
        val updatedItem = item.copy(isSelected = !item.isSelected)
        filteredList[index] = updatedItem
        val updatedList = state.taskTypeStatus.listItems.map {
            if (it.id == updatedItem.id) it.copy(isSelected = updatedItem.isSelected) else it
        }


        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            taskTypeStatus = taskTypeState.copy(
                isSelectAllEnable = allSelected,
                listItems = updatedList,
                filteredListState = filteredList,
            )
        )
    }


    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val notes = state.taskTypeStatus
        val selectedIds =
            (notes.listItems + notes.filteredListState).filter { it.isSelected }.map { it.id }
                .toSet()
        val filtered = notes.listItems.filter {
            it.title.contains(searchText, ignoreCase = true)
        }.map {
            it.copy(isSelected = selectedIds.contains(it.id))
        }
        return state.copy(
            taskTypeStatus = notes.copy(
                searchText = searchText, filteredListState = filtered
            )
        )
    }
}