package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.location.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import javax.inject.Inject

class LocationFilterHandler @Inject constructor() : FilterHandler {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val locations = state.locationState
        val selectAll = !locations.isSelectAllEnable
        val updatedFiltered = locations.filteredItems.map { it.copy(isSelected = selectAll) }
        val updatedList = locations.listItems.map {
            if (updatedFiltered.any { u -> u.locationId == it.locationId })
                it.copy(isSelected = selectAll) else it
        }
        return state.copy(
            locationState = locations.copy(
                listItems = updatedList,
                filteredItems = updatedFiltered,
                isSelectAllEnable = selectAll
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val locations = state.locationState
        val updatedFiltered = locations.filteredItems.toMutableList()
        val item = updatedFiltered[index]
        val updatedItem = item.copy(isSelected = !item.isSelected)
        updatedFiltered[index] = updatedItem

        val updatedList = locations.listItems.map {
            if (it.locationId == updatedItem.locationId) it.copy(isSelected = updatedItem.isSelected) else it
        }
        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            locationState = locations.copy(
                listItems = updatedList,
                filteredItems = updatedFiltered,
                isSelectAllEnable = allSelected
            )
        )
    }

    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val locations = state.locationState
        val selectedIds =
            (locations.listItems + locations.filteredItems).filter { it.isSelected }
                .map { it.locationId }.toSet()
        val filtered = locations.listItems.filter {
            it.locationName.contains(searchText, ignoreCase = true)
        }.map {
            it.copy(isSelected = selectedIds.contains(it.locationId))
        }
        return state.copy(
            locationState = locations.copy(
                searchText = searchText,
                filteredItems = filtered
            )
        )
    }
}
