package com.noteactive.app.features.login.presentation.screens.enter_activation_key.state

import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo


sealed class ActivationKeyApiState {
    data object Idle : ActivationKeyApiState()
    data object Loading : ActivationKeyApiState()
    data class Success(val data: ActivationKeyResponseUserInfo) : ActivationKeyApiState()
    data class Error(val message: String) : ActivationKeyApiState()
}

