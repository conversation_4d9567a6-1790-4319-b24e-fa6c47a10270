package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.history

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon

@Composable
fun HistoryInfo(modifier: Modifier = Modifier, profile: ClientProfile) {

    Row(
        modifier = modifier
            .heightIn(min = 40.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {


        NoteItemIcon(modifier = Modifier.width(40.dp), imageId = R.drawable.ic_note_screen_sort, isNotesScreen = false)
        VerticalDivider(
            color = NotesListVerticalDivider,
            modifier = Modifier
                .fillMaxHeight()
                .padding(end = 8.dp)
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState()),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),

            ) {
            profile.locationTags.forEach { tag ->
                Box(
                    modifier = Modifier
                        .padding(vertical = 4.dp, horizontal = 8.dp),
                ) {
                    Text(
                        text = tag, fontSize = 12.sp, color = Color(0xFF374151),
                        fontFamily = CustomFontNunitoRegular
                    )
                }
            }

        }

    }

}

@PreviewScreenSizes
@Preview
@Composable
fun HistoryInfoPreview() {
    HistoryInfo(profile = ClientProfile())
}