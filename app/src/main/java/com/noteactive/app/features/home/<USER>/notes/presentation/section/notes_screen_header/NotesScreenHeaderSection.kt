package com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.authCardBackground
import com.noteactive.app.core.presentation.designsystem.composable.textfield.TransparentHintTextField
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderTextBlue
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenNavigationAction
import com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header.action.NotesScreenToolbarViewModelAction
import com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header.state.NotesScreenHeaderState

@Composable
fun NotesScreenHeader(
    modifier: Modifier = Modifier,
    notesScreenHeaderState: NotesScreenHeaderState,
    onAction: (NotesScreenToolbarViewModelAction) -> Unit,
    navigationAction: (NotesScreenNavigationAction?) -> Unit,
) {
    Box(modifier = modifier.authCardBackground()) {
        Column {
            HeaderFacilityDetailRow(notesScreenHeaderState)
            HorizontalDivider(thickness = 1.5.dp, color = NotesScreenHeaderDevider)

            Row(
                modifier = Modifier.height(46.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (notesScreenHeaderState.isSearchEnabled) {
                    SearchEnableView(notesScreenHeaderState, onAction)
                } else {
                    HeaderOptionsRow(
                        modifier = Modifier
                            .weight(1f),
                        notesScreenHeaderState,
                        onAction,
                        navigationAction
                    )
                }
            }

        }

    }
}

@Composable
private fun HeaderOptionsRow(
    modifier: Modifier = Modifier,
    notesScreenHeaderState: NotesScreenHeaderState,
    onAction: (NotesScreenToolbarViewModelAction) -> Unit,
    navigationAction: (NotesScreenNavigationAction) -> Unit,
) {

    Row(
        modifier = Modifier.padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Column {
            Text("67 Notes", style = MaterialTheme.typography.labelLarge)
            Text(
                "12 Dec 2024",
                style = MaterialTheme.typography.bodySmall.copy(color = NotesScreenHeaderTextBlue),
                modifier = Modifier.clickable {
                    navigationAction(NotesScreenNavigationAction.OnSelectDate)
                }

            )


        }

        DividerVertical(height = 21.dp)
        Icon(painter = painterResource(R.drawable.ic_download), contentDescription = null)
        DividerVertical(height = 21.dp)
        Icon(
            painter = painterResource(R.drawable.ic_bottom_nav_more),
            contentDescription = null,
            tint = Color.Black,
            modifier =Modifier.clickable {
                navigationAction.invoke(NotesScreenNavigationAction.AdvanceSearchClicked)
            }
        )

        Spacer(Modifier.weight(1f))

        Icon(
            painter = painterResource(R.drawable.ic_notes_screen_filter),
            contentDescription = null,
            tint = Color.Black
        )
        DividerVertical(height = 21.dp)

        Icon(
            painter = painterResource(R.drawable.ic_note_screen_sort),
            contentDescription = null,
            tint = Color.Black
        )

        DividerVertical(height = 21.dp)
        Icon(
            painter = painterResource(R.drawable.ic_search),
            contentDescription = null,
            tint = Color.Black,
            modifier = Modifier.clickable {
                navigationAction.invoke(NotesScreenNavigationAction.AdvanceSearchClicked)
            }
        )

    }
}

@Composable
private fun SearchEnableView(
    notesScreenHeaderState: NotesScreenHeaderState,
    onAction: (NotesScreenToolbarViewModelAction) -> Unit
) {
    TransparentHintTextField(
        modifier = Modifier
            .height(46.dp)
            .padding(horizontal = 12.dp),
        hintText = "Search Notes",
        text = notesScreenHeaderState.searchText,
        onValueChange = { text ->
            onAction(
                NotesScreenToolbarViewModelAction.OnSearchTextChanged(
                    text
                )
            )
        },
        trailingIcon = {
            Icon(
                imageVector = Icons.Default.Clear,
                contentDescription = null,
                tint = Color(0xFF989898),
                modifier = Modifier.clickable {
                    onAction(
                        NotesScreenToolbarViewModelAction.OnSearchCrossClicked
                    )
                })
        })
}

@Composable
private fun HeaderFacilityDetailRow(notesScreenHeaderState: NotesScreenHeaderState) {
    Row(
        modifier = Modifier
            .height(46.dp)
            .padding(start = 16.dp, end = 5.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(painter = painterResource(R.drawable.ic_notes), contentDescription = null)
        Icon(painter = painterResource(R.drawable.ic_module_drop_down), contentDescription = null)

        DividerVertical(46.dp)
        HeaderFacilityTitle(text = notesScreenHeaderState.name)
        DividerVertical(18.dp)
        HeaderFacilityTitle(
            notesScreenHeaderState.parentFacility,
            NotesScreenHeaderTextBlue
        )
        DividerVertical(18.dp)
        HeaderFacilityTitle(
            text = notesScreenHeaderState.childFacility,
            color = NotesScreenHeaderTextBlue,
            modifier = Modifier.weight(1f) // makes last text stretch if needed
        )
        Icon(
            imageVector = Icons.Default.MoreVert,
            contentDescription = "Options",
            modifier = Modifier.padding(start = 8.dp)
        )
    }
}

@Composable
private fun DividerVertical(height: Dp) {
    VerticalDivider(
        modifier = Modifier
            .height(height)
            .padding(horizontal = 8.dp)
            .clip(RoundedCornerShape(50)),
        thickness = 1.5.dp,
        color = NotesScreenHeaderDevider
    )
}

@Composable
private fun HeaderFacilityTitle(
    text: String, color: Color = Color.Black, modifier: Modifier = Modifier
) {
    Text(
        text = text,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        color = color,
        style = MaterialTheme.typography.labelLarge,
        modifier = modifier.padding(end = 8.dp)
    )
}

@Preview
@Composable
private fun NotesScreenHeaderTitleRowPreviews() {
    MyAppTheme {
        HeaderFacilityDetailRow(getHeader())

    }
}

@Preview
@Composable
private fun NotesScreenHeaderSearchPreviews() {
    MyAppTheme {
        SearchEnableView(
            notesScreenHeaderState = getHeader()
        ) {}
    }
}

@Preview
@Composable
private fun NotesScreenHeaderSecondRowPreviews() {
    MyAppTheme {
        Row(
            modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            HeaderOptionsRow(
                modifier = Modifier.weight(1f),
                notesScreenHeaderState = getHeader(), {}, {}
            )
        }

    }
}

@PreviewScreenSizes
@Preview
@Composable
private fun NotesScreenHeaderPreviews() {
    MyAppTheme {
        NotesScreenHeader(
            notesScreenHeaderState = getHeader(),
            onAction = {},
            navigationAction = {

            },
        )
    }
}

fun getHeader() = NotesScreenHeaderState(
    name = "SMU",
    parentFacility = "Parent facility",
    childFacility = "Child Facility login Facility Center",
    isSearchEnabled = false,
    date = "12/09/2026"
)