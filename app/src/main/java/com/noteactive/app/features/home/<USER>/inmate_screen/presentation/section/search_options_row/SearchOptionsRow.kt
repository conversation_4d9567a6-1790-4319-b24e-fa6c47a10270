package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.search_options_row

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenToolbarViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.InmateScreenState

@Composable
fun SearchOptionsRow(
    inmateScreenState: InmateScreenState,
    action: (InmateScreenToolbarViewModelAction) -> Unit
) {

    val listOfOptions = listOf(
        R.drawable.ic_search,
        R.drawable.ic_note_screen_sort,
        R.drawable.ic_sort_inmate,
    )
    Row(
        modifier = Modifier
            .padding(horizontal = 0.dp, vertical = 1.dp)
            .background(Color.White),
        verticalAlignment = Alignment.CenterVertically
    ) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(6.dp),
            modifier = Modifier.padding(8.dp)
        ) {
            itemsIndexed(
                listOfOptions
            ) { index, item ->
                val isLastItem = index == listOfOptions.lastIndex

                Image(
                    painter = painterResource(item),
                    contentDescription = null,
                    modifier = Modifier.clickable {
                        if (index == 2) {
                            action.invoke(InmateScreenToolbarViewModelAction.OnExpandToggle)
                        }
                    }
                )
                if (!isLastItem) Devider()
            }
        }
        SearchBarWithCamera()
    }

}

@Composable
fun SearchBarWithCamera(
    searchText: String = "",
    onSearchTextChange: (String) -> Unit = {},
    onCameraClick: () -> Unit = {},
    placeholder: String = "Search"
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(36.dp)
            .border(width = 1.dp, color = Color(0XFFD0D5DD)),
        shape = RoundedCornerShape(4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Search Icon
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "Search",
                tint = Color.Gray,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Search TextField
            BasicTextField(
                value = searchText,
                onValueChange = onSearchTextChange,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                singleLine = true,
                textStyle = MaterialTheme.typography.bodyMedium,
                decorationBox = { innerTextField ->
                    if (searchText.isEmpty()) {
                        Text(
                            text = placeholder,
                            color = Color.Gray,
                            fontSize = 16.sp
                        )
                    }
                    innerTextField()
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            VerticalDivider(color = Color(0XFFD0D5DD))
            // Camera Icon
            IconButton(
                onClick = onCameraClick,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_notes_screen_filter),
                    contentDescription = "Camera",
                    tint = Color.Black,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SearchBarPreview() {
    var searchText by remember { mutableStateOf("") }

    MyAppTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
                .padding(16.dp)
        ) {
            SearchBarWithCamera(
                searchText = searchText,
                onSearchTextChange = { searchText = it },
                onCameraClick = {
                    // Handle camera click
                    println("Camera clicked")
                }
            )
        }
    }
}

@Composable
private fun Devider() {
    VerticalDivider(
        modifier = Modifier
            .height(18.dp)
            .padding(horizontal = 8.dp)
            .clip(RoundedCornerShape(50)),
        thickness = 1.5.dp,
        color = NotesScreenHeaderDevider
    )
}

@PreviewScreenSizes
@Composable
private fun SearchOptionsRowPreview() {
    SearchOptionsRow (InmateScreenState()){

    }
}