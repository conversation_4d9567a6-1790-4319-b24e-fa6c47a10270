package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.IconWithBackgroundContainer
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.FilterOptionsViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.select_all_header.SelectAllHeader
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState

@Composable
fun ActiveNoteSection(
    state: FilterSectionState,
    navigationAction: (FilterOptionsViewModelAction) -> Unit
) {

    ActiveNoteItems(
        state = state,
        action = navigationAction::invoke
    )

}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ActiveNoteItems(
    modifier: Modifier = Modifier,
    state: FilterSectionState,
    action: (FilterOptionsViewModelAction) -> Unit
) {

    LazyColumn(modifier) {
        stickyHeader {
            SelectAllHeader(
                title = "All",
                searchText = state.activeNoteState.searchText,
                hintText = "Search Active Note",
                showSearchBar = true,
                isSelectAllEnabled = state.activeNoteState.isSelectAllEnable,
                onSelectAllToggle = {
                    action(FilterOptionsViewModelAction.OnSelectAllToggle)
                },
                onSearchTextChange = {
                    action(FilterOptionsViewModelAction.OnSearchTextChanged(it))
                },
                titleStyle = MaterialTheme.typography.bodyLarge,
                showSelectAllRow = state.activeNoteState.searchText.isEmpty()
            )
        }

        itemsIndexed(state.activeNoteState.filteredListState) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        action.invoke(
                            FilterOptionsViewModelAction.OnSelectToggle(
                                index
                            )
                        )
                    }
                    .padding(horizontal = 16.dp, vertical = 10.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))

                IconWithBackgroundContainer(
                    imageRes = item.icon,
                    tint = Color.Black,
                    bgColor = Color(0xFFF2F2F2),
                )
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.bodyLarge.copy(lineHeight = 1.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )


            }
            HorizontalDivider(color = NotesScreenHeaderDevider)
        }
    }
}

@PreviewScreenSizes
@Composable
fun ActiveNoteSectionPreview() {
    MyAppTheme {
        ActiveNoteSection(state = FilterSectionState()) {}
    }
}