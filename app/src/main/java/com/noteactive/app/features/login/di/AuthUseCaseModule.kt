package com.noteactive.app.features.login.di

import com.noteactive.app.features.login.domain.repository.AuthRepository
import com.noteactive.app.features.login.domain.usecase.GetAdvertisingIdUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object UseCaseModule {

    @Provides
    fun provideAdIdUseCase(
        userRepository: AuthRepository
    ) = GetAdvertisingIdUseCase(userRepository)

}
