package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.header

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.LightGray
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.authCardBackground
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderTextBlue
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.InmateScreenState
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenNavigationAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenToolbarViewModelAction

@Composable
fun InmateScreenHeader(
    modifier: Modifier = Modifier,
    inmateScreenState: InmateScreenState,
    onAction: (InmateScreenToolbarViewModelAction) -> Unit,
    navigationAction: (NotesScreenNavigationAction?) -> Unit,
) {
//    Card(
//        elevation = CardDefaults.cardElevation(12.dp),
//        shape = RectangleShape,
//        colors = CardDefaults.cardColors(containerColor = Color.White)
//    ) {
        Box(modifier = modifier.authCardBackground()) {
            Column {
                HeaderFacilityDetailRow(inmateScreenState)
                HorizontalDivider(thickness = 1.5.dp, color = NotesScreenHeaderDevider)
                HeaderInmateCountRow(inmateScreenState)
            }

        }
//    }

}

@Composable
fun HeaderInmateCountRow(inmateScreenState: InmateScreenState) {
    val listOf = listOf(
        inmateScreenState.totalCount,
        inmateScreenState.inTheCellCount,
        inmateScreenState.outOfCellCount,
        inmateScreenState.movementCount
    )

    LazyRow(
        modifier = Modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start),
        verticalAlignment = Alignment.Top,
    ) {
        itemsIndexed(listOf) { index, item ->
            Box(modifier = Modifier.size(50.dp), contentAlignment = Alignment.Center) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .border(
                            width = 1.5.dp, color = LightGray, shape = CircleShape
                        )
                        .clip(CircleShape)
                        .background(item.backgroundColor),
                    contentAlignment = Alignment.Center
                ) {

                    Text(
                        text = "" + item.count,
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center,
                        color = item.textColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier,
                    )
                }
                if (index == listOf.size - 1) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_inmate_header_movement),
                        contentDescription = null,
                        modifier = Modifier
                            .size(24.dp)
                            .padding(start = 10.dp)
                            .align(Alignment.CenterEnd)
                    )
                }

            }

        }

    }

}


@Composable
private fun HeaderFacilityDetailRow(inmateScreenState: InmateScreenState) {
    Row(
        modifier = Modifier
            .height(46.dp)
            .padding(horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        HeaderFacilityTitle(text = inmateScreenState.customerName)
        Devider()
        HeaderFacilityTitle(
            inmateScreenState.parentFacility, NotesScreenHeaderTextBlue
        )
        Devider()
        HeaderFacilityTitle(
            text = inmateScreenState.childFacility,
            color = NotesScreenHeaderTextBlue,
            modifier = Modifier.weight(1f) // makes last text stretch if needed
        )
        Icon(
            imageVector = Icons.Default.MoreVert,
            contentDescription = "Options",
            modifier = Modifier.padding(start = 8.dp)
        )
    }
}

@Composable
private fun Devider() {
    VerticalDivider(
        modifier = Modifier
            .height(18.dp)
            .padding(horizontal = 8.dp)
            .clip(RoundedCornerShape(50)),
        thickness = 1.5.dp,
        color = NotesScreenHeaderDevider
    )
}

@Composable
private fun HeaderFacilityTitle(
    text: String, color: Color = Color.Black, modifier: Modifier = Modifier
) {
    Text(
        text = text,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        color = color,
        style = MaterialTheme.typography.labelLarge,
        modifier = modifier.padding(end = 8.dp)
    )
}


@PreviewScreenSizes
@Preview
@Composable
private fun NotesScreenHeaderPreviews() {
    MyAppTheme {
        InmateScreenHeader(
            inmateScreenState = InmateScreenState(),
            onAction = {},
            navigationAction = {})
    }
}
