package com.noteactive.app.features.login.presentation.screens.activation_detail_screen

import android.widget.Toast
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Snackbar
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsNavigationAction
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsViewModelAction
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetailsState
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.FacilityApiState
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.getActivationDetailedInfo


@Composable
fun ActivationDetailScreenRoot(
    modifier: Modifier = Modifier,
    navigationAction: (ActivationDetailsNavigationAction) -> Unit
) {
    val viewModel: ActivationDetailsViewModel = hiltViewModel()
    val activationKeyState by viewModel.activationDetailState.collectAsStateWithLifecycle()
    val facilityApiState by viewModel.facilityApiState.collectAsStateWithLifecycle()

    ActivationDetailScreen(
        modifier = modifier,
        activationKeyState = activationKeyState,
        facilityApiState = facilityApiState,
        action = viewModel::onAction,
        navigationAction = navigationAction
    )
}

@Composable
fun ActivationDetailScreen(
    modifier: Modifier = Modifier,
    activationKeyState: ActivationDetailsState,
    facilityApiState: FacilityApiState,
    action: (ActivationDetailsViewModelAction) -> Unit,
    navigationAction: (ActivationDetailsNavigationAction) -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(facilityApiState) {
        when (facilityApiState) {
            is FacilityApiState.Success -> navigationAction(ActivationDetailsNavigationAction.GoToLoginScreen)

            is FacilityApiState.Error -> Toast.makeText(
                context,
                facilityApiState.message,
                Toast.LENGTH_SHORT
            ).show()

            else -> {}
        }
    }
    Column(modifier) {
        AppCommonToolbar(
            modifier = Modifier.shadow(2.dp),
            title = stringResource(R.string.activation_details_screen_title),
            onBackIconClicked = {
                navigationAction(ActivationDetailsNavigationAction.OnDismiss)
            })
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .weight(1f)
                .verticalScroll(rememberScrollState())
        ) {
            activationKeyState.activationDetails.forEach { detail ->
                Text(
                    text = detail.title,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(top = 12.dp, bottom = 4.dp)
                )
                PrimaryTextField(
                    value = detail.text,
                    onValueChange = {
                        action(
                            ActivationDetailsViewModelAction.OnInfoChanged(
                                detail.copy(text = it)
                            )
                        )
                    },
                    enabled = false,
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = detail.getTextFieldType()
                )
            }
        }
        MultiButtonBottomRow(
            modifier = Modifier,
            secondaryButtonTitle = R.string.cancel,
            primaryButtonTitle = R.string.submit,
            secondaryButtonClickListener = {
                navigationAction(ActivationDetailsNavigationAction.OnDismiss)
            },
            primaryButtonClickListener = {
                action(ActivationDetailsViewModelAction.OnSubmitClicked)
            })
    }
}

@Preview
@Composable
private fun ActivationDetailScreenRootPreview() {
    MyAppTheme {
        ActivationDetailScreen(
            activationKeyState = ActivationDetailsState(
                activationDetails = getActivationDetailedInfo(
                    ActivationKeyResponseUserInfo(
                        id = "1",
                        firstName = "Rakesh",
                        lastName = "Rana",
                        email = "<EMAIL>",
                        activationKey = "rrgdcdev1",
                        contactNumber = "*********",
                        companyName = "Info Tech Pvt. Ltd.",
                        address = "LA"
                    )
                )
            ),
            action = {},
            navigationAction = {},
            facilityApiState = FacilityApiState.Idle
        )
    }
}
