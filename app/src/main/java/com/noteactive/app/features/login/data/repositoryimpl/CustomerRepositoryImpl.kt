package com.noteactive.app.features.login.data.repositoryimpl

import com.noteactive.app.core.util.preferences.AppPreferences
import com.noteactive.app.features.login.data.model.dao.activation_key_dao.ActivationKeyResponseDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.FacilityDataSaveSource
import com.noteactive.app.features.login.domain.repository.CustomerRepository
import com.noteactive.app.features.login.presentation.screens.select_customer_screen.state.CustomerItemUi
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi
import javax.inject.Inject

class CustomerRepositoryImpl @Inject constructor(
    private val activationKeyResponseDao: ActivationKeyResponseDao,
    private val facilityDataSaveSource: FacilityDataSaveSource,
    private val appPreferences: AppPreferences
) : CustomerRepository {
    override fun getCustomerFacilities(): String = activationKeyResponseDao.getFacilities()

    override fun getCurrentCustomerKey(): String? = appPreferences.selectedCustomer

    override fun getFacilities(customerKey: String): List<FacilityItemUi> =
        facilityDataSaveSource.getAllFacility(customerKey)

    override suspend fun selectCustomer(customerKey: String) {
        appPreferences.selectedCustomer?.let {
            facilityDataSaveSource.selectCustomer(it)
        }
    }

    override suspend fun getAllCustomer(): List<CustomerItemUi> =
        facilityDataSaveSource.getAllCustomer()

}