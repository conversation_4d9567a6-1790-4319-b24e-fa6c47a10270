package com.noteactive.app.features.common.presentation.toolbar_search

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.textfield.TransparentHintTextField
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction

@Composable
fun ToolbarSearchView(
    modifier: Modifier = Modifier,
    title : String,
    hint : String,
    isSearchEnabled: Boolean = false,
    searchText: String = "",
    onSearchTextChanged: (String) -> Unit,
    onSearchCrossClicked: () -> Unit,
    onSearchEnabledClicked: () -> Unit,
    onBackIconClicked: () -> Unit
) {
    if (isSearchEnabled) {
        TransparentHintTextField(
            text = searchText,
            onValueChange = {
                onSearchTextChanged.invoke(it)
            },
            maxLines = 1,
            textStyle = MaterialTheme.typography.titleSmall.copy(color = Color.Black),
            hintText = hint,
            trailingIcon = {
                Icon(
                    imageVector = Icons.Default.Clear,
                    contentDescription = null,
                    modifier = Modifier.clickable {
                        onSearchCrossClicked.invoke()
                    })
            },
            modifier = Modifier
                .padding(horizontal = 12.dp)
                .height(50.dp)
        )
    } else {
        AppCommonToolbar(
            modifier = Modifier,
            title = title,
            endIcon = R.drawable.ic_search,
            onBackIconClicked = {

                onBackIconClicked.invoke()
            },
            onEndIconClicked = {
                onSearchEnabledClicked.invoke()
            })
    }

}

@PreviewScreenSizes
@Preview
@Composable
private fun ToolbarSearchViewPreview() {
    ToolbarSearchView(
        title = "Toolbar title",
        hint = "Select item",
        onSearchTextChanged = {},
        onSearchEnabledClicked = {},
        onSearchCrossClicked = {},
        onBackIconClicked = {})
}