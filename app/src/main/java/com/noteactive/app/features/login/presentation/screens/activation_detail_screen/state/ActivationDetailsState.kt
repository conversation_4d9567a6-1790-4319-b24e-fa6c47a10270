package com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state

import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo

data class ActivationDetailsState(
    val isLoading: Boolean = false,
    val activationDetails: List<ActivationDetails> = emptyList(),
)
data class ActivationDetails(
    val title: String,
    var text: String,
    val type: ActivationDetailTextFieldType,
) {
    fun getTextFieldType(): KeyboardOptions {
        var keyboardOption = KeyboardOptions.Default
        when (type) {
            ActivationDetailTextFieldType.TEXT -> {
                keyboardOption = keyboardOption.copy(keyboardType = KeyboardType.Text)
            }

            ActivationDetailTextFieldType.PASSWORD -> {
                keyboardOption = keyboardOption.copy(keyboardType = KeyboardType.Password)

            }

            ActivationDetailTextFieldType.EMAIL -> {
                keyboardOption = keyboardOption.copy(keyboardType = KeyboardType.Email)

            }

            ActivationDetailTextFieldType.MOBILE -> {
                keyboardOption = keyboardOption.copy(keyboardType = KeyboardType.Phone)

            }
        }
        return keyboardOption
    }
}

enum class ActivationDetailTextFieldType {
    TEXT,
    PASSWORD,
    EMAIL,
    MOBILE,
}


fun getActivationDetailedInfo(activationKeyResponseUserInfo: ActivationKeyResponseUserInfo?): List<ActivationDetails> {
    if (activationKeyResponseUserInfo == null) {
        return emptyList()
    } else {
       return listOf(
            ActivationDetails(
                title = "Client name",
                text = activationKeyResponseUserInfo.companyName,
                type = ActivationDetailTextFieldType.TEXT
            ),
            ActivationDetails(
                title = "Password",
                text = activationKeyResponseUserInfo.activationKey,
                type = ActivationDetailTextFieldType.PASSWORD,
            ),
            ActivationDetails(
                title = "User name",
                text = activationKeyResponseUserInfo.firstName + " " + activationKeyResponseUserInfo.lastName,
                type = ActivationDetailTextFieldType.TEXT,
            ),
            ActivationDetails(
                title = "Email",
                text = activationKeyResponseUserInfo.email,
                type = ActivationDetailTextFieldType.EMAIL,
            ),
            ActivationDetails(
                title = "Mobile",
                text = activationKeyResponseUserInfo.contactNumber,
                type = ActivationDetailTextFieldType.MOBILE,
            ),
            ActivationDetails(
                title = "Location",
                text = activationKeyResponseUserInfo.address,
                type = ActivationDetailTextFieldType.TEXT,
            )
        )

    }
}
