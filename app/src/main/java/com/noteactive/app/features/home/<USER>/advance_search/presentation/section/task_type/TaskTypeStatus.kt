package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.task_type

import com.noteactive.app.R

data class TaskTypeStatus(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<AdvanceSearchTaskType> = formState(),
    var filteredListState: List<AdvanceSearchTaskType> = formState(),
    var searchText: String = ""
)

data class AdvanceSearchTaskType(
    val icon: Int,
    val title: String,
    val id: String,
    val isSelected: Boolean
)


private fun formState() = listOf(
    AdvanceSearchTaskType(
        icon = R.drawable.ic_location_building,
        title = "Bed Check",
        isSelected = false,
        id = "1"
    ),
    AdvanceSearchTaskType(
        icon = R.drawable.ic_note_test1,
        title = "Fire watch",
        isSelected = false,
        id = "2"

    ),
    AdvanceSearchTaskType(
        icon = R.drawable.ic_fire,
        title = "Count",
        isSelected = false,
        id = "3"

    ),
    AdvanceSearchTaskType(
        icon = R.drawable.ic_note_test3,
        title = "Disciplinary Report Task",
        isSelected = false,
        id = "4"
    ),

    )