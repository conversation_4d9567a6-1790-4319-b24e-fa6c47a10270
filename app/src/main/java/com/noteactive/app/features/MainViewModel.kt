package com.noteactive.app.features

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.features.login.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.noteactive.app.features.login.domain.usecase.GetAdvertisingIdUseCase
import com.noteactive.app.navigation.Screens

@HiltViewModel
class MainViewModel @Inject constructor(
    private val getAdvertisingIdUseCase: GetAdvertisingIdUseCase,
    authRepository: AuthRepository
) : ViewModel() {
    private val _advertisingId = MutableLiveData<String>()
    val advertisingId: LiveData<String> = _advertisingId

    private val _errorFetchingAdId = MutableLiveData("")
    val errorFetchingAdId: LiveData<String> = _errorFetchingAdId

    var startDestination: String = if (authRepository.isLoggedIn()) {
        Screens.LoginScreen.route
    } else {
        Screens.EnterActivationKeyScreen.route
    }

    init {
        fetchAdvertisingId()

    }

    private fun fetchAdvertisingId() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val id = getAdvertisingIdUseCase.invoke()
                _advertisingId.postValue(id)
            } catch (e: Exception) {
                _errorFetchingAdId.postValue(e.message)
            }
        }
    }
}
