package com.noteactive.app.features.login.data.model.entity.response.activation_response

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

data class ActivationKeyResponse(
    @SerializedName("result")
    val result: List<ActivationKeyResponseUserInfo>,

    @SerializedName("status")
    val status: Boolean
)

@Entity(tableName = "activation_user_table")
data class ActivationKeyResponseUserInfo(
    @PrimaryKey
    @SerializedName("Id")
    val id: String = "",

    @SerializedName("FName")
    val firstName: String = "",

    @SerializedName("LName")
    val lastName: String = "",

    @SerializedName("Email_add")
    val email: String = "",

    @SerializedName("Contact_no")
    val contactNumber: String = "",

    @SerializedName("Company_name")
    val companyName: String = "",

    @SerializedName("Activitation_key")
    val activationKey: String = "",

    @SerializedName("Address")
    val address: String = "",

    @SerializedName("Android_id")
    val androidId: String = "",

    @SerializedName("Server_url")
    val serverUrl: String = "",

    @SerializedName("UserDate")
    val userDate: String = "",

    @SerializedName("facilities")
    val facilities: String = "",

    @SerializedName("login_facilities")
    val loginFacilities: String = "",

    @SerializedName("customer_id")
    val customerId: String = "",

    @SerializedName("asset_id")
    val assetId: String = "",

    @SerializedName("support_server_id")
    val supportServerId: String = "",

    @SerializedName("Server_url_other")
    val serverUrlOther: String = "",

    @SerializedName("version")
    val version: String = "",

    @SerializedName("version_v2")
    val versionV2: String = "",

    @SerializedName("is_updated")
    val isUpdated: String = "",

    @SerializedName("date_updated")
    val dateUpdated: String = "",

    @SerializedName("okta_enable")
    val oktaEnable: String = "",

    @SerializedName("is_all_customer_data")
    val isAllCustomerData: String = "",

    @SerializedName("user_id")
    val userId: String = "",

    @SerializedName("environment")
    val environment: String = "",

    @SerializedName("okta_offline")
    val oktaOffline: String = "",

    @SerializedName("log_password")
    val logPassword: String = ""
)

