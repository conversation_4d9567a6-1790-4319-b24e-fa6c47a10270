package com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.inmate_card_header

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBox
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.ClientProfile

@Composable
fun InmateCardHeader(modifier: Modifier = Modifier, clientProfile: ClientProfile) {

    // Header Section
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color(0xFFE9F2FF))
            .height(76.dp)
            .padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Profile Avatar
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        Color(0xFF4CAF50),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(R.drawable.inmate_demo_pic) ,
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize()
                )
                Image(
                    painter = painterResource(R.drawable.ic_client_bookmark),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.BottomStart)

                )
            }

            // Name and Details
            Column(modifier = Modifier.fillMaxWidth(.70f)) {
                Text(
                    text = clientProfile.name,
                    fontWeight = FontWeight.SemiBold,
                    style = MaterialTheme.typography.labelLarge,
                    color = Color(0xFF1F2937)
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.padding(top =2.dp )
                ) {
                    Text(
                        text = clientProfile.code1,
                        style = MaterialTheme.typography.bodySmall,
                        lineHeight = 14.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFF666666)
                    )
                }

            }
        }

        // Time
        Row(
            modifier = Modifier.padding(top = 12.dp).height(21.dp).clip(RoundedCornerShape(4.dp)).background(Color.White).padding(2.dp).align(Alignment.Top),
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_recreation),
                contentDescription = null,
                tint = Color(0xFF6B7280)
            )
            Text(
                text = clientProfile.time,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.W600,
                color = Color(0xFF2A333C),
                textAlign = TextAlign.Center
            )
            Icon(
                painter = painterResource(R.drawable.ic_inmate_recreation_down_arrow),
                contentDescription = null,
            )
        }
    }
}

@PreviewScreenSizes
@Composable
fun InmateCardHeaderPreview() {
    InmateCardHeader(clientProfile = ClientProfile())
}