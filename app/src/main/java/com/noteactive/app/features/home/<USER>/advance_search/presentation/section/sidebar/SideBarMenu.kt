package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.sidebar

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextSubTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesListChipContainerColor
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection.Companion.getAdvanceFilterList

@Composable
fun SidebarMenu(
    filters: List<FilterSection>,
    selectedFilter: FilterSection,
    onFilterClick: (FilterSection) -> Unit
) {

    LazyColumn(
        modifier = Modifier
            .width(100.dp)
            .background(Color.White)
    ) {
        items(filters) { filter ->
            SidebarMenuItem(
                title = filter.displayName,
                isSelected = filter.displayName == selectedFilter.displayName,
                onClick = { onFilterClick(filter) }
            )
        }
    }
}

@Composable
private fun SidebarMenuItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val fontStyle = if (isSelected) {
        MaterialTheme.typography.labelLarge
    } else {
        MaterialTheme.typography.bodyMedium
    }

    val textColor = if (isSelected) {
        FocusTextTitleColor
    } else {
        FocusTextSubTitleColor
    }

    val backgroundColor = if (isSelected) {
        Color.White
    } else {
        NotesListChipContainerColor
    }

    Column(modifier = Modifier.clickable(onClick = onClick)) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(52.dp)
                .background(backgroundColor),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (isSelected) {
                VerticalDivider(
                    thickness = 3.dp,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.height(52.dp)
                )
            }

            Text(
                text = title,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp),
                style = fontStyle,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = textColor
            )
        }

        HorizontalDivider(color = Color(0xFFE1E6EA))
    }
}

@Preview
@Composable
fun SidebarMenuPreview() {
    MyAppTheme {
        SidebarMenu(
            filters = getAdvanceFilterList(),
            selectedFilter = FilterSection.DATE_TIME,
            onFilterClick = {}
        )
    }
}
