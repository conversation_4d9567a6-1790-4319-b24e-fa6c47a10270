package com.noteactive.app.features.home.shownotes.notes.presentation.section

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.CustomFontNunitoRegular
import com.noteactive.app.core.presentation.designsystem.theme.NotesListChipBorderColor
import com.noteactive.app.core.presentation.designsystem.theme.NotesListChipContainerColor
import com.noteactive.app.core.presentation.designsystem.theme.NotesListChipTextColor

@Composable
fun NoteItemIcon(
    modifier: Modifier = Modifier.width(60.dp),
    @DrawableRes imageId: Int,
    isNotesScreen: Boolean = true
) {
    val color = if (isNotesScreen) {
        Color(0xFF2A333C)
    } else {
        Color(0xFF556777)
    }
    Box(
        modifier = modifier,
        contentAlignment = Alignment.BottomEnd
    ) {
        Icon(
            painter = painterResource(imageId),
            contentDescription = "Location",
            tint = color,
            modifier = Modifier.padding(end = 6.dp),
        )
    }
}


@Composable
fun NoteChip(modifier: Modifier = Modifier, text: String) {
    Box(
        modifier = modifier
            .background(NotesListChipContainerColor)
            .border(
                border = BorderStroke(
                    1.dp,
                    NotesListChipBorderColor
                ), RoundedCornerShape(4.dp)
            )
            .padding(4.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                painter = painterResource(R.drawable.ic_bottom_nav_notes),
                contentDescription = null,
                tint = Color.Black,
                modifier = Modifier.size(17.dp)
            )
            Text(
                text = text,
                style = MaterialTheme.typography.bodySmall.copy(color = NotesListChipTextColor),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                fontWeight = FontWeight.W400
            )

        }
    }
}

val numberOfHiddenItemsCountGradient = Brush.horizontalGradient(
    colors = listOf(
        Color.White.copy(alpha = 0f),
        Color.White.copy(alpha = .9f),
        Color.White
    )
)

fun getNoteTextStyle() = SpanStyle(
    fontSize = 14.sp,
    fontFamily = CustomFontNunitoRegular,
    fontWeight = FontWeight(400),
    color = Color(0xFF2A333C),
)

fun getNoteBoldTextStyle() = SpanStyle(
    fontSize = 14.sp,
    fontFamily = CustomFontNunitoRegular,
    fontWeight = FontWeight.W700,
    color = Color.Black,
)