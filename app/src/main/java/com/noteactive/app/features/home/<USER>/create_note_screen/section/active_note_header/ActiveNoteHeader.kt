package com.noteactive.app.features.home.createnote.create_note_screen.section.active_note_header

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow

@Composable
fun CreateActiveNoteHeader(
    note: NewNoteRow,
    onEditClicked: () -> Unit,
    onDeleteClicked: () -> Unit,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            note.activeNoteName,
            style = MaterialTheme.typography.labelLarge,
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        )
        Image(
            painter = painterResource(R.drawable.ic_edit),
            contentDescription = "",
            modifier = Modifier.padding(horizontal = 8.dp).clickable {
                onEditClicked.invoke()
            }
        )
        Image(
            painter = painterResource(R.drawable.ic_cross),
            contentDescription = "",
            modifier = Modifier
                .padding(end = 8.dp)
                .clickable {
                    onDeleteClicked.invoke()
                })
    }
}