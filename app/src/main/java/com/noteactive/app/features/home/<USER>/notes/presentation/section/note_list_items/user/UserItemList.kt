package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.user

import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteBoldTextStyle
import com.noteactive.app.features.home.shownotes.notes.presentation.section.getNoteTextStyle

@Composable
fun UserItemTitle(noteHeader: NoteHeader, noteContent: NoteContent, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {


        NoteItemIcon(imageId = R.drawable.ic_note_test1)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())

        Row {
            val title = buildAnnotatedString {
                withStyle(getNoteBoldTextStyle()) {
                    append("User : ")
                }
            }
            if (noteHeader.expanded) {
                Text(
                    text = title,
                    modifier = Modifier
                        .padding(horizontal = 0.dp, vertical = 4.dp)
                        .padding(start = 4.dp, top = 4.dp, bottom = 4.dp, end = 0.dp),
                )
            }
            //Kotlin
            //Compose
            //Clean Architecture
            //DI


            Icon(
                painter = painterResource(R.drawable.ic_signature_bottom_sheet_header_icon),
                contentDescription = "Location",
                modifier = Modifier
                    .size(24.dp)
                    .padding(end = 0.dp)
                    .align(Alignment.CenterVertically)
            )

            Text(
                text = buildAnnotatedString {
                    withStyle(getNoteTextStyle()) {
                        append(noteContent.user)
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 0.dp, vertical = 4.dp)
                    .padding(start = 4.dp, top = 4.dp, bottom = 4.dp, end = 0.dp),
            )
        }

    }
}

@PreviewScreenSizes
@Preview
@Composable
private fun NoteItemLocation() {
    MaterialTheme {
        UserItemTitle(
            noteHeader = NoteHeader(time = "1", expanded = false), noteContent = NoteContent(
                user = "Person Name", type = NoteItemType.SIGNATURE
            )
        )
    }
}

