package com.noteactive.app.features.home.createnote.select_offenders_screen.state

import com.noteactive.app.R

data class Offender(
    val id: String,
    val name: String,
    val image: Int,
    val location: String,
    val isChecked: Boolean = false,
)

data class SelectOffenderState(
    val offenders: List<Offender>,
    val filteredListOfOffender : List<Offender>,
    val selectedOffenders : List<Offender>,
    val isAllChecked: Boolean = false,
    val isSearchEnabled: Boolean = false,
    val searchText: String = "",
    val headerTitle: String = "${offenders.size} Offenders"
)


fun getOffenderDetails() = listOf(
    Offender(id = "110091", name = "Ahmed Jasmin 1", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110092", name = "Akarsh ", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110093", name = "Arun", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110094", name = "Testing Demo", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110095", name = "Aman", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110096", name = "Lucy", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110097", name = "Jasmin Sh. 2", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110098", name = "Lorem Jasmin", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110099", name = "Lorem Jasmin", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110100", name = "or Lorem", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110101", name = "Lorem Lorem", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110102", name = "rem Lorem", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110103", name = "Ahmed Jasmin", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    Offender(id = "110104", name = "Ahmed Jasmin", image = R.drawable.inmate_demo_pic, location = "4F Demo and Training"),
    )