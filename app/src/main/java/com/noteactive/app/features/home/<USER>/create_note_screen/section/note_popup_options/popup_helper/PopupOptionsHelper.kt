package com.noteactive.app.features.home.createnote.create_note_screen.section.note_popup_options.popup_helper

//import androidx.compose.runtime.Immutable
//import com.noteactive.app.R
//
//
//@Immutable
//data class NoteOptionsPopup(
//    val icon: Int,
//    val type: AddNoteActionEnum,
//    val isToggle: Boolean = false
//)
//
//enum class AddNoteActionEnum {
//    ACTIVE_NOTE,
//    OFFENDERS,
//    LOCATION,
//    BUILDING,
//    FORM,
//    NEXT,
//    PREVIOUS,
//    CAMERA,
//    MIC,
//    EDIT,
//    TEXT,
//    USER,
//    STRIKE,
//    ATTACH,
//    NONE,
//}
//
//val mainPopupItems = listOf(
//    NoteOptionsPopup(R.drawable.active_note_icon, AddNoteActionEnum.ACTIVE_NOTE),
//    NoteOptionsPopup(R.drawable.ic_bottom_nav_offender, AddNoteActionEnum.OFFENDERS),
//    NoteOptionsPopup(R.drawable.ic_location_building, AddNoteActionEnum.LOCATION),
//    NoteOptionsPopup(R.drawable.iv_location, AddNoteActionEnum.BUILDING),
//    NoteOptionsPopup(R.drawable.ic_note_test3, AddNoteActionEnum.USER),
//    NoteOptionsPopup(R.drawable.ic_form, AddNoteActionEnum.FORM),
//    NoteOptionsPopup(
//        R.drawable.ic_right_arrow,
//        AddNoteActionEnum.NEXT,
//        isToggle = true
//    ) // Last icon switches
//)
//
//val secondaryOptionsItems = listOf(
//    NoteOptionsPopup(
//        R.drawable.left_arrow,
//        AddNoteActionEnum.PREVIOUS,
//        isToggle = true
//    ), // First icon switches back
//    NoteOptionsPopup(R.drawable.ic_camera, AddNoteActionEnum.CAMERA),
//    NoteOptionsPopup(R.drawable.ic_mic, AddNoteActionEnum.MIC),
//    NoteOptionsPopup(R.drawable.ic_highlighter, AddNoteActionEnum.EDIT),
//    NoteOptionsPopup(R.drawable.ic_underline, AddNoteActionEnum.TEXT),
//    NoteOptionsPopup(R.drawable.ic_strikethrough, AddNoteActionEnum.STRIKE),
//    NoteOptionsPopup(R.drawable.ic_attachment, AddNoteActionEnum.ATTACH)
//)