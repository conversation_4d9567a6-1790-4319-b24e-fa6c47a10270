package com.noteactive.app.features.home.inmate.inmate_screen.presentation.state

import androidx.compose.material3.Icon
import androidx.compose.ui.graphics.Color
import com.noteactive.app.R

data class InmateScreenState(
    val customerName: String = "SMU",
    val parentFacility: String = "Parent facility",
    val childFacility: String = "Child Facility long te...",
    val inTheCellCount: HeaderCount = HeaderCount(count = 800, textColor = Color(0xFF5E9900)),
    val outOfCellCount: HeaderCount = HeaderCount(count = 9, textColor = Color(0xFFCC1400)),
    val movementCount: HeaderCount = HeaderCount(count = 1, textColor = Color(0xFFCCA300)),
    val totalCount: HeaderCount = HeaderCount(
        count = 810,
        textColor = Color(0xFFFFFFFF),
        backgroundColor = Color(0xFF666666)
    ),
    val isExpanded: Boolean = false,
    val headerIcons: List<Icons> = mutableListOf(
        Icons(R.drawable.ic_notes_screen_filter, type = ImageType.FILTER, false),
        Icons(R.drawable.ic_note_screen_sort, type = ImageType.SORT, false),
        Icons(R.drawable.ic_search, type = ImageType.SEARCH, false),
        Icons(R.drawable.ic_sort_inmate, type = ImageType.EXPAND_TOGGLE, false),
    ),
    val profiles: List<ClientProfile> = mutableListOf(
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
        ClientProfile(),
    )
)

data class HeaderCount(
    val count: Int,
    val backgroundColor: Color = Color(0XFFFFFFFF),
    val textColor: Color = Color(0xFFFFFFFF)
)

data class ClientProfile(
    val name: String = "Ahmed Jasmin",
    var isExpanded: Boolean = false,
    val code1: String = "PSP2164 | 4F demo and training |  training  |  training",
    val time: String = "15:00",
    val workDetails: String = "Enroute, Kitchen",
    val cancelledText: String = "Cancelled program, 12 sep 2024, 14:00",
    val otherTags: List<String> = listOf("Mental Health", "ADA"),
    val locationTags: List<String> = listOf("Gym |", "lorem |", "lorem |", "lorem |", "lorem |", "lorem")
)


data class Icons(
    val image: Int,
    val type: ImageType,
    var isSelected: Boolean
)

enum class ImageType {
    FILTER,
    SORT,
    SEARCH,
    EXPAND_TOGGLE
}
