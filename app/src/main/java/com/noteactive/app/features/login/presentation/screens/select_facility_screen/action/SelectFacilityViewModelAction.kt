package com.noteactive.app.features.login.presentation.screens.select_facility_screen.action

import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.FacilityItemUi

sealed interface SelectFacilityViewModelAction {

    data object OnSubmitClickedAction : SelectFacilityViewModelAction
    data class OnFacilitySelectAction(val facility: FacilityItemUi) : SelectFacilityViewModelAction
    data class OnSearchFacilityTextAction(var searchText: String) : SelectFacilityViewModelAction
    data object  OnEnableSearch : SelectFacilityViewModelAction
    data object  OnSearchCrossClicked : SelectFacilityViewModelAction

}