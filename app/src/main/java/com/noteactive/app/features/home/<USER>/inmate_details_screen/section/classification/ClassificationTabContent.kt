package com.noteactive.app.features.home.inmate.inmate_details_screen.section.classification

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.theme.LightColorPalette
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesListChipBorderColor
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.headerToolbar.CommonHeaderBar
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.InmateDetailsState

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ClassificationTabContent(inmateDetailsState: InmateDetailsState) {
    Column(
        modifier = Modifier.padding(horizontal = 16.dp, vertical = 3.dp)
    ){
        CommonHeaderBar(
            title = "${inmateDetailsState.classification.size} Classification",
            showFilter = true,
            showSort = false,
            showSearch = true,
            onSearchChange = {},
            searchText = ""
        )

        FlowRow(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            inmateDetailsState.classification.forEach { classification ->
                AssistChip(
                    onClick = { },
                    label = { Text( classification.label ,
                        style = MaterialTheme.typography.labelMedium.copy(color = Color(0XFFA5B3C0)))},
                    leadingIcon = {
                        Image(
                            painter = painterResource(id = classification.image),
                            contentDescription = null,
                            modifier = Modifier.size(20.dp).clip(CircleShape)
                        )
                    },
                    colors = AssistChipDefaults.assistChipColors(MaterialTheme.colorScheme.background),
                    border = BorderStroke(width = 1.dp, color = NotesListChipBorderColor)
                )
            }
        }

        PrimaryButton(text = stringResource(R.string.add) + "/" + stringResource(R.string.edit),
            modifier = Modifier.padding(top = 16.dp), buttonClick = {})
    }
}

@PreviewScreenSizes
@Composable
private fun ClassificationTabContentPreview() {
    MyAppTheme {
        ClassificationTabContent(inmateDetailsState = InmateDetailsState())
    } }