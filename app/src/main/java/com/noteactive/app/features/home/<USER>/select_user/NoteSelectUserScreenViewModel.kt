package com.noteactive.app.features.home.createnote.select_user

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.createnote.select_user.action.NoteSelectUserScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_user.state.NoteSelectUserState
import com.noteactive.app.features.home.createnote.select_user.state.SelectUser
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class NoteSelectUserScreenViewModel @Inject constructor() : ViewModel() {
    private val _noteSelectUserScreenState = MutableStateFlow(NoteSelectUserState())
    val noteSelectUserScreenState = _noteSelectUserScreenState.asStateFlow()

    fun action(viewModelAction: NoteSelectUserScreenViewModelAction) {
        when (viewModelAction) {

            NoteSelectUserScreenViewModelAction.OnEnableSearch -> {
                _noteSelectUserScreenState.update { it.copy(isSearchEnabled = true) }
            }

            NoteSelectUserScreenViewModelAction.OnSearchCrossClicked -> {
                val selectedCount =
                    _noteSelectUserScreenState.value.users.count { it.isSelected }
                val countLabel =
                    if (selectedCount > 0) "$selectedCount users" else "Select users"

                _noteSelectUserScreenState.update {
                    it.copy(
                        isSearchEnabled = false,
                        searchText = "",
                        filterUsers = it.users
                    )
                }
            }

            is NoteSelectUserScreenViewModelAction.OnSearchTextChanged -> {
                val searchText = viewModelAction.text
                val filteredList = _noteSelectUserScreenState.value.users.filter {
                    it.userName.contains(searchText, ignoreCase = true)
                }
                val selectedCount = filteredList.count { it.isSelected }
                val countLabel =
                    if (selectedCount > 0) "$selectedCount users" else "Select user"

                _noteSelectUserScreenState.update {
                    it.copy(
                        searchText = searchText,
                        filterUsers = filteredList,
                        count = countLabel
                    )
                }
            }

            is NoteSelectUserScreenViewModelAction.OnSelectToggle -> {
                val currentState = _noteSelectUserScreenState.value
                val updatedList = currentState.filterUsers.mapIndexed { index, users ->
                    if (index == viewModelAction.index) {
                        users.copy(isSelected = !users.isSelected)
                    } else users
                }
                updateSelectedUserState(updatedList)
            }

            NoteSelectUserScreenViewModelAction.OnSelectAllToggle -> {
                val currentState = _noteSelectUserScreenState.value
                val toggle = !currentState.isSelectAllToggle
                val updatedList = currentState.filterUsers.map {
                    it.copy(isSelected = toggle)
                }
                _noteSelectUserScreenState.update {
                    it.copy(
                        isSelectAllToggle = toggle
                    )
                }
                updateSelectedUserState(updatedList)
            }
        }
    }

    private fun updateSelectedUserState(updatedFilteredList: List<SelectUser>) {

        // Replace matching items in the full list
        val fullList = _noteSelectUserScreenState.value.users.map { original ->
            updatedFilteredList.find { it.userId == original.userId } ?: original
        }
        val selectedCount = updatedFilteredList.count { it.isSelected }
        val countLabel = if (selectedCount > 0) "$selectedCount user" else "Select user"

        _noteSelectUserScreenState.update {
            it.copy(
                users = fullList,
                filterUsers = updatedFilteredList,
                count = countLabel
            )
        }
    }

    fun getSelectedUsersList(): List<SelectUser> =
        _noteSelectUserScreenState.value.users.filter { it.isSelected }

}

