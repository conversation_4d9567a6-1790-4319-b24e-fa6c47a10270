package com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.FacilityApiResponse
import com.noteactive.app.core.util.network.ApiResponse


sealed class FacilityApiState {
    object Idle : FacilityApiState()
    object Loading : FacilityApiState()
    data class Success(val data: ApiResponse<List<FacilityApiResponse>>) : FacilityApiState()
    data class Error(val message: String) : FacilityApiState()
}
