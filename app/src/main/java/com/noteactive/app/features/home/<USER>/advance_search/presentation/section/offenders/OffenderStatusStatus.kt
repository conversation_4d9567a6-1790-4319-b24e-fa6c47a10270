package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.offenders

import com.noteactive.app.R

data class OffenderStatusState(
    val isSelectAllEnable: Boolean = false,
    var listItems: List<AdvanceSearchOffenderStatus> = formState(),
    var filteredItems: List<AdvanceSearchOffenderStatus> = formState(),
    var searchText: String = ""
)


data class AdvanceSearchOffenderStatus(
    val icon: Int,
    val title: String,
    val isSelected: Boolean,
    val id: String
)


private fun formState() = listOf(
    AdvanceSearchOffenderStatus(
        icon = R.drawable.ic_note_test3,
        title = "Attorney Phone Call ",
        isSelected = false,
        id = "1"
    ),
    AdvanceSearchOffenderStatus(
        icon = R.drawable.ic_note_test1,
        title = "Attorney visit",
        isSelected = false,
        id = "2"
    ),
    AdvanceSearchOffenderStatus(
        icon = R.drawable.ic_location_building,
        title = "Test Movement",
        isSelected = false,
        id = "4"
    ),
    AdvanceSearchOffenderStatus(
        icon = R.drawable.iv_location,
        title = "Movement ",
        isSelected = false,
        id = "10"
    ),
    AdvanceSearchOffenderStatus(
        icon = R.drawable.ic_note_test1,
        title = "Attorney visit",
        isSelected = false,
        id = "11"
    ),
    AdvanceSearchOffenderStatus(
        icon = R.drawable.ic_location_building,
        title = "Test Movement",
        isSelected = false,
        id = "19"
    ),
)