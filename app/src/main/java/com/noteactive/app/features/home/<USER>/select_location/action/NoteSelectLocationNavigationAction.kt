package com.noteactive.app.features.home.createnote.select_location.action

import com.noteactive.app.features.home.createnote.select_facility.action.NoteSelectFacilityNavigationAction
import com.noteactive.app.features.home.createnote.select_facility.state.SelectFacility
import com.noteactive.app.features.home.createnote.select_location.state.SelectLocation

sealed interface NoteSelectLocationNavigationAction {
    data class OnSelectLocationAction(val location: List<SelectLocation>) :
        NoteSelectLocationNavigationAction

    data object OnBackPressed : NoteSelectLocationNavigationAction

}