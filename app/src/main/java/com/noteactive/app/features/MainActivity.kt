package com.noteactive.app.features

import android.os.Bundle
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.runtime.livedata.observeAsState
import androidx.fragment.app.FragmentActivity
import androidx.navigation.compose.rememberNavController
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.navigation.AppNavigation
import com.noteactive.app.navigation.Screens
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : FragmentActivity() {
    private val viewModel: MainViewModel by viewModels()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MyAppTheme {
                val advertisingId = viewModel.advertisingId.observeAsState()
                advertisingId.value?.let {advertisingId->
                    if (advertisingId.isNotEmpty()) {
                        val navHostController = rememberNavController()
                        val startDestination = viewModel.startDestination
                        AppNavigation(navController = navHostController,startDestination = startDestination)

                    }
                }
            }
        }
        viewModel.errorFetchingAdId.observe(this) {
            if (it.isNotEmpty()) {
                Toast.makeText(baseContext, "" + it, Toast.LENGTH_SHORT).show()
            }
        }
    }
}


