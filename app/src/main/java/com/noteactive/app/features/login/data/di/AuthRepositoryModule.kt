package com.noteactive.app.features.login.data.di

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntrySource
import com.noteactive.app.core.data.repositoryImpl.BaseRepositoryImpl
import com.noteactive.app.core.util.preferences.AppPreferences
import com.noteactive.app.features.login.data.model.dao.activation_key_dao.ActivationKeyResponseDao
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.dao.FacilityDataSaveSource
import com.noteactive.app.features.login.data.network.api.CommonApiService
import com.noteactive.app.features.login.data.network.api.LicenceApiService
import com.noteactive.app.features.login.domain.repository.AuthRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import com.noteactive.app.features.login.data.repositoryimpl.AuthRepositoryImpl
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object AuthRepositoryModule {
    @Provides
    fun provideUserRepository(
        apiService: LicenceApiService,
        commonApiService: CommonApiService,
        activationKeyDao: ActivationKeyResponseDao,
        dataSource: FacilityDataSaveSource,
        userDataSource : UserEntrySource,
        appPreferences: AppPreferences,
        baseRepositoryImpl: BaseRepositoryImpl
    ): AuthRepository {
        return AuthRepositoryImpl(
            apiService,
            commonApiService,
            activationKeyDao,
            dataSource,
            userDataSource,
            appPreferences,
            baseRepositoryImpl,
        )
    }
}
