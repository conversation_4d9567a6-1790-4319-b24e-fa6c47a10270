package com.noteactive.app.features.home.shownotes.advance_search.presentation.action

import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection

sealed interface AdvanceSearchViewModelAction {

    data class OnSelectFilter(val filterSection: FilterSection) : AdvanceSearchViewModelAction
    data class OnKeywordTextChange(val keyword: String) : AdvanceSearchViewModelAction
    data class OnSelectDateAndTime(
        val startDate: String,
        val startTime: String,
        val endDate: String,
        val endTime: String,
    ) : AdvanceSearchViewModelAction

    data class OnSearchText(val filterSection: FilterSection, val text: String) :
        AdvanceSearchViewModelAction

    data class OnSelectAllToggle(val filterSection: FilterSection) :
        AdvanceSearchViewModelAction

    data class OnSelectToggle(val filterSection: FilterSection, val index: Int) :
        AdvanceSearchViewModelAction
}