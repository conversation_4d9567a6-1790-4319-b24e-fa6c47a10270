package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.offenders.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import javax.inject.Inject

class OffenderStatusFilter<PERSON>andler @Inject constructor() : FilterHandler {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val offenders = state.offenderStatusState
        val isSelectAllEnabledReverse = !offenders.isSelectAllEnable
        val selectAllItems = offenders.listItems.map {
            it.copy(isSelected = isSelectAllEnabledReverse)
        }

        return state.copy(
            offenderStatusState = offenders.copy(
                isSelectAllEnable = isSelectAllEnabledReverse,
                listItems = selectAllItems,
                filteredItems = selectAllItems
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val offenders = state.offenderStatusState
        val offendersList = offenders.filteredItems.toMutableList()
        var selectedItem = offendersList[index]
        selectedItem = selectedItem.copy(isSelected = !selectedItem.isSelected)
        offendersList[index] = selectedItem

        val listItems = offenders.listItems.map {
            if (it.id == selectedItem.id) it.copy(isSelected = selectedItem.isSelected) else it
        }
        val isSelected = listItems.all { it.isSelected }

        return state.copy(
            offenderStatusState = offenders.copy(
                isSelectAllEnable = isSelected,
                filteredItems = offendersList,
                listItems = listItems
            )
        )
    }

    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val status = state.offenderStatusState
        val selectedIds =
            (status.listItems + status.filteredItems).filter { it.isSelected }.map { it.id }.toSet()
        val filtered = status.listItems.filter {
            it.title.contains(searchText, ignoreCase = true)
        }.map {
            it.copy(isSelected = selectedIds.contains(it.id))
        }
        return state.copy(
            offenderStatusState = status.copy(
                searchText = searchText,
                filteredItems = filtered
            )
        )
    }
}
