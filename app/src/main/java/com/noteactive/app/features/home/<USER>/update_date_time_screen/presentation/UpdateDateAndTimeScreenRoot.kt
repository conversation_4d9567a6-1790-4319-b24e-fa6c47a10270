package com.noteactive.app.features.home.createnote.update_date_time_screen.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.composable.textfield.CommentOutlinedTextField
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.home.createnote.update_date_time_screen.action.UpdateDateAndTimeNavigationAction
import com.noteactive.app.features.home.createnote.update_date_time_screen.action.UpdateDateAndTimeViewModelAction
import com.noteactive.app.features.home.createnote.update_date_time_screen.state.UpdateDateAndTimeState
import com.noteactive.app.navigation.Screens

@Composable
fun UpdateDateAndTimeScreenRoot(
    navController: NavHostController = rememberNavController()
) {
    val viewModel: UpdateDateAndTimeViewModel = hiltViewModel()
    val state by viewModel.updateDateAndTimeState.collectAsStateWithLifecycle()
    UpdateDateAndTimeScreen(
        state, viewModel::action,
        navigationAction = { navAction ->
            when (navAction) {
                UpdateDateAndTimeNavigationAction.OnSelectDate ->
                    navController.navigate(Screens.DateRangePickerScreen.route)

                UpdateDateAndTimeNavigationAction.OnSelectTime ->
                    navController.navigate(Screens.TimePickerScreen.route)
            }
        }
    )
}

@Composable
fun UpdateDateAndTimeScreen(
    state: UpdateDateAndTimeState,
    action: (UpdateDateAndTimeViewModelAction) -> Unit,
    navigationAction: (UpdateDateAndTimeNavigationAction) -> Unit
) {
    Scaffold(
        topBar = {
            AppCommonToolbar(
                title = stringResource(R.string.update_date_time_toolbar_title),
                onBackIconClicked = {},
            )
        },
        bottomBar = {
            FooterButtonItem(onPrimaryButtonClick = {}, onSecondaryButtonClick = {},
                isPrimaryButtonEnabled = state.date.isNotEmpty() && state.time.isNotEmpty() && state.comment.isNotEmpty()
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(Color.White)
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .verticalScroll(rememberScrollState())
        ) {
            TitleWithTextField(title = stringResource(R.string.select_time_textfield_title), value = state.time, onClick = {
                navigationAction(UpdateDateAndTimeNavigationAction.OnSelectTime)
            })

            TitleWithTextField(title = stringResource(R.string.select_date_textfield_title), value = state.date, onClick = {
                navigationAction(UpdateDateAndTimeNavigationAction.OnSelectDate)
            })

            Text(
                text = stringResource(R.string.comment_textfield_title),
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(bottom = 2.dp),
                color = Color.Black
            )
            CommentOutlinedTextField(
                value = state.comment,
                onValueChange = { action(UpdateDateAndTimeViewModelAction.OnCommentChanged(it)) },
                trailingIcon = {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_scaling),
                        contentDescription = null,
                        tint = Color.Black,
                        modifier = Modifier.size(8.dp).clickable {  }
                    )
                }
            )
        }
    }
}

@Composable
fun TitleWithTextField(
    title: String,
    value: String,
    onValueChange: ((String) -> Unit)? = null,
    hintText: String? = null,
    onClick: (() -> Unit)
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(bottom = 2.dp),
            color = Color.Black
        )
        PrimaryTextField(
            value = value,
            onValueChange = { onValueChange?.invoke(it) },
            enabled = onValueChange != null,
            hintText = hintText,
            textStyle = MaterialTheme.typography.bodyMedium.copy(Color.Black),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
                .clickable { onClick() }
        )
    }
}

@Composable
fun FooterButtonItem(onPrimaryButtonClick: () -> Unit, onSecondaryButtonClick: () -> Unit,
                     isPrimaryButtonEnabled : Boolean) {
    MultiButtonBottomRow(
        secondaryButtonTitle = R.string.cancel,
        primaryButtonTitle = R.string.save,
        secondaryButtonClickListener = { onSecondaryButtonClick.invoke() },
        primaryButtonClickListener = { onPrimaryButtonClick.invoke() },
        isPrimaryButtonEnabled = isPrimaryButtonEnabled)
}

@PreviewScreenSizes
@Composable
private fun UpdateDateAndTimeScreenRootPreview() {
    MyAppTheme {
        UpdateDateAndTimeScreen(
            state = UpdateDateAndTimeState(
                time = "12:20",
                date = "07/07/2025",
                comment = ""
            ), action = {},
            navigationAction = {})
    }
}