package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note.ActiveNoteState
import javax.inject.Inject

class ActiveNoteFilterHandler @Inject constructor() : <PERSON><PERSON><PERSON><PERSON><PERSON> {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val activeNoteState = state.activeNoteState
        val isSelectAllEnabledReverse = !activeNoteState.isSelectAllEnable
        val selectAllItems = activeNoteState.listItems.map {
            it.copy(isSelected = isSelectAllEnabledReverse)
        }
        return state.copy(
            activeNoteState = ActiveNoteState(
                isSelectAllEnable = isSelectAllEnabledReverse,
                listItems = selectAllItems,
                filteredListState = selectAllItems,
            )
        )
    }

    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val activeNoteState = state.activeNoteState
        val filteredList = activeNoteState.filteredListState.toMutableList()
        val item = filteredList[index]
        val updatedItem = item.copy(isSelected = !item.isSelected)
        filteredList[index] = updatedItem
        val updatedList = state.activeNoteState.listItems.map {
            if (it.id == updatedItem.id) it.copy(isSelected = updatedItem.isSelected) else it
        }


        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            activeNoteState = activeNoteState.copy(
                isSelectAllEnable = allSelected,
                listItems = updatedList,
                filteredListState = filteredList,
            )
        )
    }


    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val notes = state.activeNoteState
        val selectedIds =
            (notes.listItems + notes.filteredListState).filter { it.isSelected }.map { it.id }
                .toSet()
        val filtered = notes.listItems.filter {
            it.title.contains(searchText, ignoreCase = true)
        }.map {
            it.copy(isSelected = selectedIds.contains(it.id))
        }
        return state.copy(
            activeNoteState = notes.copy(
                searchText = searchText, filteredListState = filtered
            )
        )
    }
}