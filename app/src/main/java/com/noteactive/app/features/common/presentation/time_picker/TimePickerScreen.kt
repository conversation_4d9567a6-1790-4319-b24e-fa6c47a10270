package com.noteactive.app.features.common.presentation.time_picker

import android.content.res.Configuration
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerDefaults
import androidx.compose.material3.TimePickerLayoutType
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.core.presentation.designsystem.composable.button.SecondaryButton
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.common.presentation.time_picker.actions.TimePickerNavigationAction

@Composable
fun TimePickerStateRoot(
    modifier: Modifier = Modifier,
    action: (TimePickerNavigationAction) -> Unit
) {
    val timePickerState = rememberTimePickerState()

    val configuration = LocalConfiguration.current
    val isLandscape = configuration.orientation == Configuration.ORIENTATION_LANDSCAPE


    val timePickerDefaultsColors = TimePickerDefaults.colors(
        clockDialUnselectedContentColor = Color.Black,
        timeSelectorSelectedContainerColor = MaterialTheme.colorScheme.background,
        timeSelectorUnselectedContainerColor = MaterialTheme.colorScheme.background,
        timeSelectorUnselectedContentColor = Color.Black,
        periodSelectorUnselectedContentColor = Color.Black,
        clockDialColor = MaterialTheme.colorScheme.background
    )

    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {

        val padding =
            if (isLandscape) {
                0.dp
            } else {
                20.dp
            }
        if (!isLandscape) {
            HeaderToolbar(action)
        }


        TimePicker(
            state = timePickerState,
            modifier = Modifier
                .weight(1f)
                .padding(top = padding),
            colors = timePickerDefaultsColors,
            layoutType = if (isLandscape) {
                TimePickerLayoutType.Horizontal
            } else {
                TimePickerLayoutType.Vertical
            }

        )
        TimePickerBottomButtons(action, timePickerState)
    }

}

@Composable
private fun HeaderToolbar(action: (TimePickerNavigationAction) -> Unit) {
    AppCommonToolbar(
        title = stringResource(R.string.select_time_title),
        modifier = Modifier.shadow(2.dp),
        onBackIconClicked = {
            action.invoke(TimePickerNavigationAction.OnDismiss)
        }
    )
}

@Composable
private fun TimePickerBottomButtons(
    action: (TimePickerNavigationAction) -> Unit,
    timePickerState: TimePickerState
) {
    Row(
        modifier = Modifier
            .shadow(1.dp)
            .padding(top = 12.dp, start = 16.dp, end = 16.dp, bottom = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        SecondaryButton(
            text = stringResource(R.string.time_picker_close_button_title),
            modifier = Modifier.weight(1f)
        ) {
            action.invoke(
                TimePickerNavigationAction.OnDismiss
            )
        }
        PrimaryButton(
            text = stringResource(R.string.time_picker_update_button_title),
            modifier = Modifier.weight(1f)
        ) {
            val selectedHour = timePickerState.hour
            val selectedMinute = timePickerState.minute
            action.invoke(
                TimePickerNavigationAction.OnDateSelectedComplete(
                    selectedHour,
                    selectedMinute
                )
            )
        }
    }
}


@PreviewScreenSizes
@Preview
@Composable
fun TimePickerStateRootPreview(modifier: Modifier = Modifier) {
    MyAppTheme {
        TimePickerStateRoot {}
    }
}