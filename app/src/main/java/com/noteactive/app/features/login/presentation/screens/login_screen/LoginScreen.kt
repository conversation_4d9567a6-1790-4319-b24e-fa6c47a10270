package com.noteactive.app.features.login.presentation.screens.login_screen

import android.content.res.Configuration
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.noteactive.app.core.presentation.designsystem.composable.button.PrimaryButton
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.authCardBackground
import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.screens.ExampleScreen
import com.noteactive.app.core.presentation.designsystem.composable.circular_menu.presentation.screens.FloatingButtonPanel
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.login_screen.action.LoginScreenNavigation
import com.noteactive.app.features.login.presentation.screens.login_screen.action.LoginScreenViewModelAction
import com.noteactive.app.features.login.presentation.screens.login_screen.state.LoginState

@Composable
fun LoginScreenRoot(
    modifier: Modifier = Modifier,
    navController: NavController,
    onNavigation: (LoginScreenNavigation) -> Unit,
) {
    val context = LocalContext.current

    val viewModel: LoginScreenViewModel = hiltViewModel()
    val loginState by viewModel.loginScreenState.collectAsStateWithLifecycle()
    val error by viewModel.error.collectAsStateWithLifecycle()
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    val facilityResult = savedStateHandle?.getLiveData<String>("facilityResult")?.observeAsState()
    val customerResult = savedStateHandle?.getLiveData<String>("customerResult")?.observeAsState()
    val customerKey = savedStateHandle?.getLiveData<String>("customerKey")?.observeAsState()

    LaunchedEffect(facilityResult?.value) {
        facilityResult?.value?.let { facilityName ->
            println("Facility Id: $facilityName")
            viewModel.onAction(LoginScreenViewModelAction.OnFacilityChanged(facilityName))
            savedStateHandle.remove<String>("facilityResult")
        }
    }
    LaunchedEffect(customerResult?.value) {
        customerResult?.value?.let { customerName ->

            println("Facility Id: $customerName")
            customerKey?.value?.let{key->
                viewModel.onAction(LoginScreenViewModelAction.OnCustomerChanged(customerName,key))
            }
            savedStateHandle.remove<String>("facilityResult")
        }
    }
    LaunchedEffect(error) {
        if(error.isNotEmpty()){
            Toast.makeText(context,error,Toast.LENGTH_LONG).show()
            viewModel.onAction(LoginScreenViewModelAction.OnError(""))
        }
    }

//    if(loginState.customerKey.isEmpty()){
//
//    }
    LoginScreen(
        modifier,
        loginState,
        action = viewModel::onAction,
        onNavigation = { navAction -> onNavigation(navAction) })


}

@Composable
private fun LoginScreen(
    modifier: Modifier = Modifier,
    loginState: LoginState,
    action: (LoginScreenViewModelAction) -> Unit,
    onNavigation: (LoginScreenNavigation) -> Unit

) {
    val customerNotSelectedError = stringResource(R.string.login_error_select_customer_first)

    val configuration = LocalConfiguration.current
    val imagePadding = when (configuration.orientation) {
        Configuration.ORIENTATION_PORTRAIT -> 120.dp
        else -> {
            40.dp
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {

        Box(
            modifier = Modifier
                .fillMaxWidth(.75f)
                .authCardBackground()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Row(
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = stringResource(R.string.login_screen_title),
                        style = MaterialTheme.typography.labelLarge
                    )
                    Image(painter = painterResource(R.drawable.ic_info), contentDescription = null)
                }

                if(loginState.isCustomerVisible){
                    Text(
                        modifier = Modifier.padding(top = 12.dp),
                        text = stringResource(R.string.login_select_customer),
                        style = MaterialTheme.typography.bodySmall
                    )

                    PrimaryTextField(
                        modifier = Modifier
                            .padding(top = 2.dp)
                            .clickable {
                                onNavigation(LoginScreenNavigation.SelectCustomerScreenNav)
                            },
                        value = loginState.customerName,
                        enabled = false,
                        onValueChange = {},
                        maxLines = 1,
                        hintText = stringResource(R.string.login_select_customer),
                        singleLine = true,
                        trailingIcon = {
                            Icon(
                                painter = painterResource(R.drawable.ic_dropdown),
                                contentDescription = null,
                                tint = Color.Black,
                            )
                        })

                }

                Text(
                    modifier = Modifier.padding(top = 12.dp),
                    text = stringResource(R.string.login_screen_facility_header),
                    style = MaterialTheme.typography.bodySmall
                )

                PrimaryTextField(
                    modifier = Modifier
                        .padding(top = 2.dp)
                        .clickable {
                            if (loginState.customerKey.isEmpty()) {
                                action.invoke(LoginScreenViewModelAction.OnError(customerNotSelectedError))
                            } else {
                                onNavigation(
                                    LoginScreenNavigation.SelectFacilityScreenNav(
                                        loginState.customerKey
                                    )
                                )
                            }
                        },
                    value = loginState.facilityName,
                    enabled = false,
                    onValueChange = {},
                    hintText = stringResource(R.string.login_screen_select_facility_hint),
                    trailingIcon = {
                        Icon(
                            painter = painterResource(R.drawable.ic_dropdown),
                            contentDescription = null,
                            tint = Color.Black
                        )
                    })

                Text(
                    modifier = Modifier.padding(top = 6.dp),
                    text = stringResource(R.string.login_screen_pin_header),
                    style = MaterialTheme.typography.bodySmall
                )
                PrimaryTextField(
                    modifier = Modifier
                        .padding(top = 2.dp),
                    value = loginState.pin,
                    onValueChange = { pin -> action(LoginScreenViewModelAction.OnEnterPin(pin)) },
                    hintText = stringResource(R.string.login_screen_select_pin_hint),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Next,
                        keyboardType = KeyboardType.Password
                    )
                )

                PrimaryButton(
                    text = "Login",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    isEnabled = loginState.pin.isNotEmpty()
                ) {
//                    action(LoginScreenViewModelAction.OnLoginClicked)
                    onNavigation.invoke(LoginScreenNavigation.HomeScreenNav)
                }
            }

        }
        Image(
            modifier = Modifier
                .padding(top = imagePadding)
                .align(Alignment.TopCenter),
            painter = painterResource(R.drawable.ic_app_name),
            contentDescription = null,
            contentScale = ContentScale.FillBounds
        )

    }

}

@PreviewScreenSizes
@Preview
@Composable
fun LoginScreenPreview() {
    MyAppTheme {
        LoginScreen(loginState = LoginState(), action = {}, onNavigation = {})
    }

}