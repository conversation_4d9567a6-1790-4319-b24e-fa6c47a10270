package com.noteactive.app.features.login.domain.repository

import com.noteactive.app.core.domain.repository.BaseRepository
import com.noteactive.app.core.util.network.ApiResponse
import com.noteactive.app.core.util.network.Resource
import com.noteactive.app.features.login.data.model.entity.request.EnterActivationKeyRequest
import com.noteactive.app.core.data.model_entity_with_dao.request.FacilityApiRequest
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponse
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.core.data.model_entity_with_dao.request.CommonApiRequest
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.FacilityApiResponse
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntity
import kotlinx.coroutines.flow.Flow

interface AuthRepository : BaseRepository {
    suspend fun postActivationKey(
        activationRequest: EnterActivationKeyRequest,
    ): Flow<Resource<ActivationKeyResponse>>

    suspend fun getFacilities(
        getFacilityRequest: FacilityApiRequest
    ): Flow<Resource<ApiResponse<List<FacilityApiResponse>>>>

    fun isLoggedIn(): Boolean

    fun getActivationUserData(): Flow<List<ActivationKeyResponseUserInfo?>>

    fun loadAllUsers(request: CommonApiRequest): Flow<Resource<ApiResponse<List<UserEntity>>>>

}
