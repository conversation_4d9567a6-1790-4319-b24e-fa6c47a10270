package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.location

import com.noteactive.app.features.home.createnote.select_location.state.SelectLocation
import com.noteactive.app.features.home.createnote.select_location.state.locationSample

data class SelectLocationState(
    val searchText: String = "",
    val isSelectAllEnable: Boolean = false,
    val count: String = "Select location",
    val listItems: List<SelectLocation> = locationSample,
    val filteredItems: List<SelectLocation> = locationSample,
)


