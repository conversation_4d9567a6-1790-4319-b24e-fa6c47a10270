package com.noteactive.app.features.home.shownotes.notes.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.noteactive.app.features.home.shownotes.notes.data.model.Note
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.data.model.listOfNote
import com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header.NotesScreenHeader
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.util.navigation.NavigationResultManager
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteHeader
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenNavigationAction
import com.noteactive.app.features.home.shownotes.notes.presentation.action.NotesScreenViewModelAction
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.attachments.NoteItemAttachments
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.comment.NoteCommentRoot
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.header.NoteListHeaderRoot
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.inmates.TaggedInmatesRoot
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.location.NoteItemLocation
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.text.NoteItemTitle
import com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.user.UserItemTitle
import com.noteactive.app.features.home.shownotes.notes.presentation.section.notes_screen_header.getHeader

@Composable
fun NotesScreenRoot(
    modifier: Modifier = Modifier,
    navController: NavController,
    navigationAction: (NotesScreenNavigationAction?) -> Unit,
) {
    val viewModel: NotesScreenViewModel = hiltViewModel()
    val notesScreenState by viewModel.notes.collectAsStateWithLifecycle()

    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle


    val timeResult = remember(savedStateHandle) {
        savedStateHandle?.let {
            NavigationResultManager.getResult<String>("time", it)
        }
    }?.collectAsState()

    LaunchedEffect(timeResult?.value) {
        timeResult?.value?.let { time ->
            println("Received note: $time")
            NavigationResultManager.clearResult<String>("time", savedStateHandle!!)
        }
    }

    Box {
        Column(modifier = modifier) {
            NotesScreenHeader(
                notesScreenHeaderState = notesScreenState.notesScreenHeaderState,
                onAction = viewModel::onAction,
                navigationAction = navigationAction::invoke
            )

            NotesList(
                modifier = Modifier,
                notesScreenState.notes,
                viewModelAction = {viewModel.onAction(it)},
                navigationAction = {navigationAction.invoke(it)}
            )
        }
        /*CircularMenuScreen(
            modifier = Modifier,
            onNavigation = {}
        )*/
    }



}

@Composable
private fun NotesList(
    modifier: Modifier = Modifier,
    notes: List<Note>,
    viewModelAction: (NotesScreenViewModelAction) -> Unit,
    navigationAction: (NotesScreenNavigationAction?) -> Unit,

    ) {
    val listState = rememberLazyListState()

    LaunchedEffect(notes.size) {
        if (notes.isNotEmpty()) {
            listState.scrollToItem(notes.lastIndex)
        }
    }
    LazyColumn(modifier = modifier.background(Color.White), state = listState) {
        items(notes) { item ->
            NoteRowHeader(item, viewModelAction, navigationAction)
            HorizontalDivider(color = NotesScreenHeaderDevider)
            NoteRowContent(item.header, item.content)
        }
    }

}



@Composable
fun NoteRowContent(noteHeader: NoteHeader, noteList: List<NoteContent>) {
    noteList.forEachIndexed { index, noteItem ->
        when (noteItem.type) {
            NoteItemType.TITLE -> {
                NoteItemTitle(noteHeader, noteItem)
            }

            NoteItemType.LOCATION -> {
                NoteItemLocation(noteHeader,noteItem)
            }

            NoteItemType.Attachments -> {
                NoteItemAttachments(noteItem)
            }

            NoteItemType.COMMENTS -> {
                NoteCommentRoot(noteHeader, noteItem)

            }
            NoteItemType.TAGGED_INMATE -> {
                TaggedInmatesRoot(noteHeader = noteHeader, noteContent = noteItem)
            }

            NoteItemType.SIGNATURE -> {
                UserItemTitle(noteHeader = noteHeader, noteContent = noteItem)
            }


        }
        HorizontalDivider(color = Color(0XFFCAD1EB), modifier = Modifier.fillMaxHeight())
    }
}


@Composable
fun NoteRowHeader(
    note: Note,
    viewModelAction: (NotesScreenViewModelAction) -> Unit,
    navigationAction: (NotesScreenNavigationAction?) -> Unit,
) {
    NoteListHeaderRoot(
        note,
        viewModelAction = { viewModelAction.invoke(it) },
        navigationAction = { navigationAction.invoke(it) })
}


@PreviewScreenSizes
@Preview
@Composable
fun NotesScreenHeaderPreview() {
    MaterialTheme {
        Column(modifier = Modifier) {
            NotesScreenHeader(notesScreenHeaderState = getHeader(), onAction = {

            }, navigationAction = {

            })
            NotesList(Modifier.weight(1f), listOfNote(), viewModelAction = {}) {

            }
        }
    }
}
