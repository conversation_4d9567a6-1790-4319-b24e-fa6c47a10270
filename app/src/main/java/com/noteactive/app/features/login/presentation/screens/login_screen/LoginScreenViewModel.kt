package com.noteactive.app.features.login.presentation.screens.login_screen

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.core.util.network.Resource
import com.noteactive.app.features.login.domain.repository.AuthRepository
import com.noteactive.app.features.login.domain.repository.CustomerRepository
import com.noteactive.app.features.login.presentation.screens.login_screen.action.LoginScreenViewModelAction
import com.noteactive.app.features.login.presentation.screens.login_screen.state.LoginState
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiSync
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.ApiType
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.api_logs.dao.ApiSyncDao
import com.noteactive.app.core.data.model_entity_with_dao.request.CommonApiRequest
import com.noteactive.app.core.data.repositoryImpl.SyncRepositoryImpl
import com.noteactive.app.core.domain.repository.SyncRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginScreenViewModel @Inject constructor(
    val authRepository: AuthRepository,
    val customerRepository: CustomerRepository,
    val syncRepositoryImpl: SyncRepository,
    val apiSyncDao: ApiSyncDao
) : ViewModel() {
    private val _loginScreenState = MutableStateFlow(LoginState())
    val loginScreenState = _loginScreenState.asStateFlow()
    private val _error = MutableStateFlow("")
    val error = _error.asStateFlow()

    init {

       val colorApi =  ApiSync(
           apiId = 1,
           apiType = ApiType.COLOR.type,
           success = false,
       )

       val users =  ApiSync(
           apiId = 2,
           apiType = ApiType.USERS.type,
           success = false,
       )
        val roles =  ApiSync(
           apiId = 3,
           apiType = ApiType.ROLES.type,
           success = false,
       )
        val location =  ApiSync(
           apiId = 4,
           apiType = ApiType.LOCATION.type,
           success = false,
       )

        val gateWays =  ApiSync(
           apiId = 5,
           apiType = ApiType.GATEWAYS.type,
           success = false,
       )

        val sensors =  ApiSync(
           apiId = 6,
           apiType = ApiType.SENSOR.type,
           success = false,
       )
        viewModelScope.launch {
            apiSyncDao.delete()
            apiSyncDao.insertStatus(colorApi)
            apiSyncDao.insertStatus(users)
            apiSyncDao.insertStatus(roles)
            apiSyncDao.insertStatus(location)
            apiSyncDao.insertStatus(gateWays)
            apiSyncDao.insertStatus(sensors)

        }

    }
    init {
        viewModelScope.launch {
            if (customerRepository.getAllCustomer().size > 1) {
                _loginScreenState.update { it.copy(isCustomerVisible = true) }
            }
            val selectedCustomer = customerRepository.getCurrentCustomerKey()
            val allCustomers = customerRepository.getAllCustomer()

            if (allCustomers.isNotEmpty() && !selectedCustomer.isNullOrEmpty()) {
                if(allCustomers.size == 1){
                    val customers = allCustomers.filter { it.customerKey == selectedCustomer }
                    onAction(LoginScreenViewModelAction.OnCustomerChanged(
                        customerName = customers.first().customerName,
                        customerKey = customers.first().customerKey
                    ))
                }
            }
        }
    }

    private fun setError(error: String) {
        _error.value = error
    }

    fun onAction(action: LoginScreenViewModelAction) {
        when (action) {
            is LoginScreenViewModelAction.OnEnterPin -> {
                _loginScreenState.update { it.copy(pin = action.pin) }
            }

            is LoginScreenViewModelAction.OnFacilityChanged -> {
                _loginScreenState.update { it.copy(facilityName = action.facilityName) }
            }

            LoginScreenViewModelAction.OnLicenceSyncClicked -> {
                onLicenceSyncClicked()
            }

            LoginScreenViewModelAction.OnLoginClicked -> {
                viewModelScope.launch {
                    viewModelScope.launch {

                        val result = syncRepositoryImpl.syncAllFailedApis()

                        Log.d("SyncSummary", "✅ Success: ${result.successCount}, ❌ Failed: ${result.failedCount}")
                        result.results.forEach {
                            Log.d("SyncDetail", "${it.apiType.name}: ${if (it.success) "Success" else "Failed - ${it.errorMessage}"}")
                        }
                    }
                }
                //update Selected Facility And Customer
//                login()
            }

            is LoginScreenViewModelAction.OnCustomerChanged -> {
                _loginScreenState.update { it.copy(customerName = action.customerName) }
                _loginScreenState.update { it.copy(customerKey = action.customerKey) }
            }

            is LoginScreenViewModelAction.OnError -> {
                setError(action.error)
            }
        }
    }

    private fun onLicenceSyncClicked() {

    }

    private fun login() {
        viewModelScope.launch {

            authRepository.loadAllUsers(
                CommonApiRequest(
                    dateAdded = "",
                    deviceId = authRepository.getAdId(),
                    facilitiesId = "106",
                )
            ).collect {
                when (it) {
                    is Resource.Error<*> -> {}
                    is Resource.Loading<*> -> {}
                    is Resource.Success<*> -> {}
                }
            }
        }

    }
}