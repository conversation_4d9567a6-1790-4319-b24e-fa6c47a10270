package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.location

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.IconWithBackgroundContainer
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.createnote.select_location.action.NoteSelectLocationScreenViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.select_all_header.SelectAllHeader
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState

@Composable
fun LocationSection(
    state: FilterSectionState,
    action: (NoteSelectLocationScreenViewModelAction) -> Unit
) {
    LocationItems(
        state = state,
        modifier = Modifier,
        action = action::invoke
    )

}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LocationItems(
    modifier: Modifier = Modifier,
    state: FilterSectionState,
    action: (NoteSelectLocationScreenViewModelAction) -> Unit
) {

    LazyColumn(modifier) {
        stickyHeader {
            SelectAllHeader(
                title = "Select All",
                searchText = state.locationState.searchText,
                hintText = "Search Location",
                showSearchBar = true,
                isSelectAllEnabled = state.locationState.isSelectAllEnable,
                onSelectAllToggle = {
                    action(NoteSelectLocationScreenViewModelAction.OnSelectAllToggle)
                },
                onSearchTextChange = {
                    action(NoteSelectLocationScreenViewModelAction.OnSearchTextChanged(it))
                },
                showSelectAllRow = state.locationState.searchText.isEmpty()

            )
        }

        itemsIndexed(state.locationState.filteredItems) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        action.invoke(
                            NoteSelectLocationScreenViewModelAction.OnSelectToggle(
                                index
                            )
                        )
                    }
                    .padding(horizontal = 16.dp, vertical = 10.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))

                IconWithBackgroundContainer(
                    imageRes = R.drawable.iv_location,
                    tint = item.tint,
                    bgColor = item.background,
                )
                Column(verticalArrangement = Arrangement.spacedBy(2.dp)) {
                    Text(
                        text = item.locationName,
                        style = MaterialTheme.typography.labelLarge.copy(lineHeight = 1.sp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodySmall.copy(
                            lineHeight = 1.sp,
                            color = Color(0xFF999999)
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }


            }
            HorizontalDivider(color = NotesScreenHeaderDevider)
        }
    }
}


@PreviewScreenSizes
@Composable
fun LocationSectionPreview() {
    LocationSection(
        state = FilterSectionState()
    ) {

    }
}