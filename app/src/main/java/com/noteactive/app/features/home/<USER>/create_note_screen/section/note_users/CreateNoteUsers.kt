package com.noteactive.app.features.home.createnote.create_note_screen.section.note_users

import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.create_note_screen.section.CreateNoteItemChip
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CreateNoteUsers(
    modifier: Modifier = Modifier, note: NewNoteRow, onCrossClicked: (String) -> Unit
) {
    FlowRow(
        maxItemsInEachRow = 2, modifier = modifier
    ) {
        note.listOfUsers.forEachIndexed { index, users ->
            CreateNoteItemChip(
                title = users.userName, onRemove = {
                    onCrossClicked.invoke(users.userId)
                }, leadingIcon = R.drawable.inmate_demo_pic, trailingIcon = R.drawable.ic_cross
            )

        }
    }
}