package com.noteactive.app.features.home.createnote.create_note_screen.section.note_popup_options

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R

@Composable
fun CreateNoteOptionPopup(
    modifier: Modifier = Modifier,
    action: (NotePopupOptionsActions) -> Unit
) {
    var isSecondaryToolbar by remember { mutableStateOf(false) }

    val toolbarItems = if (isSecondaryToolbar) {
        secondaryOptionsItems
    } else {
        mainPopupItems
    }

    Card(
        modifier = modifier
            .padding(horizontal = 16.dp)
            .wrapContentSize(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(4.dp),
        shape = RoundedCornerShape(2.dp)
    ) {

        LazyRow(
            modifier = Modifier.padding(4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            itemsIndexed(toolbarItems) { index, item ->

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    Image(
                        painter = painterResource(item.icon),
                        contentDescription = item.type.name,
                        modifier = Modifier
                            .padding(12.dp)
                            .size(24.dp)
                            .clickable {
                                if (item.isToggle) {
                                    isSecondaryToolbar = !isSecondaryToolbar
                                } else {
                                    action.invoke(NotePopupOptionsActions.NotePopupAction(item.type))
                                }
                            }
                    )

                    VerticalDivider(
                        thickness = 1.dp,
                        color = Color(0xFFE1E6EA),
                        modifier = Modifier.height(19.dp)
                    )

                }

            }
        }
    }

}

@PreviewScreenSizes
@Composable
fun CreateNoteOptionPopupPreview() {
    CreateNoteOptionPopup{

    }
}


@Immutable
data class NoteOptionsPopup(
    val icon: Int,
    val type: AddNoteActionEnum,
    val isToggle: Boolean = false
)

enum class AddNoteActionEnum {
    ACTIVE_NOTE,
    OFFENDERS,
    LOCATION,
    BUILDING,
    FORM,
    NEXT,
    PREVIOUS,
    CAMERA,
    MIC,
    EDIT,
    TEXT,
    USER,
    STRIKE,
    ATTACH,
    NONE,
}

val mainPopupItems = listOf(
    NoteOptionsPopup(R.drawable.active_note_icon, AddNoteActionEnum.ACTIVE_NOTE),
    NoteOptionsPopup(R.drawable.ic_bottom_nav_offender, AddNoteActionEnum.OFFENDERS),
    NoteOptionsPopup(R.drawable.ic_location_building, AddNoteActionEnum.LOCATION),
    NoteOptionsPopup(R.drawable.iv_location, AddNoteActionEnum.BUILDING),
    NoteOptionsPopup(R.drawable.ic_note_test3, AddNoteActionEnum.USER),
    NoteOptionsPopup(R.drawable.ic_form, AddNoteActionEnum.FORM),
    NoteOptionsPopup(
        R.drawable.ic_right_arrow,
        AddNoteActionEnum.NEXT,
        isToggle = true
    ) // Last icon switches
)

val secondaryOptionsItems = listOf(
    NoteOptionsPopup(
        R.drawable.left_arrow,
        AddNoteActionEnum.PREVIOUS,
        isToggle = true
    ), // First icon switches back
    NoteOptionsPopup(R.drawable.ic_camera, AddNoteActionEnum.CAMERA),
    NoteOptionsPopup(R.drawable.ic_mic, AddNoteActionEnum.MIC),
    NoteOptionsPopup(R.drawable.ic_highlighter, AddNoteActionEnum.EDIT),
    NoteOptionsPopup(R.drawable.ic_underline, AddNoteActionEnum.TEXT),
    NoteOptionsPopup(R.drawable.ic_strikethrough, AddNoteActionEnum.STRIKE),
    NoteOptionsPopup(R.drawable.ic_attachment, AddNoteActionEnum.ATTACH)
)
