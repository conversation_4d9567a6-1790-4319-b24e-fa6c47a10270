package com.noteactive.app.features.home.inmate.inmate_screen.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenNavigationAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.header.InmateScreenHeader
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenToolbarViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.action.InmateScreenViewModelAction
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.inmate_list.InmateCardRoot
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.section.search_options_row.SearchOptionsRowV2
import com.noteactive.app.features.home.inmate.inmate_screen.presentation.state.InmateScreenState

@Composable
fun InmateScreenRoot(navigationAction: (InmateScreenNavigationAction)->Unit) {
    val viewModel: InmateScreenViewModel = hiltViewModel()
    val inmateScreenState by viewModel.inmateScreenState.collectAsStateWithLifecycle()
    InmateScreen(
        inmateScreenState = inmateScreenState,
        viewModelAction = viewModel::action,
        viewModelToolbarAction = viewModel::actionToolbar,
        navigationAction = navigationAction::invoke
    )

}

@Composable
fun InmateScreen(
    inmateScreenState: InmateScreenState,
    modifier: Modifier = Modifier,
    viewModelAction: (InmateScreenViewModelAction) -> Unit,
    viewModelToolbarAction: (InmateScreenToolbarViewModelAction) -> Unit,
    navigationAction: (InmateScreenNavigationAction)->Unit
) {
    Column(modifier = modifier) {
        InmateScreenHeader(
            inmateScreenState = inmateScreenState,
            onAction = { action ->
                when (action) {

                    else -> {}
                }
            },
            navigationAction = {}
        )
        LazyColumn(
            modifier = Modifier
                .padding(top = 2.dp)
                .fillMaxSize()
                .background(Color.White)
        ) {
            stickyHeader {
                Box(modifier = Modifier.background(Color.White)) {
                    SearchOptionsRowV2(
                        inmateScreenState = inmateScreenState
                    ) {
                        viewModelToolbarAction.invoke(it)
                    }
                }
            }

            itemsIndexed(inmateScreenState.profiles) { index, profile ->
                InmateCardRoot(
                    inmateScreenState.isExpanded,
                    profile = profile,
                    index = index,
                    viewModelAction = {viewModelAction.invoke(it)}
                ) {
                    navigationAction.invoke(InmateScreenNavigationAction.OnInmateDetailsClicked(""))
                }
            }
        }


    }

}

@PreviewScreenSizes
@Preview
@Composable
fun InmateScreenPreview() {
    MyAppTheme {
        InmateScreen(
            InmateScreenState(),
            viewModelAction = {},
            viewModelToolbarAction = {},
            navigationAction = {

            }
        )
    }
}