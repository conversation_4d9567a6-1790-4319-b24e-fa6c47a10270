package com.noteactive.app.features.home.createnote.active_note_form.sections.header

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider

@Composable
fun NotesFormHeader(modifier: Modifier = Modifier.fillMaxWidth(), headerTitle: String) {
    Card(
        shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp),
        colors = CardDefaults.cardColors(containerColor = NotesScreenHeaderDevider),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline),
        modifier = modifier
    ) {

        Text(
            text = headerTitle,
            style = MaterialTheme.typography.labelLarge,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            color = Color.Black,
            fontWeight = FontWeight.W800,
            textAlign = TextAlign.Center
        )
    }
}

@PreviewScreenSizes
@Composable
fun NotesFormHeaderPreview(modifier: Modifier = Modifier) {
    NotesFormHeader(headerTitle = "Fill info")
}