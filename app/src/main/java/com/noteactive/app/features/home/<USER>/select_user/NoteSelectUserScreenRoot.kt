package com.noteactive.app.features.home.createnote.select_user

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.common.presentation.toolbar_search.ToolbarSearchView
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.IconWithBackgroundContainer
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.createnote.select_user.action.NoteSelectUserNavigationAction
import com.noteactive.app.features.home.createnote.select_user.action.NoteSelectUserScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_user.state.NoteSelectUserState

@Composable
fun NoteSelectUserScreenRoot(
    navActions: (NoteSelectUserNavigationAction) -> Unit
) {
    val viewModel: NoteSelectUserScreenViewModel = hiltViewModel()
    val userState by viewModel.noteSelectUserScreenState.collectAsStateWithLifecycle()
    NoteSelectUserScreen(
        userState = userState,
        action = viewModel::action,
        navActions = navActions::invoke
    )
}

@Composable
private fun NoteSelectUserScreen(
    userState: NoteSelectUserState,
    action: (NoteSelectUserScreenViewModelAction) -> Unit,
    navActions: (NoteSelectUserNavigationAction) -> Unit

) {
    Scaffold(
        topBar = {
            ToolbarSearchView(
                title = userState.toolbarTitle,
                hint = userState.hint,
                isSearchEnabled = userState.isSearchEnabled,
                searchText = userState.searchText,
                onSearchTextChanged = {
                    action(
                        NoteSelectUserScreenViewModelAction.OnSearchTextChanged(
                            it
                        )
                    )
                },
                onSearchCrossClicked = { action(NoteSelectUserScreenViewModelAction.OnSearchCrossClicked) },
                onSearchEnabledClicked = { action(NoteSelectUserScreenViewModelAction.OnEnableSearch) },
                onBackIconClicked = {
                    navActions(NoteSelectUserNavigationAction.OnBackPressed)
                },
            )
        },
        bottomBar = {
            MultiButtonBottomRow(
                modifier = Modifier.background(Color.White),
                secondaryButtonTitle = R.string.cancel,
                primaryButtonTitle = R.string.submit,
                secondaryButtonClickListener = {
                    navActions(NoteSelectUserNavigationAction.OnBackPressed)
                },
                primaryButtonClickListener = {
                    val listOfSelectedUsers = userState.users.filter { it.isSelected }
                    navActions(NoteSelectUserNavigationAction.OnSelectUserAction(listOfSelectedUsers))
                })
        }
    ) { innerPadding ->
        UserItems(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(color = Color.White),

            state = userState,
            action = action
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun UserItems(
    modifier: Modifier,
    state: NoteSelectUserState,
    action: (NoteSelectUserScreenViewModelAction) -> Unit
) {

    LazyColumn(modifier = modifier) {
        stickyHeader {
            SelectAllHeader(state, action)
        }
        itemsIndexed(state.filterUsers) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        action.invoke(
                            NoteSelectUserScreenViewModelAction.OnSelectToggle(
                                index
                            )
                        )
                    }
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))

                IconWithBackgroundContainer(
                    imageRes = R.drawable.ic_note_test3,
                    tint = item.tint,
                    bgColor = item.background,
                )
                Column(verticalArrangement = Arrangement.spacedBy(2.dp)) {
                    Text(
                        text = item.userName,
                        style = MaterialTheme.typography.labelLarge.copy(lineHeight = 1.sp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodySmall.copy(
                            lineHeight = 1.sp,
                            color = Color(0xFF999999)
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }


            }
            HorizontalDivider(color = NotesScreenHeaderDevider)
        }
    }
}

@Composable
private fun SelectAllHeader(
    state: NoteSelectUserState,
    action: (NoteSelectUserScreenViewModelAction) -> Unit
) {
    Row(
        modifier = Modifier.padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconHelper(
            imageRes = getCheckBoxIcon(state.isSelectAllToggle),
            modifier = Modifier.clickable {
                action(NoteSelectUserScreenViewModelAction.OnSelectAllToggle)
            })
        Text(text = state.count, style = MaterialTheme.typography.labelLarge)
    }
    HorizontalDivider(color = NotesScreenHeaderDevider)
}


@PreviewScreenSizes
@Composable
private fun NoteSelectUsersScreenRootPreview() {
    NoteSelectUserScreen(userState = NoteSelectUserState(), {}) {}
}