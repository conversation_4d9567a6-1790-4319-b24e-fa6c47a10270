package com.noteactive.app.features.home.inmate.inmate_details_screen.section.timeline

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AssistChip
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.FocusTextSubTitleColor
import com.noteactive.app.core.presentation.designsystem.theme.LightColorPalette
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NoteRowDivider
import com.noteactive.app.features.home.inmate.inmate_details_screen.section.headerToolbar.CommonHeaderBar
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.InmateDetailsState
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.Timeline

@Composable
fun TimelineTabContent(inmateDetailsState: InmateDetailsState) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {  CommonHeaderBar(
            title = "<From ${inmateDetailsState.timeline[0].intakeDate} to ${inmateDetailsState.timeline[0].dischargeDate}>" ,
            showFilter = true,
            showSort = false,
            showSearch = true,
            modifier = Modifier.background(Color.White).padding(horizontal = 16.dp),
            onSearchChange = {},
            searchText = ""
        ) },
        ) { innerPadding ->
        LazyColumn(verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(innerPadding)
                .background(Color.White)
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            items(inmateDetailsState.timeline){ timelineCard->
                TimeLineCard(timelineCard)
                Spacer(modifier = Modifier.height(16.dp))
                TimeLineCard(timelineCard)
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TimeLineCard(timeline: Timeline){
    Column( modifier = Modifier.fillMaxWidth()
            .border(1.dp, color = NoteRowDivider, shape = RoundedCornerShape(4.dp))
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 16.dp , vertical = 6.dp)
        ) {
            Image(painter = painterResource(R.drawable.ic_note_test3),
                contentDescription = null)
            Text(text = timeline.intakeDate, style = MaterialTheme.typography.labelLarge, modifier = Modifier.padding(horizontal = 7.dp))
            Image(painter = painterResource(R.drawable.ic_note_test3),
                contentDescription = null, modifier = Modifier.padding(start = 12.dp))
            Text(text = timeline.dischargeDate, style = MaterialTheme.typography.labelLarge, modifier = Modifier.padding(horizontal = 7.dp))
        }
        HorizontalDivider(color = NoteRowDivider)

        Box(
            modifier = Modifier.padding(16.dp)
        ) {
            Box(modifier = Modifier.matchParentSize().width(2.dp)) {
                VerticalDivider(
                    modifier = Modifier.padding(start = 4.dp, top = 4.dp, bottom = 4.dp).matchParentSize(), color = MaterialTheme.colorScheme.background
                )
            }
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                TimelineEventRow( label = "${timeline.dischargeDate} Discharge", bulletColor = Color(0xFF7E3EF4), sizeOfBullet = 9, textColor = Color(0xFF7E3EF4) )

                Column(modifier = Modifier.fillMaxWidth().padding(start = 2.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    timeline.monthlyActivities.forEach { activity ->
                        TimelineEventRow(
                            label = activity.month,
                            bulletColor = Color(0xFF999999),
                            sizeOfBullet = 5,
                            textColor = Color.Black
                        )
                        FlowRow(horizontalArrangement = Arrangement.spacedBy(6.dp),
                            verticalArrangement = Arrangement.spacedBy(6.dp),
                            modifier = Modifier.padding(start = 16.dp)) {
                            Text(text = "Activity: ", style = MaterialTheme.typography.labelMedium.copy(Color(0xFF999999)))
                            activity.activity.notes?.let { Chip( label = "${activity.activity.notes} Notes", image = activity.activity.chipImage) }
                            activity.activity.forms?.let { Chip( label = "${activity.activity.forms} Forms", image = activity.activity.chipImage ) }
                            activity.activity.activeNotes?.let { Chip( label = activity.activity.activeNotes, image = activity.activity.chipImage ) }
                            activity.activity.tasks?.let { Chip( label = "${activity.activity.tasks} Tasks", image = activity.activity.chipImage ) }
                            }
                    }
                }
                TimelineEventRow( label = "${timeline.intakeDate} Intake", bulletColor = Color(0xFF7E3EF4), sizeOfBullet = 9, textColor = Color(0xFF7E3EF4) )
            }
        }
    } }

@Composable
fun TimelineEventRow(label: String, bulletColor: Color, sizeOfBullet : Int, textColor: Color) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_bullet_inmate_details_screen),
            contentDescription = null,
            tint = bulletColor,
            modifier = Modifier.size(sizeOfBullet.dp)
        )
        Text(
            text = label,
            fontWeight = FontWeight.Bold,
            style = MaterialTheme.typography.labelMedium.copy(textColor),
        )
    } }

@Composable
fun Chip(label:String, image: Int){

    Row(horizontalArrangement = Arrangement.spacedBy(2.dp),
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.border(1.dp, color = MaterialTheme.colorScheme.background , RoundedCornerShape(4.dp))
            .padding(horizontal = 8.dp, vertical = 2.dp)

    ) {
        Icon(
            painter = painterResource(image),
            contentDescription = null,
            tint = FocusTextSubTitleColor,
            modifier = Modifier.size(16.dp)
        )
        Text(text = label, style = MaterialTheme.typography.labelMedium.copy(Color.Black))
    }
}

@PreviewScreenSizes
@Composable
private fun TimelineTabContentPreview() {
    MyAppTheme {
        TimelineTabContent(inmateDetailsState = InmateDetailsState())
    } }