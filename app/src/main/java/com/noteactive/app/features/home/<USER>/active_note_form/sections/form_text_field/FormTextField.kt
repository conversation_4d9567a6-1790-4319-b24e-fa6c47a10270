package com.noteactive.app.features.home.createnote.active_note_form.sections.form_text_field

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField

@Composable
fun FormTextField(
    modifier: Modifier = Modifier.padding(horizontal = 12.dp),
    label: String,
    value: String,
    placeholder: String,
    onValueChange: (String) -> Unit
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            label, style = MaterialTheme.typography.bodyMedium.copy(
                color = Color.Black, fontWeight = FontWeight.W400
            )
        )
        PrimaryTextField(
            value = value, onValueChange = onValueChange, hintText = placeholder
        )
    }
}