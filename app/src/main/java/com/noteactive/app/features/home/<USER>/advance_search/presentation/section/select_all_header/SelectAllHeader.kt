package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.select_all_header

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon

@Composable
fun SelectAllHeader(
    modifier: Modifier = Modifier,
    title: String,
    searchText: String = "",
    hintText: String = "",
    onSearchTextChange: ((String) -> Unit)? = null,
    showSearchBar: Boolean = false,
    showSelectAllRow: Boolean = true,
    isSelectAllEnabled: Boolean = true,
    onSelectAllToggle: () -> Unit,
    titleStyle: TextStyle = MaterialTheme.typography.labelLarge
) {
    Column(modifier = modifier.background(Color.White).padding(start = 16.dp, end = 16.dp, top = 16.dp)) {

        if (showSearchBar && onSearchTextChange != null) {
            SearchTextField(
                text = searchText,
                onTextChange = onSearchTextChange,
                hintText = hintText,
                titleStyle = titleStyle
            )
        }

        if (showSelectAllRow) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp, bottom = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(
                    imageRes = getCheckBoxIcon(isSelectAllEnabled),
                    modifier = Modifier.clickable { onSelectAllToggle() }
                )
                Text(text = title, style = titleStyle)
            }
        }

        HorizontalDivider(color = NotesScreenHeaderDevider)
    }
}

@Composable
private fun SearchTextField(
    modifier: Modifier = Modifier
        .fillMaxWidth(),
    text: String,
    onTextChange: (String) -> Unit,
    hintText: String,
    titleStyle: TextStyle = MaterialTheme.typography.labelLarge
) {
    PrimaryTextField(
        value = text,
        textStyle = titleStyle,
        onValueChange = onTextChange,
        modifier = modifier,
        hintText = hintText
    )
}

@Preview
@Composable
fun SelectAllHeaderPreview() {
    SelectAllHeader(
        title = "Select All",
        searchText = "",
        onSearchTextChange = { },
        showSearchBar = true,
        isSelectAllEnabled = false,
        onSelectAllToggle = { }
    )
}
