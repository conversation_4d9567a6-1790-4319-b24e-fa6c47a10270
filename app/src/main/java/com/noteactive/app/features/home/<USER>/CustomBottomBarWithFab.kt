package com.noteactive.app.features.home.bottom_nav

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemColors
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.noteactive.app.R

@Composable
fun CustomBottomBarWithFab(
    modifier: Modifier = Modifier,
    navController: NavHostController,
    currentRoute: String?,
    onFabClick: () -> Unit = {}
) {
    val items = listOf(
        BottomNavItem.Home,
        BottomNavItem.Search,
        BottomNavItem.Notifications,
        BottomNavItem.Profile
    )

    Box(modifier = modifier) {
        NavigationBar(

            containerColor = Color.White,
            modifier = Modifier
                .background(Color.Transparent)
                .height(64.dp)
                .fillMaxWidth()
                .shadow(4.dp)
                .align(Alignment.BottomCenter),

        ) {
            items.forEachIndexed { index, item ->
                if (index == 2) {
                    Spacer(modifier = Modifier.weight(1f))
                }

                NavigationBarItem(
                    modifier = Modifier,
                    colors = NavigationBarItemColors(
                        selectedIconColor = Color(0XFFF36A21),
                        selectedTextColor = Color(0XFFF36A21),
                        selectedIndicatorColor = Color(0XFFFFFF),
                        unselectedIconColor = MaterialTheme.colorScheme.outlineVariant,
                        unselectedTextColor = MaterialTheme.colorScheme.outlineVariant,
                        disabledIconColor = MaterialTheme.colorScheme.outlineVariant,
                        disabledTextColor = MaterialTheme.colorScheme.outlineVariant
                    ),
                    icon = {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Icon(
                                painter = painterResource(item.icon),
                                contentDescription = item.label,
                            )



                            Text(
                                text = item.label,
                                style = MaterialTheme.typography.bodySmall,
                            )
                            if (currentRoute == item.route) {
                                Spacer(
                                    modifier = Modifier
                                        .padding(top = 4.dp)
                                        .height(2.dp)
                                        .fillMaxWidth()
                                        .background(Color(0XFFF36A21), RoundedCornerShape(1.dp))
                                )
                            }
                        }
                    },

                    selected = currentRoute == item.route,
                    onClick = {
                        if (currentRoute != item.route) {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.startDestinationId) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    }
                )
            }
        }
        FloatingActionButton(
            onClick = { onFabClick.invoke() },
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(y = (-24).dp),
            shape = CircleShape,
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = Color.White,
            elevation = FloatingActionButtonDefaults.elevation(
                defaultElevation = 1.dp,
                pressedElevation = 0.dp,
            )

        ) {
            Image(
                painter = painterResource(R.drawable.ic_bottom_fab_active_note),
                contentDescription = "FAB",
                modifier = Modifier.size(24.dp)
            )
        }

    }
}


@Preview(showBackground = true)
@PreviewScreenSizes
@Composable
fun PreviewCustomBottomBarWithFab() {
    val navController = rememberNavController()
    val currentRoute = BottomNavItem.Home.route
    Row(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Text("Hello", modifier = Modifier.weight(1f))
        CustomBottomBarWithFab(
            navController = navController,
            currentRoute = currentRoute
        )
    }
}