package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.form

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.IconWithBackgroundContainer
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.FilterOptionsViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.select_all_header.SelectAllHeader
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FormSection(state: FilterSectionState, onAction: (FilterOptionsViewModelAction) -> Unit) {
    LazyColumn {
        stickyHeader {
            SelectAllHeader(
                title = "Select All",
                hintText = "Select Form",
                searchText = state.forms.searchFormText,
                showSearchBar = true,
                isSelectAllEnabled = state.forms.isSelectAllEnable,
                onSearchTextChange = {
                    onAction(FilterOptionsViewModelAction.OnSearchTextChanged(it))
                },
                onSelectAllToggle = {
                    onAction(FilterOptionsViewModelAction.OnSelectAllToggle)

                },
                showSelectAllRow = state.forms.searchFormText.isEmpty()
            )
        }

        itemsIndexed(state.forms.filteredItems) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        onAction(FilterOptionsViewModelAction.OnSelectToggle(index))
                    }
                    .padding(horizontal = 16.dp, vertical = 10.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))

                IconWithBackgroundContainer(
                    imageRes = R.drawable.ic_form,
                    tint = item.tint,
                    bgColor = item.background,
                )
                Column(verticalArrangement = Arrangement.spacedBy(2.dp)) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.labelLarge.copy(lineHeight = 1.sp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                }


            }
            HorizontalDivider(color = NotesScreenHeaderDevider)
        }
    }

}

@Preview
@Composable
fun FormSectionPreview() {
    FormSection(
        FilterSectionState()
    ){

    }

}