package com.noteactive.app.features.home.createnote.select_location

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.createnote.select_location.action.NoteSelectLocationScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_location.state.NoteSelectLocationState
import com.noteactive.app.features.home.createnote.select_location.state.SelectLocation
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class NoteSelectLocationScreenViewModel @Inject constructor() : ViewModel() {
    private val _noteSelectLocationScreenState = MutableStateFlow(NoteSelectLocationState())
    val noteSelectLocationScreenState = _noteSelectLocationScreenState.asStateFlow()

    fun action(viewModelAction: NoteSelectLocationScreenViewModelAction) {
        when (viewModelAction) {

            NoteSelectLocationScreenViewModelAction.OnEnableSearch -> {
                _noteSelectLocationScreenState.update { it.copy(isSearchEnabled = true) }
            }

            NoteSelectLocationScreenViewModelAction.OnSearchCrossClicked -> {
                val selectedCount =
                    _noteSelectLocationScreenState.value.locations.count { it.isSelected }
                val countLabel =
                    if (selectedCount > 0) "$selectedCount locations" else "Select location"

                _noteSelectLocationScreenState.update {
                    it.copy(
                        isSearchEnabled = false,
                        searchText = "",
                        filteredLocations = it.locations
                    )
                }
            }

            is NoteSelectLocationScreenViewModelAction.OnSearchTextChanged -> {
                val searchText = viewModelAction.text
                val filteredList = _noteSelectLocationScreenState.value.locations.filter {
                    it.locationName.contains(searchText, ignoreCase = true)
                }
                val selectedCount = filteredList.count { it.isSelected }
                val countLabel =
                    if (selectedCount > 0) "$selectedCount locations" else "Select location"

                _noteSelectLocationScreenState.update {
                    it.copy(
                        searchText = searchText,
                        filteredLocations = filteredList,
                        count = countLabel
                    )
                }
            }

            is NoteSelectLocationScreenViewModelAction.OnSelectToggle -> {
                val currentState = _noteSelectLocationScreenState.value
                val updatedList = currentState.filteredLocations.mapIndexed { index, location ->
                    if (index == viewModelAction.index) {
                        location.copy(isSelected = !location.isSelected)
                    } else location
                }
                updateSelectedLocationState(updatedList)
            }

            NoteSelectLocationScreenViewModelAction.OnSelectAllToggle -> {
                val currentState = _noteSelectLocationScreenState.value
                val toggle = !currentState.isSelectAllToggle
                val updatedList = currentState.filteredLocations.map {
                    it.copy(isSelected = toggle)
                }
                _noteSelectLocationScreenState.update {
                    it.copy(
                        isSelectAllToggle = toggle
                    )
                }
                updateSelectedLocationState(updatedList)
            }
        }
    }

    private fun updateSelectedLocationState(updatedFilteredList: List<SelectLocation>) {

        // Replace matching items in the full list
        val fullList = _noteSelectLocationScreenState.value.locations.map { original ->
            updatedFilteredList.find { it.locationId == original.locationId } ?: original
        }
        val selectedCount = updatedFilteredList.count { it.isSelected }
        val countLabel = if (selectedCount > 0) "$selectedCount locations" else "Select location"

        _noteSelectLocationScreenState.update {
            it.copy(
                locations = fullList,
                filteredLocations = updatedFilteredList,
                count = countLabel
            )
        }
    }

}

