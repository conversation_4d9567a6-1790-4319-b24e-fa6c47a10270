package com.noteactive.app.features.home.inmate.inmate_details_screen.presentation

import androidx.lifecycle.ViewModel
import com.noteactive.app.features.home.inmate.inmate_details_screen.action.InmateDetailsScreenViewModelAction
import com.noteactive.app.features.home.inmate.inmate_details_screen.state.InmateDetailsState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class InmateDetailsScreenViewModel @Inject constructor() : ViewModel() {
    private val _inmateDetailsState = MutableStateFlow(InmateDetailsState())
    val inmateDetailsState = _inmateDetailsState.asStateFlow()

    fun action(action: InmateDetailsScreenViewModelAction){
        when(action){
            is InmateDetailsScreenViewModelAction.OnTabSelected -> {
                _inmateDetailsState.update { it.copy(
                    selectedTab = action.tab
                ) }
            }

            InmateDetailsScreenViewModelAction.OnEditButtonClicked -> {

            }
        }
    }

}