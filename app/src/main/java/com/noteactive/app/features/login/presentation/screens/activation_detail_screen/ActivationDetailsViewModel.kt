package com.noteactive.app.features.login.presentation.screens.activation_detail_screen

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.core.util.network.Resource
import com.noteactive.app.core.data.model_entity_with_dao.request.FacilityApiRequest
import com.noteactive.app.features.login.domain.repository.AuthRepository
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsViewModelAction
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetailsState
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.FacilityApiState
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.getActivationDetailedInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ActivationDetailsViewModel @Inject constructor(
    private val authRepository: AuthRepository,
) : ViewModel() {
    private val _activationDetailState = MutableStateFlow(ActivationDetailsState())
    val activationDetailState = _activationDetailState.asStateFlow()

    private val _facilityApiState = MutableStateFlow<FacilityApiState>(FacilityApiState.Idle)
    val facilityApiState = _facilityApiState.asStateFlow()
    private var facilities = ""

    init {

        viewModelScope.launch {
            authRepository.getActivationUserData().collect { activationDetails ->
                if (activationDetails.isNotEmpty()) {
                    activationDetails.firstOrNull()?.facilities?.let { key ->
                        facilities = key
                    }
                    _activationDetailState.update {
                        it.copy(
                            activationDetails = getActivationDetailedInfo(
                                activationDetails.firstOrNull()
                            )
                        )
                    }
                }

            }

        }

    }

    fun onAction(action: ActivationDetailsViewModelAction) {
        when (action) {
            is ActivationDetailsViewModelAction.OnInfoChanged -> {
            }

            ActivationDetailsViewModelAction.OnSubmitClicked -> {
                fetchFacilities(facilities)
            }
        }
    }

    fun fetchFacilities(facilities: String) {
        viewModelScope.launch {
            val request = FacilityApiRequest(
                dateAdded = "",
                deviceId = authRepository.getAdId(),
                facilities = facilities
            )

            authRepository.getFacilities(request).collect {
                when (it) {
                    is Resource.Loading -> {
                        _facilityApiState.value = FacilityApiState.Loading
                    }

                    is Resource.Success -> {
                        it.data?.let {
                            _facilityApiState.value = FacilityApiState.Success(it)
                        }
                    }

                    is Resource.Error -> {
                        _facilityApiState.value = FacilityApiState.Error("Something went wrong")
                    }
                }
            }
        }
    }
}
