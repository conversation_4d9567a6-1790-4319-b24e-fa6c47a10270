package com.noteactive.app.features.login.presentation.screens.select_facility_screen

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.noteactive.app.features.login.domain.repository.CustomerRepository
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.SelectFacilityState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class SelectedFacilityScreenViewModel @Inject constructor(private val customerRepository: CustomerRepository) : ViewModel() {
    private val _selectFacilityState = MutableStateFlow(
        SelectFacilityState(
            allFacilityList = emptyList(),
            filteredList = emptyList()
        )
    )

    val selectFacilityState = _selectFacilityState.asStateFlow()

    init {

    }

    fun getSelectedFacilities(customerKey: String){
        viewModelScope.launch {
            val facilities = withContext(Dispatchers.IO) {
                customerRepository.getFacilities(customerKey)
            }
            _selectFacilityState.value = _selectFacilityState.value.copy(
                allFacilityList = facilities,
                filteredList = facilities
            )
        }
    }
    fun onAction(action: SelectFacilityViewModelAction) {
        when (action) {
            is SelectFacilityViewModelAction.OnFacilitySelectAction -> {
                val updatedAllFacilities = _selectFacilityState.value.allFacilityList.map {
                    it.copy(isSelected = it == action.facility)
                }

                val updatedFilteredList = updatedAllFacilities.filter {
                    it.facility.contains(
                        _selectFacilityState.value.searchText,
                        ignoreCase = true
                    )
                }
                _selectFacilityState.update {
                    it.copy(
                        allFacilityList = updatedAllFacilities,
                        filteredList = updatedFilteredList
                    )
                }


            }

            is SelectFacilityViewModelAction.OnSearchFacilityTextAction -> {
                filterFacilities(action.searchText)
            }

            SelectFacilityViewModelAction.OnSubmitClickedAction -> {

            }

            is SelectFacilityViewModelAction.OnEnableSearch -> {
                _selectFacilityState.update {
                    it.copy(
                        isSearchEnabled = true
                    )
                }
            }

            SelectFacilityViewModelAction.OnSearchCrossClicked -> {
                _selectFacilityState.update {
                    it.copy(
                        isSearchEnabled = false
                    )
                }
                filterFacilities(filterText = "")
            }
        }
    }

    private fun filterFacilities(filterText: String) {
        val filteredList = _selectFacilityState.value.allFacilityList.filter {
            it.facility.contains(filterText, ignoreCase = true)
        }
        _selectFacilityState.update {
            it.copy(
                searchText = filterText,
                filteredList = filteredList
            )
        }
    }

}