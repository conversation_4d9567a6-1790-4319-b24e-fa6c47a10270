package com.noteactive.app.features.login.presentation.screens.select_facility_screen.state

data class SelectFacilityState(
    val allFacilityList: List<FacilityItemUi>,
    val filteredList: List<FacilityItemUi> = emptyList(),
    val searchText: String = "",
    val isSearchEnabled: Boolean = false
)

//data class Facility(val facilityName: String, var isSelected: Boolean)
//
//fun getFacility() = listOf(
//    Facility("Facility 1", false),
//    Facility("Facility 2", false),
//    Facility("Facility 3", false),
//    Facility("Facility 4", false),
//    Facility("Facility 5", false),
//    Facility("Facility 6", false),
//    Facility("Facility 7", false),
//    Facility("Facility 8", false),
//    Facility("Facility 9", false),
//    Facility("Facility 10", false),
//    Facility("Facility 11", false),
//    Facility("Facility 12", false),
//)