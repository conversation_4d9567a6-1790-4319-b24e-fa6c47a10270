package com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState

interface FilterHandler {
    fun toggleSelectAll(state: FilterSectionState): FilterSectionState
    fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState
    fun search(state: FilterSectionState, searchText: String): FilterSectionState
}