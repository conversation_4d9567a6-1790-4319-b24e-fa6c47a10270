package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.form.handler

import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.handler.FilterHandler
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState
import javax.inject.Inject

class Form<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> @Inject constructor() : FilterHandler {
    override fun toggleSelectAll(state: FilterSectionState): FilterSectionState {
        val forms = state.forms
        val enabled = !state.forms.isSelectAllEnable

        val updatedList = forms.listItems.map {
            it.copy(isSelected = enabled)
        }
        return state.copy(
            forms = forms.copy(
                isSelectAllEnable = enabled,
                filteredItems = updatedList,
                listItems = updatedList
            )
        )
    }


    override fun toggleItem(state: FilterSectionState, index: Int): FilterSectionState {
        val form = state.forms
        val formItems = form.filteredItems.toMutableList()
        val currentItem = form.filteredItems[index]
        val updatedItem = currentItem.copy(isSelected = !currentItem.isSelected) // fix is here
        formItems[index] = updatedItem
        val updatedList = state.forms.listItems.map {
            if (it.id == updatedItem.id) it.copy(isSelected = updatedItem.isSelected) else it
        }
        val allSelected = updatedList.all { it.isSelected }
        return state.copy(
            forms = form.copy(
                listItems = updatedList,
                filteredItems = formItems,
                isSelectAllEnable = allSelected
            )
        )
    }



    override fun search(state: FilterSectionState, searchText: String): FilterSectionState {
        val forms = state.forms
        val selectedIds =
            (forms.listItems + forms.filteredItems).filter { it.isSelected }.map { it.id }.toSet()
        val filtered = forms.listItems.filter {
            it.title.contains(searchText, ignoreCase = true)
        }.map {
            it.copy(isSelected = selectedIds.contains(it.id))
        }
        return state.copy(
            forms = forms.copy(
                searchFormText = searchText,
                filteredItems = filtered
            )
        )
    }
}
