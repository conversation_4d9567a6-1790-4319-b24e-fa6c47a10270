package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.date_time

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.core.presentation.designsystem.composable.textfield.PrimaryTextField
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme

@Composable
fun DateAndTimeSection(modifier: Modifier = Modifier) {
    val viewModel: DateAndTimeViewModel = hiltViewModel()
    val dateAndTime by viewModel.dateAndTime.collectAsStateWithLifecycle()
    Column(
        modifier = modifier.fillMaxSize().background(Color.White).padding(horizontal = 16.dp, vertical = 8.dp),
    ) {
        Text(text = "From", style = MaterialTheme.typography.bodyMedium)
        PrimaryTextField(
            value = dateAndTime.startDate + " , " + dateAndTime.startTime,
            onValueChange = {},
        )

        Text(
            text = "To",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(top = 10.dp)
        )
        PrimaryTextField(
            value = dateAndTime.startDate + " , " + dateAndTime.startTime,
            onValueChange = {},
        )
    }

}

@PreviewScreenSizes
@Composable
fun DateAndTimeSectionPreview() {
    MyAppTheme {
        DateAndTimeSection()
    }
}