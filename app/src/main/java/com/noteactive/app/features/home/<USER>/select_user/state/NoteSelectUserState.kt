package com.noteactive.app.features.home.createnote.select_user.state

import androidx.compose.ui.graphics.Color
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions

data class NoteSelectUserState(
    val toolbarTitle: String = "Select User",
    val hint: String = "Search User",
    val isSearchEnabled: Boolean = false,
    var isSelectAllToggle: Boolean = false,
    val searchText: String = "",
    val count: String = "Select User",
    val users: List<SelectUser> = userSample,
    val filterUsers: List<SelectUser> = userSample,
)

data class SelectUser(
    val userName: String,
    val userId: String,
    var isSelected: Boolean,
    val tint: Color,
    val background: Color,
    val description: String = "4F Demo and Training",
    val type : NewNoteRowTypeOptions =NewNoteRowTypeOptions.USERS

)

val userSample = listOf(
    SelectUser(
        "<PERSON> J<PERSON>min",
        "1", false,
        tint = Color(0xFF13B4EA),
        background = Color(0xFFE7F9FF)
    ),
    SelectUser(
        "<PERSON> Jasmin", "2", false,
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1)
    ),
    SelectUser(
        "Akarsh ", "3", false,
        tint = Color(0xFFFF6900),
        background = Color(0xFFFFF3EB)
    ),
    SelectUser(
        "Nishant", "4", false,
        tint = Color(0xFF1ECA86),
        background = Color(0xFFDCFFF1)
    ),
    SelectUser(
        "Ally John Doe", "5", false,
        tint = Color(0xFF7AB51D),
        background = Color(0xFFEFFFD6)
    ),
    SelectUser(
        "Ahmed Jasmin", "6", false,
        tint = Color(0xFFD03798),
        background = Color(0xFFFFECF8)
    ),
    SelectUser(
        "Justin", "7", false,
        tint = Color(0xFF7E3EF4),
        background = Color(0xFFF3F0FF)
    ),
    SelectUser(
        "Demo User Selected", "8", false,
        tint = Color(0xFFCC1400),
        background = Color(0xFFFFECEB)
    ),
    SelectUser(
        "Ahmed Jasmin", "6", false,
        tint = Color(0xFFD03798),
        background = Color(0xFFFFECF8)
    ),
    SelectUser(
        "Justin", "19", false,
        tint = Color(0xFF7E3EF4),
        background = Color(0xFFF3F0FF)
    ),
    SelectUser(
        "Demo User Selected", "11", false,
        tint = Color(0xFFCC1400),
        background = Color(0xFFFFECEB)
    )
)
