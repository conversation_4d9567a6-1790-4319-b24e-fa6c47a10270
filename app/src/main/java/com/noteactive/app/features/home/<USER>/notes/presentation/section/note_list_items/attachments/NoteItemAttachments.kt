package com.noteactive.app.features.home.shownotes.notes.presentation.section.note_list_items.attachments

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.theme.NotesListVerticalDivider
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteContent
import com.noteactive.app.features.home.shownotes.notes.data.model.NoteItemType
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteChip
import com.noteactive.app.features.home.shownotes.notes.presentation.section.NoteItemIcon

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun NoteItemAttachments(noteContent: NoteContent, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .heightIn(min = 36.dp)
            .height(IntrinsicSize.Max)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,

        ) {
        NoteItemIcon(imageId = R.drawable.ic_note_test5)
        VerticalDivider(color = NotesListVerticalDivider, modifier = Modifier.fillMaxHeight())
        FlowRow(
            horizontalArrangement = Arrangement.spacedBy(2.dp),
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = 6.dp)
        ) {
            noteContent.files.forEach { chipText ->
                NoteChip(modifier = Modifier.padding(vertical = 4.dp), text = chipText)

            }
        }

    }
}

@PreviewScreenSizes
@Preview
@Composable
fun NoteItemAttachmentsPreview() {
    NoteItemAttachments(
        noteContent =
            NoteContent(
                files = listOf("File1.jpg", "File2.jpg", "File3 demo.jpg"),
                type = NoteItemType.Attachments
            )
    )
}