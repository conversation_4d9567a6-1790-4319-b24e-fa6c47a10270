package com.noteactive.app.features.common.presentation.date_picker

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.common.data.repositoryimpl.DateTimeRangePickerRepositoryImpl
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.features.common.presentation.date_picker.actions.DateRangeSelectionNavigationAction

@Composable
fun DatePickerScreen(
    isPickRange: Boolean = true,
    onAction: (DateRangeSelectionNavigationAction) -> Unit
) {
    val viewModel: DateRangePickerViewModel = hiltViewModel()
    DatePicker(isPickRange, viewModel,onAction::invoke)
}

@Composable
private fun DatePicker(
    isPickRange: Boolean = false,
    viewModel: DateRangePickerViewModel,
    onAction: (DateRangeSelectionNavigationAction) -> Unit
) {
    val customColors = DatePickerDefaults.colors(
        containerColor = Color.White,
        weekdayContentColor = Color.Black,
        selectedDayContainerColor = MaterialTheme.colorScheme.primary,
        selectedDayContentColor = Color.White,
        disabledSelectedDayContainerColor = MaterialTheme.colorScheme.onSurface,
        todayContentColor = Color.Black,
        dayContentColor = Color.Gray
    )

    val datePickerState = rememberDateRangePickerState(
        initialDisplayedMonthMillis = System.currentTimeMillis(),
        yearRange = 2023..2026,
        selectableDates = object : SelectableDates {
            override fun isSelectableDate(utcTimeMillis: Long) =
                viewModel.isSelectable(utcTimeMillis)
        })

    LaunchedEffect(datePickerState.selectedStartDateMillis, datePickerState.selectedEndDateMillis) {
        val start = datePickerState.selectedStartDateMillis
        val end = datePickerState.selectedEndDateMillis
        if (start != null && (isPickRange && end != null || !isPickRange)) {
            onAction(
                DateRangeSelectionNavigationAction.OnDateSelectedComplete(
                    start = start,
                    end = if (isPickRange) end else null
                )
            )
        }
    }

    Box {
        DateRangePicker(
            state = datePickerState,
            modifier = Modifier,
            colors = customColors,
            showModeToggle = false
        )
        AppCommonToolbar(
            title = stringResource(R.string.select_date_title),
            modifier = Modifier.shadow(2.dp),
            onBackIconClicked = {
                onAction.invoke(DateRangeSelectionNavigationAction.OnDismiss)
            },
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DateRangePickerScreenPreview() {
    val mockViewModel = object : DateRangePickerViewModel(DateTimeRangePickerRepositoryImpl()) {
        override fun isSelectable(millis: Long): Boolean = true
    }
    MyAppTheme {
        DatePicker(
            isPickRange = false,
            mockViewModel,
            onAction = {}
        )
    }
}
