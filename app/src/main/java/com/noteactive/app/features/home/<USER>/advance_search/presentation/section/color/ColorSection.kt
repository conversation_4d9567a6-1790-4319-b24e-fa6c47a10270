package com.noteactive.app.features.home.shownotes.advance_search.presentation.section.color

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.noteactive.app.core.presentation.designsystem.theme.NoteRowDivider
import com.noteactive.app.core.presentation.designsystem.theme.NotesScreenHeaderDevider
import com.noteactive.app.core.presentation.designsystem.theme.SelectedBackgroundColor
import com.noteactive.app.features.home.createnote.common_composable.IconHelper
import com.noteactive.app.features.home.createnote.common_composable.getCheckBoxIcon
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.FilterOptionsViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.select_all_header.SelectAllHeader
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ColorSection(
    state: FilterSectionState,
    action: (FilterOptionsViewModelAction) -> Unit
) {
    LazyColumn(modifier = Modifier.fillMaxSize()) {
        stickyHeader {
            SelectAllHeader(
                title = "Select All",
                isSelectAllEnabled = state.listOfColor.isSelectAllEnable,
                onSelectAllToggle = {
                    action(FilterOptionsViewModelAction.OnSelectAllToggle)
                },
                onSearchTextChange = {
                    action(FilterOptionsViewModelAction.OnSearchTextChanged(it))

                },
                showSelectAllRow = state.listOfColor.searchText.isEmpty()
            )
        }
        itemsIndexed(state.listOfColor.filteredItems) { index, item ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .background(
                        if (item.isSelected) SelectedBackgroundColor else Color.White
                    )
                    .clickable {
                        action(FilterOptionsViewModelAction.OnSelectToggle(index))

                    }
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconHelper(imageRes = getCheckBoxIcon(item.isSelected))
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .border(2.dp, NoteRowDivider, CircleShape)
                        .background(item.color)
                )

                Text(
                    text = item.title,
                    style = MaterialTheme.typography.labelLarge.copy(lineHeight = 1.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

            }
            HorizontalDivider(color = NotesScreenHeaderDevider)


        }

    }
}

@PreviewScreenSizes
@Composable
fun ColorSectionPreview() {
    ColorSection(FilterSectionState()) {

    }
}