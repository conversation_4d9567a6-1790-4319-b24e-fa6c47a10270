package com.noteactive.app.features.home.createnote.create_note_screen

import androidx.lifecycle.ViewModel
import com.noteactive.app.R
import com.noteactive.app.features.home.createnote.create_note_screen.action.CreateNoteViewModelAction
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRow
import com.noteactive.app.features.home.createnote.create_note_screen.state.NewNoteRowTypeOptions
import com.noteactive.app.features.home.createnote.create_note_screen.state.getNewNoteItem
import com.noteactive.app.features.home.createnote.select_facility.state.SelectFacility
import com.noteactive.app.features.home.createnote.select_location.state.SelectLocation
import com.noteactive.app.features.home.createnote.select_offenders_screen.state.Offender
import com.noteactive.app.features.home.createnote.select_user.state.SelectUser
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class CreateNewScreenViewModel @Inject constructor() : ViewModel() {


    private val _noteDetails: MutableStateFlow<List<NewNoteRow>> = MutableStateFlow(
        getNewNoteItem()
    )

    val noteDetails = _noteDetails.asStateFlow()


    fun onAction(viewModelAction: CreateNoteViewModelAction) {
        when (viewModelAction) {
            is CreateNoteViewModelAction.OnTextChange -> {
                addOrUpdateText(viewModelAction.text)
            }

            is CreateNoteViewModelAction.OnActiveNoteRemoved -> {
                _noteDetails.update { noteDetails ->
                    noteDetails.toMutableList().apply {
                        removeAt(viewModelAction.index)
                    }
                }

            }

            is CreateNoteViewModelAction.OnNoteItemOptionRemoved -> {
                _noteDetails.update { list ->
                    list.toMutableList().apply {
                        val row = getOrNull(viewModelAction.index)
                        row?.let {
                            val updatedRow = when (viewModelAction.rowTypeOptions) {
                                NewNoteRowTypeOptions.USERS -> {
                                    val listOfUsers =
                                        row.listOfUsers.filterNot { it.userId == viewModelAction.id }
                                            .toMutableList()
                                    if (listOfUsers.isEmpty()) {
                                        removeAt(viewModelAction.index)
                                        return@apply
                                    } else {
                                        row.copy(
                                            listOfUsers = listOfUsers
                                        )
                                    }
                                }

                                NewNoteRowTypeOptions.LOCATION -> {

                                    val listOfLocation =
                                        row.locations.filterNot { it.locationId == viewModelAction.id }
                                            .toMutableList()

                                    if (listOfLocation.isEmpty()) {
                                        removeAt(viewModelAction.index)
                                        return@apply
                                    } else {
                                        row.copy(
                                            locations = listOfLocation
                                        )
                                    }


                                }

                                NewNoteRowTypeOptions.INMATE -> {

                                    val listOfInmates =
                                        row.listOfInmates.filterNot { it.id == viewModelAction.id }
                                            .toMutableList()

                                    if (listOfInmates.isEmpty()) {
                                        removeAt(viewModelAction.index)
                                        return@apply
                                    } else {
                                        row.copy(
                                            listOfInmates = listOfInmates
                                        )
                                    }


                                }

                                NewNoteRowTypeOptions.FACILITIES -> {

                                    val listOfFacilities =
                                        row.facilities.filterNot { it.facilityId == viewModelAction.id }
                                            .toMutableList()

                                    if (listOfFacilities.isEmpty()) {
                                        removeAt(viewModelAction.index)
                                        return@apply
                                    } else {
                                        row.copy(
                                            facilities = listOfFacilities
                                        )
                                    }

                                }

                                NewNoteRowTypeOptions.ACTIVE_NOTE -> {

                                    when (viewModelAction.typeTobeRemoved) {
                                        NewNoteRowTypeOptions.LOCATION -> {
                                            row.copy(
                                                locations = row.locations.filterNot { it.locationId == viewModelAction.id }
                                                    .toMutableList()
                                            )
                                        }

                                        NewNoteRowTypeOptions.BUILDING -> {
                                            row.copy(
                                                facilities = row.facilities.filterNot { it.facilityId == viewModelAction.id }
                                                    .toMutableList()
                                            )
                                        }


                                        NewNoteRowTypeOptions.USERS -> {
                                            row.copy(
                                                listOfUsers = row.listOfUsers.filterNot { it.userId == viewModelAction.id }
                                                    .toMutableList()
                                            )
                                        }

                                        NewNoteRowTypeOptions.FACILITIES -> {
                                            row.copy(
                                                facilities = row.facilities.filterNot { it.facilityId == viewModelAction.id }
                                                    .toMutableList()
                                            )
                                        }

                                        NewNoteRowTypeOptions.INMATE -> {
                                            row.copy(
                                                listOfInmates = row.listOfInmates.filterNot { it.id == viewModelAction.id }
                                                    .toMutableList()
                                            )
                                        }

                                        else -> {
                                            row
                                        }
                                    }
                                }

                                else -> row
                            }

                            set(viewModelAction.index, updatedRow)
                        }
                    }
                }
            }

        }
    }

    fun addSelectedUsers(users: MutableList<SelectUser>) {

        addOrUpdateRow(
            type = NewNoteRowTypeOptions.USERS,
            updateRow = { it.listOfUsers.addAll(users) },
            createNewRow = {
                NewNoteRow(
                    typeOptions = NewNoteRowTypeOptions.USERS,
                    listOfUsers = users,
                    icon = R.drawable.ic_note_test3
                )
            }
        )

    }

    fun addSelectedLocation(locations: MutableList<SelectLocation>) {
        addOrUpdateRow(
            type = NewNoteRowTypeOptions.LOCATION,
            updateRow = { it.locations.addAll(locations) },
            createNewRow = {
                NewNoteRow(
                    typeOptions = NewNoteRowTypeOptions.LOCATION,
                    locations = locations,
                    icon = R.drawable.iv_location
                )
            }
        )

    }

    fun addOrUpdateText(text: String) {
        val currentList = noteDetails.value.toMutableList()

        val existing = noteDetails.value
            .mapIndexedNotNull { index, item ->
                if (item.typeOptions == NewNoteRowTypeOptions.TEXT) index to item else null
            }
            .firstOrNull()

        if (existing != null) {
            val (index, item) = existing
            currentList[index] = item.copy(text = text) // Create new object with updated text
        } else {
            currentList.add(
                NewNoteRow(
                    typeOptions = NewNoteRowTypeOptions.TEXT,
                    text = text,
                )
            )
        }

        _noteDetails.update { currentList }

    }

    fun addSelectedFacilities(facilities: MutableList<SelectFacility>) {

        addOrUpdateRow(
            type = NewNoteRowTypeOptions.FACILITIES,
            updateRow = { it.facilities.addAll(facilities) },
            createNewRow = {
                NewNoteRow(
                    typeOptions = NewNoteRowTypeOptions.FACILITIES,
                    facilities = facilities,
                    icon = R.drawable.ic_location_building

                )
            }
        )

    }

    private fun addOrUpdateRow(
        type: NewNoteRowTypeOptions,
        updateRow: (NewNoteRow) -> Unit,
        createNewRow: () -> NewNoteRow
    ) {
        val currentList = noteDetails.value.toMutableList()

        val existingItemWithIndex = noteDetails.value
            .mapIndexedNotNull { index, item ->
                if (item.typeOptions == type) index to item else null
            }
            .firstOrNull()

        if (existingItemWithIndex == null) {
            currentList.add(0, createNewRow())
        } else {
            val (index, item) = existingItemWithIndex
            updateRow(item)
            currentList[index] = item
        }

        _noteDetails.update { currentList }
    }

    fun addOffenders(offenders: List<Offender>) {
        addOrUpdateRow(
            type = NewNoteRowTypeOptions.INMATE,
            updateRow = { it.listOfInmates.addAll(offenders) },
            createNewRow = {
                NewNoteRow(
                    typeOptions = NewNoteRowTypeOptions.INMATE,
                    listOfInmates = offenders.toMutableList(),
                    icon = R.drawable.ic_bottom_nav_offender
                )
            }
        )
    }

}

