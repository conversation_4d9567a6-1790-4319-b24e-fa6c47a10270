package com.noteactive.app.features.home.shownotes.advance_search.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.multi_buttons_bottom_row.MultiButtonBottomRow
import com.noteactive.app.core.presentation.designsystem.composable.toolbar.AppCommonToolbar
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.home.createnote.select_location.action.NoteSelectLocationScreenViewModelAction
import com.noteactive.app.features.home.createnote.select_user.action.NoteSelectUserScreenViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.AdvanceSearchViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.action.FilterOptionsViewModelAction
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.active_note.ActiveNoteSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.classification.ClassificationSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.color.ColorSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.date_time.DateAndTimeSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.form.FormSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.keyword.KeywordSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.location.LocationSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.offenders.OffenderSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.sidebar.SidebarMenu
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.task_type.TaskTypeSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.section.user.UserSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSection
import com.noteactive.app.features.home.shownotes.advance_search.presentation.state.FilterSectionState

@Composable
fun SearchNotesScreenRoot() {
    val viewModel: AdvanceSearchScreenViewModel = hiltViewModel()
    val filterItemsState by viewModel.filterItems.collectAsStateWithLifecycle()
    Scaffold(topBar = {
        AppCommonToolbar(
            title = "Advance search",
            backIcon = R.drawable.ic_back,
            onBackIconClicked = {},
        )
    }, bottomBar = {
        FooterButtonItem(onPrimaryButtonClick = {}, onSecondaryButtonClick = {})
    }) { padding ->
        Row(
            modifier = Modifier
                .padding(padding)
                .fillMaxSize()
        ) {
            SidebarMenu(
                selectedFilter = filterItemsState.selectedSection, onFilterClick = {
                    viewModel.onAction(AdvanceSearchViewModelAction.OnSelectFilter(it))
                }, filters = filterItemsState.filters
            )
            FilterContentArea(
                selectedFilter = filterItemsState,
                action = viewModel::onAction
            )
        }
    }

}

@Composable
fun FilterContentArea(
    selectedFilter: FilterSectionState,
    action: (AdvanceSearchViewModelAction) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(start = 16.dp)
    ) {
        val section = selectedFilter.selectedSection
        when (section) {

            FilterSection.DATE_TIME -> {
                DateAndTimeSection()
            }

            FilterSection.KEYWORDS -> {
                KeywordSection(selectedFilter.keyword) {
                    action(AdvanceSearchViewModelAction.OnKeywordTextChange(it))
                }
            }

            FilterSection.USER -> {
                UserSection(selectedFilter) { userAction ->
                    handleUserSectionAction(userAction, section, action)
                }
            }

            FilterSection.FORM,
            FilterSection.HIGHLIGHTER,
            FilterSection.ACTIVE_NOTES,
            FilterSection.OFFENDER_STATUS,
            FilterSection.CLASSIFICATION,
            FilterSection.TASK_TYPE -> {
                GenericFilterSection(
                    section = section,
                    selectedFilter = selectedFilter,
                    onAction = action
                )
            }

            FilterSection.LOCATION -> {
                LocationSection(selectedFilter) { locationAction ->
                    handleLocationAction(locationAction, section, action)
                }
            }

            FilterSection.OFFENDER,
            FilterSection.TIME,
            FilterSection.OTHER -> {
                // TODO: Implement if needed
            }
        }
    }
}
@Composable
fun GenericFilterSection(
    section: FilterSection,
    selectedFilter: FilterSectionState,
    onAction: (AdvanceSearchViewModelAction) -> Unit
) {
    val composable: @Composable ((FilterOptionsViewModelAction) -> Unit) -> Unit = when (section) {
        FilterSection.FORM -> { { FormSection(selectedFilter, it) } }
        FilterSection.HIGHLIGHTER -> { { ColorSection(selectedFilter, it) } }
        FilterSection.ACTIVE_NOTES -> { { ActiveNoteSection(selectedFilter, it) } }
        FilterSection.OFFENDER_STATUS -> { { OffenderSection(selectedFilter, it) } }
        FilterSection.CLASSIFICATION -> { { ClassificationSection(selectedFilter, it) } }
        FilterSection.TASK_TYPE -> { { TaskTypeSection(selectedFilter, it) } }
        else -> return
    }

    composable { filterAction ->
        when (filterAction) {
            is FilterOptionsViewModelAction.OnSearchTextChanged -> {
                onAction(AdvanceSearchViewModelAction.OnSearchText(section, filterAction.text))
            }

            is FilterOptionsViewModelAction.OnSelectAllToggle -> {
                onAction(AdvanceSearchViewModelAction.OnSelectAllToggle(section))
            }

            is FilterOptionsViewModelAction.OnSelectToggle -> {
                onAction(AdvanceSearchViewModelAction.OnSelectToggle(section, filterAction.index))
            }
        }
    }
}

fun handleUserSectionAction(
    action: NoteSelectUserScreenViewModelAction,
    section: FilterSection,
    dispatch: (AdvanceSearchViewModelAction) -> Unit
) {
    when (action) {
        is NoteSelectUserScreenViewModelAction.OnSearchTextChanged ->
            dispatch(AdvanceSearchViewModelAction.OnSearchText(section, action.text))

        is NoteSelectUserScreenViewModelAction.OnSelectAllToggle ->
            dispatch(AdvanceSearchViewModelAction.OnSelectAllToggle(section))

        is NoteSelectUserScreenViewModelAction.OnSelectToggle ->
            dispatch(AdvanceSearchViewModelAction.OnSelectToggle(section, action.index))

        else -> {}
    }
}

fun handleLocationAction(
    action: NoteSelectLocationScreenViewModelAction,
    section: FilterSection,
    dispatch: (AdvanceSearchViewModelAction) -> Unit
) {
    when (action) {
        is NoteSelectLocationScreenViewModelAction.OnSearchTextChanged ->
            dispatch(AdvanceSearchViewModelAction.OnSearchText(section, action.text))

        is NoteSelectLocationScreenViewModelAction.OnSelectToggle ->
            dispatch(AdvanceSearchViewModelAction.OnSelectToggle(section, action.index))

        NoteSelectLocationScreenViewModelAction.OnSelectAllToggle ->
            dispatch(AdvanceSearchViewModelAction.OnSelectAllToggle(section))

        else -> {}
    }
}


@Composable
fun FooterButtonItem(onPrimaryButtonClick: () -> Unit, onSecondaryButtonClick: () -> Unit) {
    MultiButtonBottomRow(
        modifier = Modifier.background(Color.White),
        secondaryButtonTitle = R.string.close,
        primaryButtonTitle = R.string.verify,
        secondaryButtonClickListener = {
            onSecondaryButtonClick.invoke()

        },
        primaryButtonClickListener = {
            onPrimaryButtonClick.invoke()

        })

}

@PreviewScreenSizes
@Composable
private fun AdvanceSearchScreenPreview() {
    MyAppTheme {
        SearchNotesScreenRoot()
    }
}