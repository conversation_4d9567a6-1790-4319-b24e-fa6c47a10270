package com.noteactive.app.features.home.inmate.inmate_details_screen.section.headerToolbar

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import com.noteactive.app.R
import com.noteactive.app.core.presentation.designsystem.composable.textfield.TransparentHintTextField

@Composable
fun CommonHeaderBar(
    modifier: Modifier = Modifier,
    title: String,
    showFilter: Boolean = true,
    showSort: Boolean = false,
    showSearch: Boolean = true,
    onSearchChange: (String) -> Unit = {},
    searchText: String = "",
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(vertical = 6.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.labelLarge,
            modifier = Modifier
                .padding(top = 4.dp, bottom = 4.dp, end = 4.dp)
        )

        Spacer(modifier = Modifier.weight(1f))

        TransparentHintTextField(
            text = searchText,
            onValueChange = onSearchChange,
            trailingIcon = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    if (showFilter) {
                        Icon(
                            painter = painterResource(R.drawable.ic_notes_screen_filter),
                            contentDescription = null
                        )
                    }
                    if (showFilter && (showSort || showSearch)) {
                        VerticalDivider(thickness = 1.dp, modifier = Modifier.height(16.dp))
                    }
                    if (showSort) {
                        Icon(
                            painter = painterResource(R.drawable.ic_note_screen_sort),
                            contentDescription = null
                        )
                    }
                    if (showSort && showSearch) {
                        VerticalDivider(thickness = 1.dp, modifier = Modifier.height(16.dp))
                    }
                    if (showSearch) {
                        Icon(
                            painter = painterResource(R.drawable.ic_search),
                            contentDescription = null
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        )
    }
}
