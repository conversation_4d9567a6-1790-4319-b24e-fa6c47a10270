package com.noteactive.app.features.login.data.network.api

import com.noteactive.app.core.util.network.ApiEndpoints
import com.noteactive.app.core.util.network.ApiResponse
import com.noteactive.app.core.data.model_entity_with_dao.request.CommonApiRequest
import com.noteactive.app.core.data.model_entity_with_dao.request.FacilityApiRequest
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.colors.ColorEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.FacilityApiResponse
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.gate_ways.GatewaysEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.highlighter.HighlighterEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.location.LocationEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.role.UserRoleEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.sensor.SensorEntity
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.updated_information.UpdatedInformation
import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.users.UserEntity
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

interface CommonApiService {
    @POST(ApiEndpoints.V1.FACILITIES)
    suspend fun facilities(
        @Body getFacilityRequest: FacilityApiRequest
    ): Response<ApiResponse<List<FacilityApiResponse>>>

    @POST(ApiEndpoints.V1.USERS)
    suspend fun users(
        @Body userRequest: CommonApiRequest
    ): Response<ApiResponse<List<UserEntity>>>


    @POST(ApiEndpoints.V1.LOCATIONS)
    suspend fun location(
        @Body locationApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<LocationEntity>>>

    @POST(ApiEndpoints.V1.HIGHLIGHTER)
    suspend fun highlighter(
        @Body highlighterApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<HighlighterEntity>>>

    @POST(ApiEndpoints.V1.ROLES)
    suspend fun userRole(
        @Body roleApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<UserRoleEntity>>>

    @POST(ApiEndpoints.V1.COLORS)
    suspend fun colors(
        @Body colorApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<ColorEntity>>>

    @POST(ApiEndpoints.V1.GATE_WAYS)
    suspend fun gateWays(
        @Body highlighterApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<GatewaysEntity>>>

    @POST(ApiEndpoints.V1.SENSOR)
    suspend fun sensor(
        @Body highlighterApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<SensorEntity>>>

    @POST(ApiEndpoints.V1.UPDATED_INFO)
    suspend fun updatedInfo(
        @Body highlighterApiRequest: CommonApiRequest
    ): Response<ApiResponse<List<UpdatedInformation>>>


//first time load > //different query


//    const val KEYWORDS = "${BASE_API}getkeywords" //data present in string
//    const val COMMON = "${BASE_API}getcommon" // data present in string
//    const val COMMON_TASK = "${BASE_API}gettaskcommon"  not all object available
//     val TAGS = "gettags" 404
//    const val NOTES = "${BASE_API}getnotes"  //api needed to be changesd
//    const val KEYWORD_WITH_QUERY = "${BASE_API}getKeywordswithquery"  //data present in string
//    const val GENERATE_TOKEN = "${BASE_API}generatetoken"
//    const val TASKS = "${BASE_API}gettasks" //404

}