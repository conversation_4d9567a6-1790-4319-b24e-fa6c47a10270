package com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action

import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetails

sealed interface ActivationDetailsViewModelAction {
    data class OnInfoChanged(val activationDetails: ActivationDetails):ActivationDetailsViewModelAction
    data object OnSubmitClicked:ActivationDetailsViewModelAction
}