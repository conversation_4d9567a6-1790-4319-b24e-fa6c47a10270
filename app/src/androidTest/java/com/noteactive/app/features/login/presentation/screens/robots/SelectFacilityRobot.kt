package com.noteactive.app.features.login.presentation.screens.robots

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.test.ext.junit.rules.ActivityScenarioRule
import com.noteactive.app.features.MainActivity

/**
 * Robot Pattern Implementation for SelectFacilityScreen
 * 
 * This robot provides a fluent API for testing the SelectFacilityScreen.
 * It handles facility search, selection, and navigation to the main
 * application after successful facility selection.
 * 
 * Key Features:
 * - Facility list display verification
 * - Search functionality testing
 * - Facility selection validation
 * - Navigation to main app
 * - Error handling for empty facility lists
 * - Accessibility testing
 * 
 * Usage Example:
 * ```
 * selectFacilityRobot
 *     .verifyScreenIsDisplayed()
 *     .searchForFacility("Test")
 *     .selectFacility("Test Facility")
 *     .verifyNavigationToMainApp()
 * ```
 */
class SelectFacilityRobot(
    private val composeTestRule: AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>
) {

    /**
     * Verify that the SelectFacilityScreen is displayed
     */
    fun verifyScreenIsDisplayed(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("Select Facility")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Search for a facility using the search field
     */
    fun searchForFacility(searchTerm: String): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput(searchTerm)
        
        return this
    }

    /**
     * Clear the search field
     */
    fun clearSearch(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextClearance()
        
        return this
    }

    /**
     * Select a facility from the list
     */
    fun selectFacility(facilityName: String): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText(facilityName)
            .performClick()
        
        return this
    }

    /**
     * Select a facility from search results
     */
    fun selectFacilityFromSearch(facilityName: String): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText(facilityName)
            .assertIsDisplayed()
            .performClick()
        
        return this
    }

    /**
     * Verify that search results are displayed
     */
    fun verifySearchResults(expectedFacility: String): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText(expectedFacility)
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that no search results are found
     */
    fun verifyNoSearchResults(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("No facilities found")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that a facility is selected (highlighted)
     */
    fun verifyFacilityIsSelected(facilityName: String): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText(facilityName)
            .assertIsSelected()
        
        return this
    }

    /**
     * Verify navigation to main application
     */
    fun verifyNavigationToMainApp(): SelectFacilityRobot {
        composeTestRule.waitForIdle()
        
        // Verify main app elements are displayed
        composeTestRule
            .onNodeWithText("Notes")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that the facility list is populated with real data from API
     */
    fun verifyFacilityListIsPopulated(): SelectFacilityRobot {
        // Wait for real facility data to load
        composeTestRule.waitForIdle()

        // Verify at least one facility is displayed from real API
        composeTestRule.waitUntil(timeoutMillis = 15000) {
            try {
                composeTestRule
                    .onAllNodesWithTag("facility_item")
                    .assertCountEquals(1)
                true
            } catch (e: AssertionError) {
                false
            }
        }

        return this
    }

    /**
     * Select the first available facility from real API data
     */
    fun selectFirstAvailableFacility(): SelectFacilityRobot {
        // Wait for facility list to load
        composeTestRule.waitForIdle()

        // Select the first facility from the real list
        composeTestRule
            .onAllNodesWithTag("facility_item")
            .onFirst()
            .performClick()

        return this
    }

    /**
     * Verify that the facility list is empty
     */
    fun verifyFacilityListIsEmpty(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("No facilities available")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test scrolling through the facility list
     */
    fun testFacilityListScrolling(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithTag("facility_list")
            .performScrollToIndex(5)
        
        return this
    }

    /**
     * Verify the toolbar with back button
     */
    fun verifyToolbar(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("Select Facility")
            .assertIsDisplayed()
        
        composeTestRule
            .onNodeWithContentDescription("Back")
            .assertIsDisplayed()
            .assertHasClickAction()
        
        return this
    }

    /**
     * Click the back button
     */
    fun clickBackButton(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithContentDescription("Back")
            .performClick()
        
        return this
    }

    /**
     * Verify navigation back to login screen
     */
    fun verifyNavigationToLoginScreen(): SelectFacilityRobot {
        composeTestRule.waitForIdle()
        
        composeTestRule
            .onNodeWithText("Select Customer")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test search with special characters
     */
    fun testSearchWithSpecialCharacters(): SelectFacilityRobot {
        val specialSearchTerms = listOf("@", "#", "$", "%", "&", "*")
        
        specialSearchTerms.forEach { term ->
            clearSearch()
            searchForFacility(term)
            composeTestRule.waitForIdle()
        }
        
        return this
    }

    /**
     * Test search with empty string
     */
    fun testEmptySearch(): SelectFacilityRobot {
        clearSearch()
        
        // Verify all facilities are shown when search is empty
        verifyFacilityListIsPopulated()
        
        return this
    }

    /**
     * Test case-insensitive search
     */
    fun testCaseInsensitiveSearch(): SelectFacilityRobot {
        // Test lowercase
        clearSearch()
        searchForFacility("test")
        verifySearchResults("Test Facility")
        
        // Test uppercase
        clearSearch()
        searchForFacility("TEST")
        verifySearchResults("Test Facility")
        
        // Test mixed case
        clearSearch()
        searchForFacility("TeSt")
        verifySearchResults("Test Facility")
        
        return this
    }

    /**
     * Verify accessibility labels and descriptions
     */
    fun verifyAccessibilityLabels(): SelectFacilityRobot {
        // Verify search field accessibility
        composeTestRule
            .onNodeWithText("Search Facility")
            .assertHasClickAction()
        
        // Verify back button accessibility
        composeTestRule
            .onNodeWithContentDescription("Back")
            .assertHasClickAction()
        
        // Verify facility items accessibility
        composeTestRule
            .onAllNodesWithTag("facility_item")
            .assertAll(hasClickAction())
        
        return this
    }

    /**
     * Test keyboard navigation functionality
     */
    fun testKeyboardNavigation(): SelectFacilityRobot {
        // Test search field focus
        composeTestRule
            .onNodeWithText("Search Facility")
            .requestFocus()
            .assertIsFocused()
        
        // Test IME action
        composeTestRule
            .onNodeWithText("Search Facility")
            .performImeAction()
        
        return this
    }

    /**
     * Test rapid facility selection (debouncing)
     */
    fun testRapidFacilitySelection(): SelectFacilityRobot {
        val facilityName = "Test Facility"
        
        repeat(5) {
            composeTestRule
                .onNodeWithText(facilityName)
                .performClick()
        }
        
        composeTestRule.waitForIdle()
        
        return this
    }

    /**
     * Verify loading state while facilities are being fetched
     */
    fun verifyLoadingState(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithContentDescription("Loading facilities")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify error state when facility loading fails
     */
    fun verifyErrorState(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("Failed to load facilities")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test retry functionality after error
     */
    fun testRetryAfterError(): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("Retry")
            .performClick()
        
        verifyLoadingState()
        
        return this
    }

    /**
     * Verify facility count display
     */
    fun verifyFacilityCount(expectedCount: Int): SelectFacilityRobot {
        composeTestRule
            .onNodeWithText("$expectedCount facilities")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test long facility names display
     */
    fun testLongFacilityNames(): SelectFacilityRobot {
        val longFacilityName = "Very Long Facility Name That Should Be Handled Properly"
        
        composeTestRule
            .onNodeWithText(longFacilityName)
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test facility selection persistence
     */
    fun testFacilitySelectionPersistence(): SelectFacilityRobot {
        val facilityName = "Test Facility"
        
        selectFacility(facilityName)
        verifyFacilityIsSelected(facilityName)
        
        // Navigate away and back
        clickBackButton()
        verifyNavigationToLoginScreen()
        
        // Navigate back to facility selection
        // (This would require additional navigation logic)
        
        return this
    }

    /**
     * Verify search result highlighting
     */
    fun verifySearchResultHighlighting(): SelectFacilityRobot {
        searchForFacility("Test")
        
        // Verify search term is highlighted in results
        composeTestRule
            .onNodeWithTag("highlighted_text")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test search performance with large facility lists
     */
    fun testSearchPerformance(): SelectFacilityRobot {
        // Test search with large dataset
        searchForFacility("a")

        // Verify search completes within reasonable time
        composeTestRule.waitForIdle()

        return this
    }

    /**
     * Test facility search with real data from API
     */
    fun testRealFacilitySearch(): SelectFacilityRobot {
        // Test search functionality with real facility data
        searchForFacility("Facility")

        // Verify search results contain real facilities
        composeTestRule.waitForIdle()

        // Verify at least one result is shown
        composeTestRule
            .onAllNodesWithTag("facility_item")
            .assertCountEquals(1)

        // Clear search to show all facilities
        clearSearch()

        return this
    }

    /**
     * Test rapid facility selection (debouncing)
     */

}
