package com.noteactive.app.features.login.presentation.screens

import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.navigation.compose.rememberNavController
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.features.MainActivity
import com.noteactive.app.features.login.presentation.screens.robots.ActivationDetailRobot
import com.noteactive.app.features.login.presentation.screens.robots.EnterActivationKeyRobot
import com.noteactive.app.features.login.presentation.screens.robots.LoginScreenRobot
import com.noteactive.app.features.login.presentation.screens.robots.SelectFacilityRobot
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * End-to-End UI Tests for Login Feature Flow with Real API Integration
 *
 * This test class covers the complete login flow across all screens using
 * actual API calls to your backend services:
 * 1. EnterActivationKeyScreen - User enters real activation key
 * 2. ActivationDetailScreen - User reviews real activation details from API
 * 3. LoginScreen - User selects real customer and enters PIN
 * 4. SelectFacilityScreen - User selects from real facility data
 *
 * Test Coverage:
 * - Complete user journey with real backend integration
 * - Actual API call verification and response validation
 * - Real network response handling and error scenarios
 * - Navigation flow validation with live data
 * - UI state management with actual API responses
 * - Performance testing with real network latency
 * - Error handling for actual API failures
 *
 * Prerequisites:
 * - Android device or emulator with API 24+
 * - Network connectivity to your backend services
 * - Valid test activation keys in your backend
 * - Test environment properly configured
 * - Hilt dependency injection setup
 *
 * Test Data:
 * - Real activation keys: "hpdemo" (from your codebase)
 * - Invalid activation keys that will trigger real API errors
 * - Actual customer and facility data from your backend
 *
 * Expected Results:
 * - Successful navigation through all screens with real data
 * - Proper handling of actual API responses
 * - Correct UI updates based on real backend data
 * - Appropriate error handling for real API failures
 * - Performance within acceptable limits for real network calls
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class LoginFlowEndToEndTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    private lateinit var enterActivationKeyRobot: EnterActivationKeyRobot
    private lateinit var activationDetailRobot: ActivationDetailRobot
    private lateinit var loginScreenRobot: LoginScreenRobot
    private lateinit var selectFacilityRobot: SelectFacilityRobot

    @Before
    fun setup() {
        hiltRule.inject()
        
        // Initialize robots for each screen
        enterActivationKeyRobot = EnterActivationKeyRobot(composeTestRule)
        activationDetailRobot = ActivationDetailRobot(composeTestRule)
        loginScreenRobot = LoginScreenRobot(composeTestRule)
        selectFacilityRobot = SelectFacilityRobot(composeTestRule)
    }

    /**
     * Test Case: Complete Happy Path Login Flow with Real API
     *
     * Scenario: User successfully completes entire login process using real backend
     *
     * Steps:
     * 1. Enter valid activation key that exists in your backend
     * 2. Wait for real API call and verify actual response data
     * 3. Review real activation details from API and continue
     * 4. Navigate to login screen with real customer data
     * 5. Select actual customer from API response and enter PIN
     * 6. Navigate to facility selection with real facility data
     * 7. Select actual facility from API and complete login
     *
     * Expected Result: User successfully navigates through all screens
     * using real data from your backend services
     */
    @Test
    fun completeLoginFlow_realApi_happyPath_navigatesSuccessfully() {
        // Step 1: Enter Real Activation Key
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .verifyApiCallIsTriggered()
            .waitForApiSuccess()  // Wait for real API response
            .verifyNavigationToActivationDetail()

        // Step 2: Verify Real Activation Detail Data
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .verifyActivationDetailsAreShown()
            .waitForFacilityDataLoad()  // Wait for real facility API call
            .clickContinueButton()
            .verifyNavigationToLoginScreen()

        // Step 3: Login with Real Customer Data
        loginScreenRobot
            .verifyScreenIsDisplayed()
            .verifyCustomerListIsPopulated()  // Verify real customers loaded
            .selectFirstAvailableCustomer()  // Select from real customer list
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .clickLoginButton()
            .verifyNavigationToFacilitySelection()

        // Step 4: Select Real Facility
        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .verifyFacilityListIsPopulated()  // Verify real facilities loaded
            .selectFirstAvailableFacility()  // Select from real facility list
            .verifyNavigationToMainApp()
    }

    /**
     * Test Case: Invalid Activation Key Error Handling
     * 
     * Scenario: User enters invalid activation key
     * 
     * Steps:
     * 1. Enter invalid activation key
     * 2. Verify error message is displayed
     * 3. Verify no navigation occurs
     * 4. Enter valid key and continue
     * 
     * Expected Result: Error is handled gracefully and user can recover
     */
    @Test
    fun loginFlow_invalidActivationKey_showsErrorAndAllowsRecovery() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey("invalid_key")
            .clickContinueButton()
            .verifyErrorIsDisplayed()
            .verifyNoNavigationOccurs()
            .clearActivationKey()
            .enterActivationKey("hpdemo")
            .clickContinueButton()
            .verifyNavigationToActivationDetail()
    }

    /**
     * Test Case: Empty Activation Key Validation
     * 
     * Scenario: User tries to continue with empty activation key
     * 
     * Steps:
     * 1. Verify continue button is disabled initially
     * 2. Enter text and verify button becomes enabled
     * 3. Clear text and verify button becomes disabled again
     * 
     * Expected Result: Button state correctly reflects input validation
     */
    @Test
    fun loginFlow_emptyActivationKey_disablesContinueButton() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .verifyContinueButtonIsDisabled()
            .enterActivationKey("test")
            .verifyContinueButtonIsEnabled()
            .clearActivationKey()
            .verifyContinueButtonIsDisabled()
    }

    /**
     * Test Case: Back Navigation Handling
     * 
     * Scenario: User navigates back through the flow
     * 
     * Steps:
     * 1. Complete activation key entry
     * 2. Navigate to activation details
     * 3. Press back button
     * 4. Verify return to activation key screen
     * 
     * Expected Result: Back navigation works correctly
     */
    @Test
    fun loginFlow_backNavigation_returnsToCorrectScreen() {
        // Navigate forward
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey("hpdemo")
            .clickContinueButton()
            .verifyNavigationToActivationDetail()

        // Navigate back
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickBackButton()
            .verifyNavigationToActivationKey()

        // Verify we're back at the first screen
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
    }

    /**
     * Test Case: Network Error Handling
     * 
     * Scenario: API call fails due to network error
     * 
     * Steps:
     * 1. Enter valid activation key
     * 2. Simulate network error
     * 3. Verify error handling
     * 4. Retry with network restored
     * 
     * Expected Result: Network errors are handled gracefully
     */
    @Test
    fun loginFlow_networkError_handlesGracefully() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey("hpdemo")
            .simulateNetworkError()
            .clickContinueButton()
            .verifyNetworkErrorIsDisplayed()
            .restoreNetwork()
            .clickContinueButton()
            .verifyNavigationToActivationDetail()
    }

    /**
     * Test Case: Facility Search and Selection
     * 
     * Scenario: User searches for and selects a facility
     * 
     * Steps:
     * 1. Complete flow to facility selection
     * 2. Search for specific facility
     * 3. Verify search results
     * 4. Select facility from search results
     * 
     * Expected Result: Facility search and selection works correctly
     */
    @Test
    fun loginFlow_facilitySearchAndSelection_worksCorrectly() {
        // Navigate to facility selection
        completeFlowToFacilitySelection()

        // Test facility search and selection
        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .searchForFacility("Test")
            .verifySearchResults("Test Facility")
            .selectFacilityFromSearch("Test Facility")
            .verifyFacilityIsSelected("Test Facility")
            .verifyNavigationToMainApp()
    }

    /**
     * Test Case: Accessibility Compliance
     * 
     * Scenario: Verify accessibility features work across all screens
     * 
     * Steps:
     * 1. Navigate through each screen
     * 2. Verify accessibility labels and descriptions
     * 3. Test keyboard navigation
     * 4. Verify screen reader compatibility
     * 
     * Expected Result: All screens are accessible
     */
    @Test
    fun loginFlow_accessibility_compliesWithStandards() {
        // Test accessibility on each screen
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()

        enterActivationKeyRobot
            .enterActivationKey("hpdemo")
            .clickContinueButton()

        activationDetailRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()

        activationDetailRobot
            .clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()
    }

    /**
     * Helper method to complete the flow up to facility selection
     */
    private fun completeFlowToFacilitySelection() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey("hpdemo")
            .clickContinueButton()

        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer("Test Customer")
            .enterPin("1234")
            .clickLoginButton()
    }
}
