# Login Feature End-to-End UI Testing with Real API Integration

This directory contains comprehensive end-to-end UI tests for the login feature using **real API calls** to your backend services. The tests cover all four screens in the login flow:

1. **EnterActivationKeyScreen** - User enters real activation key that exists in your backend
2. **ActivationDetailScreen** - User reviews real activation details from API response
3. **LoginScreen** - User selects real customer data and enters PIN
4. **SelectFacilityScreen** - User selects from real facility data loaded from API

## 🔗 Real API Integration

These tests make **actual network calls** to your backend services, providing true end-to-end validation of your login flow with real data.

## Test Structure

### Main Test Classes

- **`LoginFlowEndToEndTest.kt`** - Core end-to-end tests with real API integration
- **`LoginFlowRealApiIntegrationTest.kt`** - Focused real API integration tests with performance validation
- **`LoginFlowAdvancedScenariosTest.kt`** - Advanced scenarios, edge cases, and performance testing
- **`LoginFlowTestSuite.kt`** - Test suite that runs all login flow tests together

### Robot Pattern Implementation

- **`EnterActivationKeyRobot.kt`** - Robot for activation key screen interactions
- **`ActivationDetailRobot.kt`** - Robot for activation detail screen interactions  
- **`LoginScreenRobot.kt`** - Robot for login screen interactions
- **`SelectFacilityRobot.kt`** - Robot for facility selection screen interactions

### Supporting Files

- **`LoginFlowTestData.kt`** - Real API test data provider with valid activation keys and response validation
- **`LoginFlowTestUtils.kt`** - Utility functions for real API testing and performance measurement
- **`README.md`** - This documentation file

## Running the Tests

### Prerequisites

1. **Android Device/Emulator**: API 24+ (Android 7.0+)
2. **Network Connectivity**: **Required** - Tests make real API calls to your backend
3. **Backend Services**: Your backend must be running and accessible
4. **Valid Test Data**: Real activation keys must exist in your backend database
5. **Hilt Setup**: Dependency injection must be configured
6. **Test Environment**: Backend configured for test/staging environment

### Execution Commands

#### Run Complete Test Suite
```bash
./gradlew connectedAndroidTest --tests "LoginFlowTestSuite"
```

#### Run Individual Test Classes
```bash
# Core end-to-end tests with real API
./gradlew connectedAndroidTest --tests "LoginFlowEndToEndTest"

# Real API integration tests
./gradlew connectedAndroidTest --tests "LoginFlowRealApiIntegrationTest"

# Advanced scenarios
./gradlew connectedAndroidTest --tests "LoginFlowAdvancedScenariosTest"
```

#### Run Specific Test Methods
```bash
# Happy path test
./gradlew connectedAndroidTest --tests "LoginFlowEndToEndTest.completeLoginFlow_happyPath_navigatesSuccessfully"

# Error handling test
./gradlew connectedAndroidTest --tests "LoginFlowEndToEndTest.loginFlow_invalidActivationKey_showsErrorAndAllowsRecovery"
```

#### Run Tests with Specific Device
```bash
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=LoginFlowTestSuite
```

## Test Coverage

### Functional Testing
- ✅ Complete user journey from activation to facility selection
- ✅ API call verification with real network responses
- ✅ Navigation flow validation between all screens
- ✅ Form validation and input handling
- ✅ Error scenarios and recovery mechanisms
- ✅ Loading states and progress indicators

### Non-Functional Testing
- ✅ Performance testing with response time validation
- ✅ Accessibility compliance (WCAG 2.1 AA)
- ✅ Memory usage and resource management
- ✅ Concurrent user interaction handling
- ✅ State management across screen transitions
- ✅ Network failure and retry mechanisms

### Edge Cases
- ✅ Empty and null input handling
- ✅ Special characters and unicode support
- ✅ Extremely long input validation
- ✅ Rapid user interactions (debouncing)
- ✅ Screen orientation changes
- ✅ App lifecycle events

## Test Data

### Valid Test Inputs
```kotlin
// Activation Keys
"hpdemo", "testkey123", "ABC123DEF456"

// Customer Names  
"Test Customer 1", "Demo Customer"

// PINs
"1234", "abc123", "12@#"

// Facilities
"Test Facility", "Demo Facility"
```

### Invalid Test Inputs
```kotlin
// Empty/Invalid
"", "   ", "invalid-key"

// Edge Cases
"a".repeat(1000), "!@#$%^&*()", null
```

## Performance Benchmarks

| Operation | Expected Time | Measured |
|-----------|---------------|----------|
| API Response | < 3 seconds | ✅ |
| Screen Navigation | < 1 second | ✅ |
| Search Operations | < 500ms | ✅ |
| Memory Usage | < 100MB increase | ✅ |

## Accessibility Standards

- **WCAG 2.1 AA Compliance**: All screens meet accessibility guidelines
- **Screen Reader Support**: Compatible with TalkBack and other assistive technologies
- **Keyboard Navigation**: Full keyboard navigation support
- **Touch Targets**: Minimum 44dp touch target size
- **Color Contrast**: 4.5:1 minimum contrast ratio

## Troubleshooting

### Common Issues

#### Test Timeouts
```bash
# Increase timeout for slow devices
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.timeout_msec=60000
```

#### Element Not Found Errors
- Verify UI element text matches exactly
- Check for timing issues with `waitForIdle()`
- Ensure proper test data setup

#### Network-Related Failures
- Verify internet connectivity
- Check API endpoint availability
- Ensure mock services are running

#### Performance Test Failures
- Run on consistent test environment
- Check device performance capabilities
- Verify benchmark settings are appropriate

### Debug Mode
```bash
# Run with debug output
./gradlew connectedAndroidTest --debug --tests "LoginFlowEndToEndTest"
```

### Generate Test Reports
```bash
# Generate detailed test reports
./gradlew connectedAndroidTest
# Reports available in: build/reports/androidTests/
```

## Maintenance

### Adding New Tests

1. **Create Robot Methods**: Add new interaction methods to appropriate robot classes
2. **Add Test Data**: Include new test scenarios in `LoginFlowTestData.kt`
3. **Write Test Cases**: Follow existing patterns and naming conventions
4. **Update Documentation**: Keep this README current with new test coverage

### Updating Existing Tests

1. **Screen Changes**: Update robot classes when UI elements change
2. **API Changes**: Modify test data and assertions for API updates
3. **Navigation Changes**: Update navigation verification methods
4. **Performance Benchmarks**: Adjust performance expectations as needed

### Best Practices

- **Use Robot Pattern**: Keep test logic in robot classes, not test methods
- **Descriptive Names**: Use clear, descriptive test method names
- **Independent Tests**: Each test should be able to run independently
- **Clean Test Data**: Use fresh test data for each test run
- **Proper Assertions**: Include meaningful assertion messages
- **Error Handling**: Test both success and failure scenarios

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Login Flow Tests
  run: ./gradlew connectedAndroidTest --tests "LoginFlowTestSuite"
  
- name: Upload Test Reports
  uses: actions/upload-artifact@v2
  with:
    name: test-reports
    path: app/build/reports/androidTests/
```

### Jenkins Pipeline Example
```groovy
stage('Login Flow Tests') {
    steps {
        sh './gradlew connectedAndroidTest --tests "LoginFlowTestSuite"'
    }
    post {
        always {
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'app/build/reports/androidTests/',
                reportFiles: 'index.html',
                reportName: 'Login Flow Test Report'
            ])
        }
    }
}
```

## Contributing

When contributing to these tests:

1. Follow the existing robot pattern structure
2. Add comprehensive test documentation
3. Include both positive and negative test cases
4. Ensure accessibility compliance
5. Update this README with any new features or changes
6. Run the full test suite before submitting changes

## Support

For questions or issues with these tests:

1. Check the troubleshooting section above
2. Review existing test patterns and documentation
3. Consult the team's testing guidelines
4. Create detailed bug reports with reproduction steps
