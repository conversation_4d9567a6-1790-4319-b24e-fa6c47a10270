package com.noteactive.app.features.login.presentation.screens.robots

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.test.ext.junit.rules.ActivityScenarioRule
import com.noteactive.app.features.MainActivity

/**
 * Robot Pattern Implementation for LoginScreen
 * 
 * This robot provides a fluent API for testing the LoginScreen.
 * It handles customer selection, PIN entry, and navigation to
 * facility selection or main application.
 * 
 * Key Features:
 * - Customer selection testing
 * - PIN entry validation
 * - Login button state management
 * - Navigation validation
 * - Error handling for invalid credentials
 * - Accessibility testing
 * 
 * Usage Example:
 * ```
 * loginScreenRobot
 *     .verifyScreenIsDisplayed()
 *     .selectCustomer("Test Customer")
 *     .enterPin("1234")
 *     .clickLoginButton()
 *     .verifyNavigationToFacilitySelection()
 * ```
 */
class LoginScreenRobot(
    private val composeTestRule: AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>
) {

    /**
     * Verify that the LoginScreen is displayed
     */
    fun verifyScreenIsDisplayed(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Select Customer")
            .assertIsDisplayed()
        
        composeTestRule
            .onNodeWithText("Enter PIN")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Select a customer from the dropdown or selection area
     */
    fun selectCustomer(customerName: String): LoginScreenRobot {
        // Click on customer selection area
        composeTestRule
            .onNodeWithText("Select Customer")
            .performClick()
        
        // Select the specific customer
        composeTestRule
            .onNodeWithText(customerName)
            .performClick()
        
        return this
    }

    /**
     * Enter PIN in the PIN input field
     */
    fun enterPin(pin: String): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Enter PIN")
            .performTextInput(pin)
        
        return this
    }

    /**
     * Clear the PIN input field
     */
    fun clearPin(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Enter PIN")
            .performTextClearance()
        
        return this
    }

    /**
     * Click the login button
     */
    fun clickLoginButton(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Login")
            .performClick()
        
        return this
    }

    /**
     * Click the select customer navigation button
     */
    fun clickSelectCustomerButton(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Select Customer")
            .performClick()
        
        return this
    }

    /**
     * Verify that the login button is enabled
     */
    fun verifyLoginButtonIsEnabled(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Login")
            .assertIsEnabled()
        
        return this
    }

    /**
     * Verify that the login button is disabled
     */
    fun verifyLoginButtonIsDisabled(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Login")
            .assertIsNotEnabled()
        
        return this
    }

    /**
     * Verify navigation to facility selection screen
     */
    fun verifyNavigationToFacilitySelection(): LoginScreenRobot {
        composeTestRule.waitForIdle()
        
        composeTestRule
            .onNodeWithText("Select Facility")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify navigation to main application (home screen)
     */
    fun verifyNavigationToMainApp(): LoginScreenRobot {
        composeTestRule.waitForIdle()
        
        // This would check for main app elements
        composeTestRule
            .onNodeWithText("Notes")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that a customer is selected and displayed
     */
    fun verifyCustomerIsSelected(customerName: String): LoginScreenRobot {
        composeTestRule
            .onNodeWithText(customerName)
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify PIN input validation
     */
    fun verifyPinValidation(): LoginScreenRobot {
        // Test empty PIN
        clearPin()
        verifyLoginButtonIsDisabled()
        
        // Test valid PIN
        enterPin("1234")
        verifyLoginButtonIsEnabled()
        
        return this
    }

    /**
     * Verify error message for invalid login
     */
    fun verifyInvalidLoginError(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Invalid PIN")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify error message for no customer selected
     */
    fun verifyNoCustomerSelectedError(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Please select a customer first")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test PIN input with various formats
     */
    fun testPinInputFormats(): LoginScreenRobot {
        // Test numeric PIN
        clearPin()
        enterPin("1234")
        verifyLoginButtonIsEnabled()
        
        // Test alphanumeric PIN
        clearPin()
        enterPin("abc123")
        verifyLoginButtonIsEnabled()
        
        // Test special characters
        clearPin()
        enterPin("12@#")
        verifyLoginButtonIsEnabled()
        
        return this
    }

    /**
     * Verify accessibility labels and descriptions
     */
    fun verifyAccessibilityLabels(): LoginScreenRobot {
        // Verify customer selection accessibility
        composeTestRule
            .onNodeWithText("Select Customer")
            .assertHasClickAction()
        
        // Verify PIN field accessibility
        composeTestRule
            .onNodeWithText("Enter PIN")
            .assertHasClickAction()
        
        // Verify login button accessibility
        composeTestRule
            .onNodeWithText("Login")
            .assertHasClickAction()
        
        return this
    }

    /**
     * Test keyboard navigation functionality
     */
    fun testKeyboardNavigation(): LoginScreenRobot {
        // Test tab navigation between fields
        composeTestRule
            .onNodeWithText("Select Customer")
            .requestFocus()
            .assertIsFocused()
        
        // Navigate to PIN field
        composeTestRule
            .onNodeWithText("Enter PIN")
            .requestFocus()
            .assertIsFocused()
        
        // Test IME action
        composeTestRule
            .onNodeWithText("Enter PIN")
            .performImeAction()
        
        return this
    }

    /**
     * Verify loading state during login API call
     */
    fun verifyLoadingState(): LoginScreenRobot {
        composeTestRule
            .onNodeWithContentDescription("Loading")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test rapid login button clicking (debouncing)
     */
    fun testRapidLoginClicking(): LoginScreenRobot {
        repeat(5) {
            composeTestRule
                .onNodeWithText("Login")
                .performClick()
        }
        
        composeTestRule.waitForIdle()
        
        return this
    }

    /**
     * Verify customer list is populated with real data from API
     */
    fun verifyCustomerListIsPopulated(): LoginScreenRobot {
        clickSelectCustomerButton()

        // Wait for real customer data to load
        composeTestRule.waitForIdle()

        // Verify at least one customer is available from real API
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onAllNodesWithTag("customer_item")
                    .assertCountEquals(1)
                true
            } catch (e: AssertionError) {
                false
            }
        }

        return this
    }

    /**
     * Select the first available customer from real API data
     */
    fun selectFirstAvailableCustomer(): LoginScreenRobot {
        clickSelectCustomerButton()

        // Wait for customer list to load
        composeTestRule.waitForIdle()

        // Select the first customer from the real list
        composeTestRule
            .onAllNodesWithTag("customer_item")
            .onFirst()
            .performClick()

        return this
    }

    /**
     * Test customer search functionality
     */
    fun testCustomerSearch(searchTerm: String): LoginScreenRobot {
        clickSelectCustomerButton()
        
        // Enter search term
        composeTestRule
            .onNodeWithText("Search customers")
            .performTextInput(searchTerm)
        
        // Verify search results
        composeTestRule
            .onNodeWithText(searchTerm)
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify PIN masking (password field)
     */
    fun verifyPinMasking(): LoginScreenRobot {
        enterPin("1234")
        
        // Verify PIN is masked (shows dots or asterisks)
        composeTestRule
            .onNodeWithText("••••")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test PIN length validation
     */
    fun testPinLengthValidation(): LoginScreenRobot {
        // Test short PIN
        clearPin()
        enterPin("12")
        verifyLoginButtonIsDisabled()
        
        // Test valid length PIN
        clearPin()
        enterPin("1234")
        verifyLoginButtonIsEnabled()
        
        // Test long PIN
        clearPin()
        enterPin("123456789")
        verifyLoginButtonIsEnabled()
        
        return this
    }

    /**
     * Verify biometric login option (if available)
     */
    fun verifyBiometricLoginOption(): LoginScreenRobot {
        composeTestRule
            .onNodeWithContentDescription("Biometric Login")
            .assertIsDisplayed()
            .assertHasClickAction()
        
        return this
    }

    /**
     * Test biometric login flow
     */
    fun testBiometricLogin(): LoginScreenRobot {
        composeTestRule
            .onNodeWithContentDescription("Biometric Login")
            .performClick()
        
        // Verify biometric prompt appears
        composeTestRule
            .onNodeWithText("Use fingerprint to login")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify remember me functionality
     */
    fun verifyRememberMeOption(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Remember me")
            .assertIsDisplayed()
            .assertHasClickAction()
        
        return this
    }

    /**
     * Test remember me checkbox
     */
    fun testRememberMeCheckbox(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Remember me")
            .performClick()
            .assertIsSelected()
        
        return this
    }

    /**
     * Verify forgot PIN option
     */
    fun verifyForgotPinOption(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Forgot PIN?")
            .assertIsDisplayed()
            .assertHasClickAction()
        
        return this
    }

    /**
     * Test forgot PIN flow
     */
    fun testForgotPinFlow(): LoginScreenRobot {
        composeTestRule
            .onNodeWithText("Forgot PIN?")
            .performClick()

        // Verify forgot PIN dialog or screen
        composeTestRule
            .onNodeWithText("Reset PIN")
            .assertIsDisplayed()

        return this
    }

    /**
     * Validate real customer data from API
     */
    fun validateRealCustomerData(): LoginScreenRobot {
        clickSelectCustomerButton()

        // Wait for real customer data to load
        composeTestRule.waitForIdle()

        // Verify customer data meets validation criteria
        composeTestRule
            .onAllNodesWithTag("customer_item")
            .assertAll(hasText())  // Ensure all customers have text

        return this
    }

    /**
     * Verify customer selection persists
     */
    fun verifyCustomerSelectionPersists(): LoginScreenRobot {
        // This would verify that the selected customer remains selected
        // Implementation depends on your UI structure

        return this
    }
}
