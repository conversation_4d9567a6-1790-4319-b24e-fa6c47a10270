package com.noteactive.app.features.login.presentation.screens

import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.features.MainActivity
import com.noteactive.app.features.login.presentation.screens.data.LoginFlowTestData
import com.noteactive.app.features.login.presentation.screens.robots.ActivationDetailRobot
import com.noteactive.app.features.login.presentation.screens.robots.EnterActivationKeyRobot
import com.noteactive.app.features.login.presentation.screens.robots.LoginScreenRobot
import com.noteactive.app.features.login.presentation.screens.robots.SelectFacilityRobot
import com.noteactive.app.features.login.presentation.screens.utils.LoginFlowTestUtils
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Advanced End-to-End Test Scenarios for Login Feature Flow
 * 
 * This test class covers advanced scenarios, edge cases, and error conditions
 * for the complete login flow. It complements the basic LoginFlowEndToEndTest
 * with more complex test scenarios.
 * 
 * Test Categories:
 * - Error Recovery Scenarios
 * - Performance Testing
 * - Accessibility Compliance
 * - Edge Cases and Boundary Conditions
 * - Network Failure Handling
 * - State Management Validation
 * - Security Testing
 * - Usability Testing
 * 
 * Prerequisites:
 * - Android device or emulator with API 24+
 * - Network connectivity for API calls
 * - Hilt dependency injection setup
 * - Mock data providers configured
 * 
 * Coverage Areas:
 * - Complex user interaction patterns
 * - Multi-step error recovery
 * - Performance under load
 * - Accessibility across all screens
 * - Data validation and sanitization
 * - Concurrent user actions
 */
@RunWith(AndroidJUnit4::class)
class LoginFlowAdvancedScenariosTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    private lateinit var enterActivationKeyRobot: EnterActivationKeyRobot
    private lateinit var activationDetailRobot: ActivationDetailRobot
    private lateinit var loginScreenRobot: LoginScreenRobot
    private lateinit var selectFacilityRobot: SelectFacilityRobot

    @Before
    fun setup() {
        enterActivationKeyRobot = EnterActivationKeyRobot(composeTestRule)
        activationDetailRobot = ActivationDetailRobot(composeTestRule)
        loginScreenRobot = LoginScreenRobot(composeTestRule)
        selectFacilityRobot = SelectFacilityRobot(composeTestRule)
    }

    /**
     * Test Case: Multiple Error Recovery Scenarios
     * 
     * Scenario: User encounters multiple errors and recovers from each
     * 
     * Steps:
     * 1. Enter invalid activation key -> recover
     * 2. Network error during API call -> recover
     * 3. Invalid PIN entry -> recover
     * 4. Facility loading error -> recover
     * 5. Complete successful login
     * 
     * Expected Result: User can recover from all errors and complete login
     */
    @Test
    fun loginFlow_multipleErrorRecovery_completesSuccessfully() {
        // Error 1: Invalid activation key
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.InvalidActivationKeys.INVALID_FORMAT)
            .clickContinueButton()
            .verifyErrorIsDisplayed()
            .clearActivationKey()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .verifyNavigationToActivationDetail()

        // Continue to login screen
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickContinueButton()

        // Error 2: Invalid PIN
        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer(LoginFlowTestData.MockCustomers.customers[0])
            .enterPin(LoginFlowTestData.InvalidPins.EMPTY)
            .verifyLoginButtonIsDisabled()
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .clickLoginButton()

        // Complete flow
        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .selectFacility(LoginFlowTestData.MockFacilities.facilities[0].facility)
            .verifyNavigationToMainApp()
    }

    /**
     * Test Case: Performance Under Load
     * 
     * Scenario: Test login flow performance with large datasets
     * 
     * Steps:
     * 1. Load large facility list
     * 2. Perform search operations
     * 3. Measure response times
     * 4. Verify performance meets requirements
     * 
     * Expected Result: All operations complete within acceptable time limits
     */
    @Test
    fun loginFlow_performanceUnderLoad_meetsRequirements() {
        // Complete flow to facility selection
        completeFlowToFacilitySelection()

        // Test search performance with large dataset
        val searchTime = LoginFlowTestUtils.measurePerformance {
            selectFacilityRobot
                .searchForFacility("Test")
                .verifySearchResults("Test Facility")
        }

        // Verify search completes within acceptable time
        assert(searchTime <= LoginFlowTestData.PerformanceTestData.MAX_SEARCH_TIME_MS) {
            "Search took ${searchTime}ms, expected <= ${LoginFlowTestData.PerformanceTestData.MAX_SEARCH_TIME_MS}ms"
        }

        // Test facility selection performance
        val selectionTime = LoginFlowTestUtils.measurePerformance {
            selectFacilityRobot.selectFacility("Test Facility")
        }

        assert(selectionTime <= LoginFlowTestData.PerformanceTestData.MAX_NAVIGATION_TIME_MS) {
            "Selection took ${selectionTime}ms, expected <= ${LoginFlowTestData.PerformanceTestData.MAX_NAVIGATION_TIME_MS}ms"
        }
    }

    /**
     * Test Case: Comprehensive Accessibility Testing
     * 
     * Scenario: Verify accessibility compliance across entire flow
     * 
     * Steps:
     * 1. Test screen reader compatibility
     * 2. Verify keyboard navigation
     * 3. Check color contrast and text size
     * 4. Test focus management
     * 5. Verify semantic markup
     * 
     * Expected Result: All screens meet accessibility standards
     */
    @Test
    fun loginFlow_accessibilityCompliance_meetsStandards() {
        // Test activation key screen accessibility
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()

        // Navigate and test activation details accessibility
        enterActivationKeyRobot
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()

        activationDetailRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()

        // Navigate and test login screen accessibility
        activationDetailRobot.clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()

        // Navigate and test facility selection accessibility
        loginScreenRobot
            .selectCustomer(LoginFlowTestData.MockCustomers.customers[0])
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .clickLoginButton()

        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .verifyAccessibilityLabels()
            .testKeyboardNavigation()
    }

    /**
     * Test Case: Edge Cases and Boundary Conditions
     * 
     * Scenario: Test with extreme inputs and edge cases
     * 
     * Steps:
     * 1. Test with very long activation keys
     * 2. Test with special characters
     * 3. Test with empty and null values
     * 4. Test with maximum input lengths
     * 5. Test with unicode characters
     * 
     * Expected Result: All edge cases are handled gracefully
     */
    @Test
    fun loginFlow_edgeCases_handledGracefully() {
        // Test long activation key
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .testLongActivationKeyInput()
            .clearActivationKey()

        // Test special characters
        enterActivationKeyRobot
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.WITH_SPECIAL_CHARS)
            .verifyContinueButtonIsEnabled()
            .clickContinueButton()
            .verifyNavigationToActivationDetail()

        // Test activation details with long names
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .verifyActivationDetailValues(
                firstName = "Christopher Alexander",
                lastName = "Montgomery-Wellington"
            )
            .clickContinueButton()

        // Test login with special character customer names
        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer(LoginFlowTestData.MockCustomers.customersWithSpecialChars[0])
            .enterPin(LoginFlowTestData.ValidPins.SPECIAL_CHAR_PIN)
            .clickLoginButton()

        // Test facility selection with special characters
        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .testSearchWithSpecialCharacters()
            .selectFacility(LoginFlowTestData.MockFacilities.facilitiesWithSpecialChars[0].facility)
    }

    /**
     * Test Case: Concurrent User Actions
     * 
     * Scenario: Test rapid user interactions and concurrent actions
     * 
     * Steps:
     * 1. Test rapid button clicking
     * 2. Test simultaneous input and navigation
     * 3. Test interrupting API calls
     * 4. Test navigation during loading states
     * 
     * Expected Result: UI remains stable under concurrent actions
     */
    @Test
    fun loginFlow_concurrentActions_maintainsStability() {
        // Test rapid clicking on activation key screen
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .testRapidButtonClicking()

        // Navigate to next screen
        composeTestRule.waitForIdle()
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .testRapidButtonClicking()

        // Navigate to login screen
        composeTestRule.waitForIdle()
        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer(LoginFlowTestData.MockCustomers.customers[0])
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .testRapidLoginClicking()

        // Navigate to facility selection
        composeTestRule.waitForIdle()
        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .testRapidFacilitySelection()
    }

    /**
     * Test Case: State Management Validation
     * 
     * Scenario: Verify state persistence and management across screens
     * 
     * Steps:
     * 1. Enter data on each screen
     * 2. Navigate back and forth
     * 3. Verify data persistence
     * 4. Test state cleanup
     * 
     * Expected Result: State is properly managed throughout the flow
     */
    @Test
    fun loginFlow_stateManagement_maintainsConsistency() {
        // Enter activation key and navigate forward
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()

        // Navigate to activation details and back
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickBackButton()

        // Verify activation key is still present
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            // Note: This would require checking if the text field still contains the value

        // Complete flow and test state persistence
        enterActivationKeyRobot
            .clickContinueButton()

        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer(LoginFlowTestData.MockCustomers.customers[0])
            .verifyCustomerIsSelected(LoginFlowTestData.MockCustomers.customers[0])
    }

    /**
     * Test Case: Network Failure Scenarios
     * 
     * Scenario: Test various network failure conditions
     * 
     * Steps:
     * 1. Simulate network timeout
     * 2. Simulate server errors
     * 3. Simulate intermittent connectivity
     * 4. Test retry mechanisms
     * 
     * Expected Result: Network failures are handled gracefully with retry options
     */
    @Test
    fun loginFlow_networkFailures_handledGracefully() {
        // Test network timeout during activation key verification
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .simulateNetworkError()
            .clickContinueButton()
            .verifyNetworkErrorIsDisplayed()

        // Test recovery after network restoration
        enterActivationKeyRobot
            .restoreNetwork()
            .clickContinueButton()
            .verifyNavigationToActivationDetail()

        // Continue with normal flow
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer(LoginFlowTestData.MockCustomers.customers[0])
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .clickLoginButton()

        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .selectFacility(LoginFlowTestData.MockFacilities.facilities[0].facility)
    }

    /**
     * Helper method to complete the flow up to facility selection
     */
    private fun completeFlowToFacilitySelection() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()

        activationDetailRobot
            .verifyScreenIsDisplayed()
            .clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectCustomer(LoginFlowTestData.MockCustomers.customers[0])
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .clickLoginButton()
    }
}
