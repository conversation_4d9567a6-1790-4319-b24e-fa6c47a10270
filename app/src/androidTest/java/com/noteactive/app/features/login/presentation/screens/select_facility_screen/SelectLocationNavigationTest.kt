package com.noteactive.app.features.login.presentation.screens.select_facility_screen

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityNavigationAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.SelectFacilityState
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.getFacility
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class SelectLocationNavigationTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun testFacilitySelectionTriggersNavigationWithCorrectFacility() {
        // Arrange
        var capturedAction: SelectFacilityViewModelAction? = null
        var capturedNavigation: SelectFacilityNavigationAction? = null
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = getFacility(),
                filteredList = getFacility(),
                searchText = ""
            )
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        capturedAction = action
                        when (action) {
                            is SelectFacilityViewModelAction.OnFacilitySelectAction -> {
                                val updatedAllFacilities = currentState.allFacilityList.map {
                                    it.copy(isSelected = it == action.facility)
                                }
                                val updatedFilteredList = updatedAllFacilities.filter {
                                    it.facilityName.contains(
                                        currentState.searchText,
                                        ignoreCase = true
                                    )
                                }
                                currentState = currentState.copy(
                                    allFacilityList = updatedAllFacilities,
                                    filteredList = updatedFilteredList
                                )
                            }

                            else -> {}
                        }
                    },
                    selectFacilityNavigation = { navigation ->
                        capturedNavigation = navigation
                    }
                )
            }
        }

        // Click on Facility 3
        composeTestRule
            .onNodeWithText("Facility 3")
            .assertIsDisplayed()
            .performClick()

        composeTestRule.waitForIdle()

        // Assert
        assert(capturedAction is SelectFacilityViewModelAction.OnFacilitySelectAction) {
            "Expected OnFacilitySelectAction but got: $capturedAction"
        }

        val selectAction = capturedAction as SelectFacilityViewModelAction.OnFacilitySelectAction
        assert(selectAction.facility.facilityName == "Facility 3") {
            "Expected facility name to be 'Facility 3' but was '${selectAction.facility.facilityName}'"
        }

        assert(capturedNavigation is SelectFacilityNavigationAction.NavigateBackWithFacility) {
            "Expected NavigateBackWithFacility but got: $capturedNavigation"
        }

        val navigationAction = capturedNavigation as SelectFacilityNavigationAction.NavigateBackWithFacility
        assert(navigationAction.facility.facilityName == "Facility 3") {
            "Expected navigation facility name to be 'Facility 3' but was '${navigationAction.facility.facilityName}'"
        }
    }

    @Test
    fun testCompleteFlowSearchAndSelectFacility() {
        // Arrange
        var capturedActions = mutableListOf<SelectFacilityViewModelAction>()
        var capturedNavigation: SelectFacilityNavigationAction? = null
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = getFacility(),
                filteredList = getFacility(),
                searchText = ""
            )
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        capturedActions.add(action)
                        when (action) {
                            is SelectFacilityViewModelAction.OnSearchFacilityAction -> {
                                val filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                                currentState = currentState.copy(
                                    searchText = action.searchText,
                                    filteredList = filteredList
                                )
                            }

                            is SelectFacilityViewModelAction.OnFacilitySelectAction -> {
                                val updatedAllFacilities = currentState.allFacilityList.map {
                                    it.copy(isSelected = it == action.facility)
                                }
                                val updatedFilteredList = updatedAllFacilities.filter {
                                    it.facilityName.contains(
                                        currentState.searchText,
                                        ignoreCase = true
                                    )
                                }
                                currentState = currentState.copy(
                                    allFacilityList = updatedAllFacilities,
                                    filteredList = updatedFilteredList
                                )
                            }

                            else -> {}
                        }
                    },
                    selectFacilityNavigation = { navigation ->
                        capturedNavigation = navigation
                    }
                )
            }
        }

        // Step 1: Search for "Facility 1"
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("Facility 1")

        composeTestRule.waitForIdle()

        // Step 2: Select "Facility 10" from filtered results
        composeTestRule
            .onNodeWithText("Facility 10")
            .assertIsDisplayed()
            .performClick()

        composeTestRule.waitForIdle()

        // Assert
        assert(capturedActions.size >= 2) {
            "Expected at least 2 actions but got ${capturedActions.size}: $capturedActions"
        }

        // Verify search action
        val searchAction = capturedActions.find { it is SelectFacilityViewModelAction.OnSearchFacilityAction }
        assert(searchAction != null) {
            "Expected OnSearchFacilityAction in captured actions: $capturedActions"
        }

        // Verify select action
        val selectAction = capturedActions.find { it is SelectFacilityViewModelAction.OnFacilitySelectAction }
        assert(selectAction != null) {
            "Expected OnFacilitySelectAction in captured actions: $capturedActions"
        }

        val facilitySelectAction = selectAction as SelectFacilityViewModelAction.OnFacilitySelectAction
        assert(facilitySelectAction.facility.facilityName == "Facility 10") {
            "Expected selected facility to be 'Facility 10' but was '${facilitySelectAction.facility.facilityName}'"
        }

        // Verify navigation
        assert(capturedNavigation is SelectFacilityNavigationAction.NavigateBackWithFacility) {
            "Expected NavigateBackWithFacility but got: $capturedNavigation"
        }

        val navigationAction = capturedNavigation as SelectFacilityNavigationAction.NavigateBackWithFacility
        assert(navigationAction.facility.facilityName == "Facility 10") {
            "Expected navigation facility to be 'Facility 10' but was '${navigationAction.facility.facilityName}'"
        }
    }

    @Test
    fun testMultipleFacilitySelectionsOnlyLastOneIsSelected() {
        // Arrange
        var capturedActions = mutableListOf<SelectFacilityViewModelAction>()
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = getFacility(),
                filteredList = getFacility(),
                searchText = ""
            )
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        capturedActions.add(action)
                        when (action) {
                            is SelectFacilityViewModelAction.OnFacilitySelectAction -> {
                                val updatedAllFacilities = currentState.allFacilityList.map {
                                    it.copy(isSelected = it == action.facility)
                                }
                                currentState = currentState.copy(
                                    allFacilityList = updatedAllFacilities,
                                    filteredList = updatedAllFacilities
                                )
                            }

                            else -> {}
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Step 1: Select Facility 1
        composeTestRule
            .onNodeWithText("Facility 1")
            .performClick()

        composeTestRule.waitForIdle()

        // Step 2: Select Facility 2
        composeTestRule
            .onNodeWithText("Facility 2")
            .performClick()

        composeTestRule.waitForIdle()

        // Assert
        assert(capturedActions.size >= 2) {
            "Expected at least 2 actions but got ${capturedActions.size}"
        }

        // Verify that only Facility 2 is selected in the final state
        val selectedFacilities = currentState.allFacilityList.filter { it.isSelected }
        assert(selectedFacilities.size == 1) {
            "Expected exactly 1 selected facility but got ${selectedFacilities.size}: $selectedFacilities"
        }

        assert(selectedFacilities.first().facilityName == "Facility 2") {
            "Expected 'Facility 2' to be selected but got '${selectedFacilities.first().facilityName}'"
        }
    }

    @Test
    fun testSearchAndSelectFromFilteredResults() {
        // Arrange
        var capturedNavigation: SelectFacilityNavigationAction? = null
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = getFacility(),
                filteredList = getFacility(),
                searchText = ""
            )
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        when (action) {
                            is SelectFacilityViewModelAction.OnSearchFacilityAction -> {
                                val filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                                currentState = currentState.copy(
                                    searchText = action.searchText,
                                    filteredList = filteredList
                                )
                            }

                            is SelectFacilityViewModelAction.OnFacilitySelectAction -> {
                                // Simulate selection logic
                                val updatedAllFacilities = currentState.allFacilityList.map {
                                    it.copy(isSelected = it == action.facility)
                                }
                                currentState = currentState.copy(
                                    allFacilityList = updatedAllFacilities
                                )
                            }

                            else -> {}
                        }
                    },
                    selectFacilityNavigation = { navigation ->
                        capturedNavigation = navigation
                    }
                )
            }
        }

        // Step 1: Search for "5" (should show Facility 5)
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("5")

        composeTestRule.waitForIdle()

        // Step 2: Verify Facility 5 is displayed and click it
        composeTestRule
            .onNodeWithText("Facility 5")
            .assertIsDisplayed()
            .performClick()

        composeTestRule.waitForIdle()

        // Assert
        assert(capturedNavigation is SelectFacilityNavigationAction.NavigateBackWithFacility) {
            "Expected NavigateBackWithFacility but got: $capturedNavigation"
        }

        val navigationAction = capturedNavigation as SelectFacilityNavigationAction.NavigateBackWithFacility
        assert(navigationAction.facility.facilityName == "Facility 5") {
            "Expected navigation facility to be 'Facility 5' but was '${navigationAction.facility.facilityName}'"
        }
    }
}
