package com.noteactive.app.features.login.presentation.screens

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.features.MainActivity
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Simple End-to-End UI Tests for Login Feature Flow with Real API Integration
 * 
 * This test class provides a simplified approach to testing the login flow
 * without requiring Hilt dependencies. It tests the complete user journey
 * using real API calls and validates the UI behavior.
 * 
 * Key Features:
 * - No Hilt dependency required
 * - Tests with real API calls to your backend
 * - Validates complete user journey
 * - Simple and maintainable test structure
 * - Real network response handling
 * 
 * Prerequisites:
 * - Android device or emulator with API 24+
 * - Network connectivity to your backend services
 * - Valid test activation keys in your backend
 * - Your app must be properly configured for testing
 */
@RunWith(AndroidJUnit4::class)
class LoginFlowSimpleTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    /**
     * Test Case: Complete Happy Path Login Flow with Real API
     * 
     * This test validates the entire login flow using real API calls:
     * 1. Enter valid activation key that exists in your backend
     * 2. Wait for real API response and navigate to activation details
     * 3. Review activation details and continue to login
     * 4. Select customer and enter PIN
     * 5. Navigate to facility selection
     * 6. Select facility and complete login
     */
    @Test
    fun completeLoginFlow_withRealApi_navigatesSuccessfully() {
        // Step 1: Enter Activation Key (using real key from your codebase)
        composeTestRule
            .onNodeWithText("Enter Key")
            .performTextInput("hpdemo")  // Real activation key from your backend
        
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        // Step 2: Wait for real API response and navigation
        composeTestRule.waitForIdle()
        
        // Wait for navigation to activation details (with longer timeout for real API)
        composeTestRule.waitUntil(timeoutMillis = 15000) {
            try {
                composeTestRule
                    .onNodeWithText("Activation Details")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        // Verify activation details screen is displayed
        composeTestRule
            .onNodeWithText("Activation Details")
            .assertIsDisplayed()
        
        // Step 3: Continue from activation details
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        // Wait for navigation to login screen
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onNodeWithText("Select Customer")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        // Step 4: Login Screen - Select Customer and Enter PIN
        composeTestRule
            .onNodeWithText("Select Customer")
            .assertIsDisplayed()
        
        // Click on customer selection
        composeTestRule
            .onNodeWithText("Select Customer")
            .performClick()
        
        // Wait for customer list to load and select first available customer
        composeTestRule.waitForIdle()
        
        // Select first customer from the list (this will be real customer data)
        try {
            composeTestRule
                .onAllNodesWithTag("customer_item")
                .onFirst()
                .performClick()
        } catch (e: Exception) {
            // Fallback: try to find any customer text and click it
            composeTestRule
                .onNodeWithText("Customer")
                .performClick()
        }
        
        // Enter PIN
        composeTestRule
            .onNodeWithText("Enter PIN")
            .performTextInput("1234")
        
        // Click Login
        composeTestRule
            .onNodeWithText("Login")
            .performClick()
        
        // Step 5: Wait for navigation to facility selection
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onNodeWithText("Select Facility")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        // Step 6: Select Facility
        composeTestRule
            .onNodeWithText("Select Facility")
            .assertIsDisplayed()
        
        // Wait for facility list to load and select first available facility
        composeTestRule.waitForIdle()
        
        // Select first facility from the list (this will be real facility data)
        try {
            composeTestRule
                .onAllNodesWithTag("facility_item")
                .onFirst()
                .performClick()
        } catch (e: Exception) {
            // Fallback: try to find any facility text and click it
            composeTestRule
                .onNodeWithText("Facility")
                .performClick()
        }
        
        // Step 7: Verify navigation to main app
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onNodeWithText("Notes")  // Main app indicator
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        // Verify we've reached the main app
        composeTestRule
            .onNodeWithText("Notes")
            .assertIsDisplayed()
    }

    /**
     * Test Case: Invalid Activation Key Error Handling
     * 
     * This test validates error handling with real API error responses:
     * 1. Enter invalid activation key
     * 2. Verify real API error response
     * 3. Test error recovery with valid key
     */
    @Test
    fun loginFlow_invalidActivationKey_handlesRealApiError() {
        // Step 1: Enter invalid activation key
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextInput("invalid-key-that-does-not-exist")
        
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        // Step 2: Wait for error response from real API
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onNodeWithText("Invalid activation key")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        // Verify error message is displayed
        composeTestRule
            .onNodeWithText("Invalid activation key")
            .assertIsDisplayed()
        
        // Step 3: Test recovery with valid key
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextClearance()
        
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextInput("hpdemo")  // Valid key
        
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        // Verify successful navigation after recovery
        composeTestRule.waitUntil(timeoutMillis = 15000) {
            try {
                composeTestRule
                    .onNodeWithText("Activation Details")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        composeTestRule
            .onNodeWithText("Activation Details")
            .assertIsDisplayed()
    }

    /**
     * Test Case: Empty Activation Key Validation
     * 
     * This test validates client-side validation before API calls:
     * 1. Verify continue button is disabled with empty input
     * 2. Verify button becomes enabled with valid input
     */
    @Test
    fun loginFlow_emptyActivationKey_disablesContinueButton() {
        // Verify continue button is disabled initially
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsNotEnabled()
        
        // Enter text and verify button becomes enabled
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextInput("test")
        
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsEnabled()
        
        // Clear text and verify button becomes disabled again
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextClearance()
        
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsNotEnabled()
    }

    /**
     * Test Case: Real API Performance Validation
     * 
     * This test measures the performance of real API calls:
     * 1. Measure activation key API response time
     * 2. Verify response time meets requirements
     */
    @Test
    fun loginFlow_realApiPerformance_meetsRequirements() {
        val startTime = System.currentTimeMillis()
        
        // Enter activation key and trigger API call
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextInput("hpdemo")
        
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        // Wait for API response
        composeTestRule.waitUntil(timeoutMillis = 15000) {
            try {
                composeTestRule
                    .onNodeWithText("Activation Details")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        val responseTime = System.currentTimeMillis() - startTime
        
        // Verify API response time is acceptable (less than 10 seconds)
        assert(responseTime <= 10000) {
            "API response took ${responseTime}ms, expected <= 10000ms"
        }
        
        // Log performance for monitoring
        println("Activation API response time: ${responseTime}ms")
    }
}
