package com.noteactive.app.features.login.presentation.screens.enter_activation_key

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyApiState
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyState
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class EnterActivationKeyScreenAccessibilityTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val defaultState = ActivationKeyState(
        text = "",
        isLoading = false,
        isError = false,
        isButtonEnabled = false
    )

    @Test
    fun enterActivationKeyScreen_hasProperSemantics() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Check that important UI elements have proper semantics
        composeTestRule.onNodeWithText("Login").assertIsDisplayed()
        composeTestRule.onNodeWithText("Enter Key").assertIsDisplayed()
        composeTestRule.onNodeWithText("Continue").assertIsDisplayed()
        
        // Verify text field is accessible
        composeTestRule.onNode(hasText("Enter Key") and hasSetTextAction()).assertExists()
        
        // Verify button is accessible
        composeTestRule.onNode(hasText("Continue") and hasClickAction()).assertExists()
    }

    @Test
    fun enterActivationKeyScreen_textFieldIsAccessibleForScreenReaders() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Text field should be focusable and have proper semantics
        composeTestRule.onNodeWithText("Enter Key")
            .assertIsDisplayed()
            .assertHasClickAction()
            .assertIsEnabled()
    }

    @Test
    fun enterActivationKeyScreen_buttonStateIsAccessible() {
        // Test disabled state
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        composeTestRule.onNodeWithText("Continue")
            .assertIsDisplayed()
            .assertIsNotEnabled()

        // Test enabled state
        val enabledState = defaultState.copy(text = "test", isButtonEnabled = true)
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = enabledState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        composeTestRule.onNodeWithText("Continue")
            .assertIsDisplayed()
            .assertIsEnabled()
            .assertHasClickAction()
    }

    @Test
    fun enterActivationKeyScreen_errorStateIsAccessible() {
        // Arrange
        val errorState = ActivationKeyState(
            text = "invalid",
            isLoading = false,
            isError = true,
            isButtonEnabled = true
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = errorState,
                    activationApiState = ActivationKeyApiState.Error("Invalid key"),
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Error message should be accessible
        composeTestRule.onNodeWithText("Please enter valid key")
            .assertIsDisplayed()
    }

    @Test
    fun enterActivationKeyScreen_supportsKeyboardNavigation() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Text field should support keyboard input
        composeTestRule.onNodeWithText("Enter Key")
            .assertIsDisplayed()
            .performTextInput("test-key")

        // Verify text was entered
        composeTestRule.onNodeWithText("test-key").assertExists()
    }

    @Test
    fun enterActivationKeyScreen_hasProperFocusOrder() {
        // Arrange
        val stateWithText = defaultState.copy(text = "test", isButtonEnabled = true)

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = stateWithText,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Should be able to navigate between focusable elements
        // Text field should be focusable
        composeTestRule.onNodeWithText("Enter Key")
            .assertIsDisplayed()
            .requestFocus()

        // Button should be focusable
        composeTestRule.onNodeWithText("Continue")
            .assertIsDisplayed()
            .requestFocus()
    }

    @Test
    fun enterActivationKeyScreen_loadingStateIsAccessible() {
        // Arrange
        val loadingState = ActivationKeyState(
            text = "test-key",
            isLoading = true,
            isError = false,
            isButtonEnabled = false
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = loadingState,
                    activationApiState = ActivationKeyApiState.Loading,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - During loading, button should be disabled
        composeTestRule.onNodeWithText("Continue")
            .assertIsDisplayed()
            .assertIsNotEnabled()
    }

    @Test
    fun enterActivationKeyScreen_textFieldSupportsTextSelection() {
        // Arrange
        val stateWithText = defaultState.copy(text = "test-activation-key")

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = stateWithText,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Text field should display the text and support selection
        composeTestRule.onNodeWithText("test-activation-key")
            .assertIsDisplayed()
            .assertTextEquals("test-activation-key")
    }

    @Test
    fun enterActivationKeyScreen_semanticsAreProperlySet() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Check semantic properties
        composeTestRule.onRoot().printToLog("SEMANTICS")
        
        // Verify that all interactive elements have proper semantics
        composeTestRule.onAllNodes(hasClickAction()).assertCountEquals(1) // Only the Continue button
        composeTestRule.onAllNodes(hasSetTextAction()).assertCountEquals(1) // Only the text field
    }

    @Test
    fun enterActivationKeyScreen_titleHasProperSemantics() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Title should be accessible as heading
        composeTestRule.onNodeWithText("Login")
            .assertIsDisplayed()
    }

    @Test
    fun enterActivationKeyScreen_errorMessageHasProperRole() {
        // Arrange
        val errorState = ActivationKeyState(
            text = "invalid",
            isError = true,
            isButtonEnabled = true
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = errorState,
                    activationApiState = ActivationKeyApiState.Error("Invalid activation key"),
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Error message should be properly announced
        composeTestRule.onNodeWithText("Please enter valid key")
            .assertIsDisplayed()
    }

    @Test
    fun enterActivationKeyScreen_allTextIsReadable() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - All text elements should be readable
        val textElements = listOf("Login", "Enter Key", "Continue")
        textElements.forEach { text ->
            composeTestRule.onNodeWithText(text)
                .assertIsDisplayed()
        }
    }

    @Test
    fun enterActivationKeyScreen_interactiveElementsHaveMinimumTouchTarget() {
        // Arrange & Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = defaultState.copy(text = "test", isButtonEnabled = true),
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Interactive elements should have adequate touch targets
        // Text field should be touchable
        composeTestRule.onNodeWithText("Enter Key")
            .assertIsDisplayed()
            .assertHasClickAction()

        // Button should be touchable
        composeTestRule.onNodeWithText("Continue")
            .assertIsDisplayed()
            .assertHasClickAction()
    }

    @Test
    fun enterActivationKeyScreen_stateChangesAreAnnounced() {
        // Arrange
        val errorState = ActivationKeyState(
            text = "test",
            isError = true,
            isButtonEnabled = true
        )

        // Act
        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = errorState,
                    activationApiState = ActivationKeyApiState.Error("Network error"),
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Error state should be announced
        composeTestRule.onNodeWithText("Please enter valid key")
            .assertIsDisplayed()

        // Test loading state announcement
        val loadingState = ActivationKeyState(
            text = "test",
            isLoading = true,
            isButtonEnabled = false
        )

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = loadingState,
                    activationApiState = ActivationKeyApiState.Loading,
                    onViewmodelAction = {},
                    onNavigation = {}
                )
            }
        }

        // Assert - Loading state should be reflected in UI
        composeTestRule.onNodeWithText("Continue")
            .assertIsNotEnabled()
    }
}
