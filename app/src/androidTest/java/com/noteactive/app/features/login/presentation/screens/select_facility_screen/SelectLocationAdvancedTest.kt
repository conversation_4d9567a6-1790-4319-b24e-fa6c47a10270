package com.noteactive.app.features.login.presentation.screens.select_facility_screen

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.Facility
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.SelectFacilityState
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class SelectLocationAdvancedTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun testSearchWithSpecialCharacters() {
        val facilitiesWithSpecialChars = listOf(
            Facility("Facility-1", false),
            Facility("Facility_2", false),
            Facility("Facility 3", false),
            Facility("Facility@4", false)
        )

        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = facilitiesWithSpecialChars,
                filteredList = facilitiesWithSpecialChars,
                searchText = ""
            )
        )

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Search for facilities with special characters
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("-")

        composeTestRule.waitForIdle()

        composeTestRule.onNodeWithText("Facility-1").assertIsDisplayed()
        composeTestRule.onNodeWithText("Facility_2").assertDoesNotExist()
    }

    @Test
    fun testLargeFacilityList() {
        val largeFacilityList = (1..100).map { 
            Facility("Facility $it", false) 
        }

        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = largeFacilityList,
                filteredList = largeFacilityList,
                searchText = ""
            )
        )

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Search should work efficiently with large lists
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("50")

        composeTestRule.waitForIdle()

        composeTestRule.onNodeWithText("Facility 50").assertIsDisplayed()
        assert(currentState.filteredList.size == 1)
    }

    @Test
    fun testEmptyFacilityList() {
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = emptyList(),
                filteredList = emptyList(),
                searchText = ""
            )
        )

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(searchText = action.searchText)
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // UI should still be functional with empty list
        composeTestRule.onNodeWithText("Select Facility").assertIsDisplayed()
        composeTestRule.onNodeWithText("Search Facility").assertIsDisplayed()

        // Search should work even with empty list
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("test")

        composeTestRule.waitForIdle()
        assert(currentState.searchText == "test")
    }

    @Test
    fun testRapidSearchUpdates() {
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = (1..20).map { Facility("Facility $it", false) },
                filteredList = (1..20).map { Facility("Facility $it", false) },
                searchText = ""
            )
        )

        var actionCount = 0

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            actionCount++
                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Perform rapid search updates
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("1")

        composeTestRule.waitForIdle()

        composeTestRule
            .onNodeWithText("1")
            .performTextInput("0")

        composeTestRule.waitForIdle()

        // Should handle rapid updates correctly
        assert(currentState.searchText == "10")
        composeTestRule.onNodeWithText("Facility 10").assertIsDisplayed()
        assert(actionCount >= 2) // Should have captured multiple search actions
    }

    @Test
    fun testSelectionPersistsThroughSearch() {
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = (1..10).map { Facility("Facility $it", false) },
                filteredList = (1..10).map { Facility("Facility $it", false) },
                searchText = ""
            )
        )

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        when (action) {
                            is SelectFacilityViewModelAction.OnSearchFacilityAction -> {
                                val filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                                currentState = currentState.copy(
                                    searchText = action.searchText,
                                    filteredList = filteredList
                                )
                            }

                            is SelectFacilityViewModelAction.OnFacilitySelectAction -> {
                                val updatedAllFacilities = currentState.allFacilityList.map {
                                    it.copy(isSelected = it == action.facility)
                                }
                                val updatedFilteredList = updatedAllFacilities.filter {
                                    it.facilityName.contains(
                                        currentState.searchText,
                                        ignoreCase = true
                                    )
                                }
                                currentState = currentState.copy(
                                    allFacilityList = updatedAllFacilities,
                                    filteredList = updatedFilteredList
                                )
                            }

                            else -> {}
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Select a facility first
        composeTestRule.onNodeWithText("Facility 5").performClick()
        composeTestRule.waitForIdle()

        // Then search for it
        composeTestRule
            .onNodeWithText("Search Facility")
            .performTextInput("5")

        composeTestRule.waitForIdle()

        // Selection should persist
        val selectedFacilities = currentState.allFacilityList.filter { it.isSelected }
        assert(selectedFacilities.size == 1)
        assert(selectedFacilities.first().facilityName == "Facility 5")
    }

    @Test
    fun testBackButtonBehavior() {
        // This test would verify back button behavior if implemented
        // Currently the screen doesn't seem to handle back button in the toolbar
        var currentState by mutableStateOf(
            SelectFacilityState(
                allFacilityList = (1..5).map { Facility("Facility $it", false) },
                filteredList = (1..5).map { Facility("Facility $it", false) },
                searchText = ""
            )
        )

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Verify toolbar is displayed (back button would be part of AppCommonToolbar)
        composeTestRule.onNodeWithText("Select Facility").assertIsDisplayed()
    }
}
