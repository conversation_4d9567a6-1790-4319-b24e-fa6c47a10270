package com.noteactive.app.features.login.presentation.screens.utils

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.test.ext.junit.rules.ActivityScenarioRule
import com.noteactive.app.features.MainActivity
import com.noteactive.app.features.login.presentation.screens.data.LoginFlowTestData
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * Utility Functions for Login Flow End-to-End Tests
 * 
 * This object provides common utility functions and helpers for testing
 * the login flow. It includes navigation helpers, assertion utilities,
 * mock data setup, and performance testing tools.
 * 
 * Key Features:
 * - Navigation verification utilities
 * - Common assertion helpers
 * - Mock data setup functions
 * - Performance measurement tools
 * - Error simulation utilities
 * - Accessibility testing helpers
 */
object LoginFlowTestUtils {

    /**
     * Wait for a specific UI element to appear with timeout
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.waitForElement(
        text: String,
        timeoutMs: Long = 5000L
    ): Boolean {
        return try {
            this.waitUntil(timeoutMs) {
                try {
                    this.onNodeWithText(text).assertExists()
                    true
                } catch (e: AssertionError) {
                    false
                }
            }
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Wait for navigation to complete by checking for specific screen elements
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.waitForNavigation(
        targetScreenIndicator: String,
        timeoutMs: Long = 3000L
    ): Boolean {
        this.waitForIdle()
        return waitForElement(targetScreenIndicator, timeoutMs)
    }

    /**
     * Simulate network delay for realistic testing
     */
    fun simulateNetworkDelay(delayMs: Long = LoginFlowTestData.NetworkSimulation.NETWORK_DELAY_MS) {
        runBlocking {
            delay(delayMs)
        }
    }

    /**
     * Verify that an error message is displayed and then dismissed
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyErrorMessageFlow(
        errorMessage: String,
        dismissAfterMs: Long = 3000L
    ) {
        // Verify error appears
        this.onNodeWithText(errorMessage).assertIsDisplayed()
        
        // Wait for auto-dismiss or manual dismiss
        runBlocking { delay(dismissAfterMs) }
        
        // Verify error is dismissed
        this.onNodeWithText(errorMessage).assertDoesNotExist()
    }

    /**
     * Perform a complete login flow with given parameters
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.performCompleteLoginFlow(
        activationKey: String = LoginFlowTestData.ValidActivationKeys.DEMO_KEY,
        customerName: String = LoginFlowTestData.MockCustomers.customers[0],
        pin: String = LoginFlowTestData.ValidPins.NUMERIC_PIN,
        facilityName: String = LoginFlowTestData.MockFacilities.facilities[0].facility
    ) {
        // Step 1: Enter activation key
        this.onNodeWithText("Enter Activation Key").performTextInput(activationKey)
        this.onNodeWithText("Continue").performClick()
        this.waitForNavigation("Activation Details")

        // Step 2: Continue from activation details
        this.onNodeWithText("Continue").performClick()
        this.waitForNavigation("Select Customer")

        // Step 3: Select customer and enter PIN
        this.onNodeWithText("Select Customer").performClick()
        this.onNodeWithText(customerName).performClick()
        this.onNodeWithText("Enter PIN").performTextInput(pin)
        this.onNodeWithText("Login").performClick()
        this.waitForNavigation("Select Facility")

        // Step 4: Select facility
        this.onNodeWithText(facilityName).performClick()
        this.waitForNavigation("Notes") // Main app indicator
    }

    /**
     * Verify accessibility compliance for a screen
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyAccessibilityCompliance(
        screenElements: List<String>
    ) {
        screenElements.forEach { element ->
            this.onNodeWithText(element)
                .assertIsDisplayed()
                .assertHasClickAction()
        }
    }

    /**
     * Test keyboard navigation between elements
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.testKeyboardNavigation(
        elements: List<String>
    ) {
        elements.forEach { element ->
            this.onNodeWithText(element)
                .requestFocus()
                .assertIsFocused()
        }
    }

    /**
     * Measure performance of an operation
     */
    fun measurePerformance(operation: () -> Unit): Long {
        val startTime = System.currentTimeMillis()
        operation()
        return System.currentTimeMillis() - startTime
    }

    /**
     * Verify that an operation completes within expected time
     */
    fun verifyPerformance(
        expectedMaxTimeMs: Long,
        operation: () -> Unit
    ): Boolean {
        val actualTime = measurePerformance(operation)
        return actualTime <= expectedMaxTimeMs
    }

    /**
     * Simulate rapid button clicking to test debouncing
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.testRapidClicking(
        buttonText: String,
        clickCount: Int = 5
    ) {
        repeat(clickCount) {
            this.onNodeWithText(buttonText).performClick()
        }
        this.waitForIdle()
    }

    /**
     * Test search functionality with various inputs
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.testSearchFunctionality(
        searchFieldText: String,
        searchTerms: List<String>,
        expectedResults: List<String>
    ) {
        searchTerms.forEachIndexed { index, searchTerm ->
            // Clear previous search
            this.onNodeWithText(searchFieldText).performTextClearance()
            
            // Enter search term
            this.onNodeWithText(searchFieldText).performTextInput(searchTerm)
            this.waitForIdle()
            
            // Verify expected result
            if (index < expectedResults.size) {
                this.onNodeWithText(expectedResults[index]).assertIsDisplayed()
            }
        }
    }

    /**
     * Verify loading state transitions
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyLoadingStateTransition(
        triggerAction: () -> Unit,
        loadingIndicator: String = "Loading",
        successIndicator: String
    ) {
        // Trigger the action that causes loading
        triggerAction()
        
        // Verify loading state appears
        this.onNodeWithText(loadingIndicator).assertIsDisplayed()
        
        // Wait for loading to complete
        this.waitUntil(timeoutMs = 10000) {
            try {
                this.onNodeWithText(successIndicator).assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        // Verify loading state is gone
        this.onNodeWithText(loadingIndicator).assertDoesNotExist()
        
        // Verify success state
        this.onNodeWithText(successIndicator).assertIsDisplayed()
    }

    /**
     * Test form validation with various inputs
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.testFormValidation(
        fieldText: String,
        validInputs: List<String>,
        invalidInputs: List<String>,
        submitButtonText: String
    ) {
        // Test valid inputs
        validInputs.forEach { input ->
            this.onNodeWithText(fieldText).performTextClearance()
            this.onNodeWithText(fieldText).performTextInput(input)
            this.onNodeWithText(submitButtonText).assertIsEnabled()
        }
        
        // Test invalid inputs
        invalidInputs.forEach { input ->
            this.onNodeWithText(fieldText).performTextClearance()
            this.onNodeWithText(fieldText).performTextInput(input)
            this.onNodeWithText(submitButtonText).assertIsNotEnabled()
        }
    }

    /**
     * Verify error recovery flow
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyErrorRecovery(
        triggerErrorAction: () -> Unit,
        errorMessage: String,
        recoveryAction: () -> Unit,
        successIndicator: String
    ) {
        // Trigger error
        triggerErrorAction()
        
        // Verify error is displayed
        this.onNodeWithText(errorMessage).assertIsDisplayed()
        
        // Perform recovery action
        recoveryAction()
        
        // Verify error is gone and success is achieved
        this.onNodeWithText(errorMessage).assertDoesNotExist()
        this.onNodeWithText(successIndicator).assertIsDisplayed()
    }

    /**
     * Test orientation changes (if supported)
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.testOrientationChange(
        verifyElementsAfterRotation: List<String>
    ) {
        // This would require additional setup for orientation testing
        // Implementation depends on your test configuration
        
        verifyElementsAfterRotation.forEach { element ->
            this.onNodeWithText(element).assertIsDisplayed()
        }
    }

    /**
     * Verify deep link navigation
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyDeepLinkNavigation(
        deepLinkUrl: String,
        expectedScreenIndicator: String
    ) {
        // This would require deep link testing setup
        // Implementation depends on your navigation configuration
        
        this.waitForNavigation(expectedScreenIndicator)
    }

    /**
     * Test memory usage during navigation
     */
    fun testMemoryUsage(operation: () -> Unit): Long {
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        operation()
        
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()
        return finalMemory - initialMemory
    }

    /**
     * Verify that UI elements are properly cleaned up
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyUICleanup(
        elementsToVerifyGone: List<String>
    ) {
        elementsToVerifyGone.forEach { element ->
            this.onNodeWithText(element).assertDoesNotExist()
        }
    }

    /**
     * Test concurrent user interactions
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.testConcurrentInteractions(
        interactions: List<() -> Unit>
    ) {
        interactions.forEach { interaction ->
            interaction()
            this.waitForIdle()
        }
    }

    /**
     * Verify state persistence across app lifecycle events
     */
    fun AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>.verifyStatePersistence(
        stateToVerify: Map<String, String>
    ) {
        stateToVerify.forEach { (key, value) ->
            this.onNodeWithText(value).assertIsDisplayed()
        }
    }
}
