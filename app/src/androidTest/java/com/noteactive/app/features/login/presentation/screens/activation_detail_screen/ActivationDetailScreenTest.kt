package com.noteactive.app.features.login.presentation.screens.activation_detail_screen

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextReplacement
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsNavigationAction
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.action.ActivationDetailsViewModelAction
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetailsState
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.getActivationDetailedInfo
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

//@RunWith(AndroidJUnit4::class)
//class ActivationDetailScreenTest {
//
//    @get:Rule
//    val composeTestRule = createComposeRule()
//
//    @Test
//    fun testActivationDetailScreenDisplaysCorrectly() {
//        // Arrange
////        val testState = ActivationDetailsState(activationDetails = getActivationDetailedInfo())
////
////        // Act
////        composeTestRule.setContent {
////            MyAppTheme {
////                ActivationDetailScreen(
////                    activationKeyState = testState,
////                    action = { },
////                    navigationAction = { }
////                )
////            }
////        }
//
//        // Assert - Verify screen title
//        composeTestRule
//            .onNodeWithText("Activation Details")
//            .assertIsDisplayed()
//
//        // Assert - Verify buttons
//        composeTestRule
//            .onNodeWithText("Activate")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("Close")
//            .assertIsDisplayed()
//
//        // Assert - Verify form fields from getFakeList()
//        composeTestRule
//            .onNodeWithText("Client name")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("Facility name")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("Password")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("User name")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("Email")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("Mobile number")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("Location")
//            .assertIsDisplayed()
//    }
//
//    @Test
//    fun testEditableFieldsCanBeModified() {
//        // Arrange
//        var capturedAction: ActivationDetailsViewModelAction? = null
//        var currentState by mutableStateOf(ActivationDetailsState(activationDetails = getActivationDetailedInfo()))
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = currentState,
//                    action = { action ->
//                        capturedAction = action
//                        when (action) {
//                            is ActivationDetailsViewModelAction.OnInfoChanged -> {
//                                val updatedList = currentState.activationDetails.map {
//                                    if (it.key == action.activationDetails.key) {
//                                        it.copy(text = action.activationDetails.text)
//                                    } else {
//                                        it
//                                    }
//                                }
//                                currentState = currentState.copy(activationDetails = updatedList)
//                            }
//                        }
//                    },
//                    navigationAction = { }
//                )
//            }
//        }
//
//        // Find an editable field (Facility name) and modify it
//        // Use performTextReplacement instead of clearance + input to avoid node tree issues
//        composeTestRule
//            .onNodeWithText("Dorm 1")
//            .assertIsDisplayed()
//            .performTextReplacement("New Facility Name")
//
//        composeTestRule.waitForIdle()
//
//        // Assert that the action was captured
//        assert(capturedAction is ActivationDetailsViewModelAction.OnInfoChanged) {
//            "Expected OnInfoChanged action but got: $capturedAction"
//        }
//
//        // Verify the text was updated in the state
//        val updatedFacilityDetail = currentState.activationDetails.find { it.key == "facility_name" }
//        assert(updatedFacilityDetail?.text == "New Facility Name") {
//            "Expected facility name to be updated to 'New Facility Name' but was '${updatedFacilityDetail?.text}'"
//        }
//    }
//
//    @Test
//    fun testNonEditableFieldsCannotBeModified() {
//        // Arrange
//        val testState = ActivationDetailsState(activationDetails = getActivationDetailedInfo())
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = testState,
//                    action = { },
//                    navigationAction = { }
//                )
//            }
//        }
//
//        // Try to interact with non-editable fields (Client name and Email are not editable)
//        // Note: In Compose, disabled text fields don't respond to text input
//        // We can verify they exist but we can't easily test that they're disabled
//        // without more specific test tags or accessibility properties
//
//        composeTestRule
//            .onNodeWithText("LPSO") // Client name value
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("<EMAIL>") // Email value
//            .assertIsDisplayed()
//    }
//
//    @Test
//    fun testActivateButtonTriggersCorrectNavigation() {
//        // Arrange
//        var capturedNavigation: ActivationDetailsNavigationAction? = null
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = ActivationDetailsState(activationDetails = getActivationDetailedInfo()),
//                    action = { },
//                    navigationAction = { navigation ->
//                        capturedNavigation = navigation
//                    }
//                )
//            }
//        }
//
//        // Click the Activate button
//        composeTestRule
//            .onNodeWithText("Activate")
//            .assertIsDisplayed()
//            .performClick()
//
//        composeTestRule.waitForIdle()
//
//        // Assert that the correct navigation action was triggered
//        assert(capturedNavigation == ActivationDetailsNavigationAction.GoToLoginScreen) {
//            "Expected GoToLoginScreen navigation but got: $capturedNavigation"
//        }
//    }
//
//    @Test
//    fun testCloseButtonTriggersCorrectNavigation() {
//        // Arrange
//        var capturedNavigation: ActivationDetailsNavigationAction? = null
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = ActivationDetailsState(activationDetails = getActivationDetailedInfo()),
//                    action = { },
//                    navigationAction = { navigation ->
//                        capturedNavigation = navigation
//                    }
//                )
//            }
//        }
//
//        // Click the Close button
//        composeTestRule
//            .onNodeWithText("Close")
//            .assertIsDisplayed()
//            .performClick()
//
//        composeTestRule.waitForIdle()
//
//        // Assert that the correct navigation action was triggered
//        assert(capturedNavigation == ActivationDetailsNavigationAction.OnDismiss) {
//            "Expected OnDismiss navigation but got: $capturedNavigation"
//        }
//    }
//
//    @Test
//    fun testBackButtonTriggersCorrectNavigation() {
//        // Arrange
//        var EcapturedNavigation: ActivationDetailsNavigationAction? = null
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = ActivationDetailsState(activationDetails = getActivationDetailedInfo()),
//                    action = { },
//                    navigationAction = { navigation ->
//                        capturedNavigation = navigation
//                    }
//                )
//            }
//        }
//
//        // Note: The back button in AppCommonToolbar might not be easily testable
//        // without specific test tags. This test demonstrates the structure
//        // but may need adjustment based on the actual toolbar implementation.
//
//        // For now, we can verify the toolbar title is displayed
//        composeTestRule
//            .onNodeWithText("Activation Details")
//            .assertIsDisplayed()
//
//        // In a real scenario, you would need to find the back button by test tag
//        // or accessibility properties and click it to test the navigation
//    }
//
//    @Test
//    fun testPasswordFieldDisplaysCorrectly() {
//        // Arrange
//        val testState = ActivationDetailsState(activationDetails = getActivationDetailedInfo())
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = testState,
//                    action = { },
//                    navigationAction = { }
//                )
//            }
//        }
//
//        // Assert - Verify password field exists
//        composeTestRule
//            .onNodeWithText("Password")
//            .assertIsDisplayed()
//    }
//
//    @Test
//    fun testEmailFieldDisplaysCorrectly() {
//        // Arrange
//        val testState = ActivationDetailsState(activationDetails = getActivationDetailedInfo())
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = testState,
//                    action = { },
//                    navigationAction = { }
//                )
//            }
//        }
//
//        // Assert - Verify email field and its value
//        composeTestRule
//            .onNodeWithText("Email")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("<EMAIL>")
//            .assertIsDisplayed()
//    }
//
//    @Test
//    fun testMobileNumberFieldDisplaysCorrectly() {
//        // Arrange
//        val testState = ActivationDetailsState(activationDetails = getActivationDetailedInfo())
//
//        // Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationDetailScreen(
//                    activationKeyState = testState,
//                    action = { },
//                    navigationAction = { }
//                )
//            }
//        }
//
//        // Assert - Verify mobile number field and its value
//        composeTestRule
//            .onNodeWithText("Mobile number")
//            .assertIsDisplayed()
//
//        composeTestRule
//            .onNodeWithText("974526678")
//            .assertIsDisplayed()
//    }
//}
