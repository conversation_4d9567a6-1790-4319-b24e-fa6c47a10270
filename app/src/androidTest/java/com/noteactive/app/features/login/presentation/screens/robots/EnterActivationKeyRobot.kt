package com.noteactive.app.features.login.presentation.screens.robots

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.test.ext.junit.rules.ActivityScenarioRule
import com.noteactive.app.features.MainActivity

/**
 * Robot Pattern Implementation for EnterActivationKeyScreen
 * 
 * This robot provides a fluent API for testing the EnterActivationKeyScreen.
 * It encapsulates all UI interactions and assertions for this screen,
 * making tests more readable and maintainable.
 * 
 * Key Features:
 * - Fluent API with method chaining
 * - Comprehensive UI element verification
 * - API call simulation and verification
 * - Navigation validation
 * - Error handling testing
 * - Accessibility testing support
 * 
 * Usage Example:
 * ```
 * enterActivationKeyRobot
 *     .verifyScreenIsDisplayed()
 *     .enterActivationKey("hpdemo")
 *     .clickContinueButton()
 *     .verifyNavigationToActivationDetail()
 * ```
 */
class EnterActivationKeyRobot(
    private val composeTestRule: AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>
) {

    /**
     * Verify that the EnterActivationKeyScreen is displayed
     */
    fun verifyScreenIsDisplayed(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Login")
            .assertIsDisplayed()
        
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Enter text into the activation key input field
     */
    fun enterActivationKey(activationKey: String): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextInput(activationKey)
        
        return this
    }

    /**
     * Clear the activation key input field
     */
    fun clearActivationKey(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performTextClearance()
        
        return this
    }

    /**
     * Click the continue button
     */
    fun clickContinueButton(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        return this
    }

    /**
     * Verify that the continue button is enabled
     */
    fun verifyContinueButtonIsEnabled(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsEnabled()
        
        return this
    }

    /**
     * Verify that the continue button is disabled
     */
    fun verifyContinueButtonIsDisabled(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsNotEnabled()
        
        return this
    }

    /**
     * Verify that an error message is displayed
     */
    fun verifyErrorIsDisplayed(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Invalid activation key")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that a network error message is displayed
     */
    fun verifyNetworkErrorIsDisplayed(): EnterActivationKeyRobot {
        composeTestRule
            .onNodeWithText("Network error")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that the API call is triggered (loading state)
     */
    fun verifyApiCallIsTriggered(): EnterActivationKeyRobot {
        // Wait for loading indicator or state change
        composeTestRule.waitForIdle()
        
        // You might want to verify loading indicator here
        // This depends on your UI implementation
        
        return this
    }

    /**
     * Verify navigation to ActivationDetailScreen
     */
    fun verifyNavigationToActivationDetail(): EnterActivationKeyRobot {
        composeTestRule.waitForIdle()
        
        // Wait for navigation to complete
        composeTestRule
            .onNodeWithText("Activation Details")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that no navigation occurs (stays on current screen)
     */
    fun verifyNoNavigationOccurs(): EnterActivationKeyRobot {
        composeTestRule.waitForIdle()
        
        // Verify we're still on the same screen
        composeTestRule
            .onNodeWithText("Login")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Simulate network error for testing error handling
     */
    fun simulateNetworkError(): EnterActivationKeyRobot {
        // This would typically involve mocking the network layer
        // Implementation depends on your dependency injection setup
        
        return this
    }

    /**
     * Restore network connectivity after simulating error
     */
    fun restoreNetwork(): EnterActivationKeyRobot {
        // Restore network connectivity
        // Implementation depends on your mocking setup
        
        return this
    }

    /**
     * Verify accessibility labels and descriptions
     */
    fun verifyAccessibilityLabels(): EnterActivationKeyRobot {
        // Verify text field has proper content description
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .assertHasClickAction()
        
        // Verify button has proper accessibility
        composeTestRule
            .onNodeWithText("Continue")
            .assertHasClickAction()
        
        return this
    }

    /**
     * Test keyboard navigation functionality
     */
    fun testKeyboardNavigation(): EnterActivationKeyRobot {
        // Test tab navigation between elements
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .requestFocus()
            .assertIsFocused()
        
        // Test IME action (Done/Next)
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .performImeAction()
        
        return this
    }

    /**
     * Verify that the app logo/branding is displayed
     */
    fun verifyAppLogoIsDisplayed(): EnterActivationKeyRobot {
        // Verify app logo or branding elements
        composeTestRule
            .onNodeWithContentDescription("App Logo")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test input validation with various activation key formats
     */
    fun testInputValidation(): EnterActivationKeyRobot {
        // Test empty input
        clearActivationKey()
        verifyContinueButtonIsDisabled()
        
        // Test valid input
        enterActivationKey("hpdemo")
        verifyContinueButtonIsEnabled()
        
        // Test special characters
        clearActivationKey()
        enterActivationKey("test-key_123")
        verifyContinueButtonIsEnabled()
        
        return this
    }

    /**
     * Verify screen orientation handling
     */
    fun verifyOrientationHandling(): EnterActivationKeyRobot {
        // This would test landscape/portrait orientation changes
        // Implementation depends on your test setup
        
        return this
    }

    /**
     * Test long activation key input
     */
    fun testLongActivationKeyInput(): EnterActivationKeyRobot {
        val longKey = "a".repeat(100)
        enterActivationKey(longKey)
        
        // Verify the field handles long input appropriately
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .assertTextContains(longKey.take(50)) // Assuming some truncation
        
        return this
    }

    /**
     * Verify loading state during API call
     */
    fun verifyLoadingState(): EnterActivationKeyRobot {
        // Check for loading indicator or disabled state
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsNotEnabled()
        
        return this
    }

    /**
     * Wait for real API response and verify success
     */
    fun waitForApiSuccess(): EnterActivationKeyRobot {
        composeTestRule.waitForIdle()

        // Wait for real API call to complete and navigation to occur
        composeTestRule.waitUntil(timeoutMillis = 15000) {  // Longer timeout for real API
            try {
                composeTestRule
                    .onNodeWithText("Activation Details")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }

        return this
    }

    /**
     * Verify real API call is triggered and handle loading state
     */
    fun verifyRealApiCallTriggered(): EnterActivationKeyRobot {
        composeTestRule.waitForIdle()

        // Verify loading state appears (if your UI shows loading)
        // This depends on your actual UI implementation

        return this
    }

    /**
     * Wait for real API error response
     */
    fun waitForApiError(): EnterActivationKeyRobot {
        composeTestRule.waitForIdle()

        // Wait for error state to appear
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onNodeWithText("Invalid activation key")
                    .assertExists()
                true
            } catch (e: AssertionError) {
                false
            }
        }

        return this
    }

    /**
     * Verify error recovery flow
     */
    fun verifyErrorRecovery(): EnterActivationKeyRobot {
        // After error, user should be able to retry
        verifyErrorIsDisplayed()
        clearActivationKey()
        enterActivationKey("hpdemo")
        clickContinueButton()
        verifyNavigationToActivationDetail()
        
        return this
    }
}
