package com.noteactive.app.features.login.presentation.screens.enter_activation_key

import androidx.compose.foundation.text.BasicTextField
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyNavigation
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyViewModelAction
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyApiState
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyState
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class ActivationKeyRootTest {

 @get:Rule
 val composeTestRule = createComposeRule()

 @Test
 fun activationKeyScreen_typingAndSubmit_updatesUiState() {
  // A fake ViewModel update function to capture actions
  var lastAction: EnterActivationKeyViewModelAction? = null

  composeTestRule.setContent {
   ActivationKeyRoot(
    activationKeyState = ActivationKeyState(
     text = "hpdemo",
     isError = false,
     isButtonEnabled = false
    ),
    activationApiState = ActivationKeyApiState.Loading,
    onViewmodelAction = { lastAction = it },
    onNavigation = {}
   )
  }

  // Type an activation key
//  val input = "hpde1mo"
//  composeTestRule.onNodeWithText("Enter Key") // Use hint string from resources!
//   .performTextInput(input)

  // Verify that the viewmodel received the OnTextChanged action
  assert(lastAction is EnterActivationKeyViewModelAction.OnTextChanged)
  assert((lastAction as EnterActivationKeyViewModelAction.OnTextChanged).value == "hpdemo")

  // Check that the button is enabled and click it
  composeTestRule.onNodeWithText("Continue") // Use button string from resources!
   .assertIsEnabled()
   .performClick()

  // Verify that OnSubmit was sent
  assert(lastAction == EnterActivationKeyViewModelAction.OnSubmit)
 }

 @Test
 fun activationKeyScreen_errorState_displaysError() {
  composeTestRule.setContent {
   ActivationKeyRoot(
    activationKeyState = ActivationKeyState(
     text = "hpdemo",
     isError = true,
     isButtonEnabled = true
    ),
    activationApiState = ActivationKeyApiState.Loading,
    onViewmodelAction = {},
    onNavigation = {}
   )
  }

  // The error message should be visible
  composeTestRule.onNodeWithText("Please enter valid key")
   .assertExists()
 }


 @Test
 fun enterActivationKeyScreen_successfulApiCallTriggersNavigation() {
  // Arrange
  var navigationTriggered = false
  var capturedNavigation: EnterActivationKeyNavigation? = null

  val apiState = ActivationKeyState()
  composeTestRule.setContent {
   MyAppTheme {
    ActivationKeyRoot(
     activationKeyState = apiState,
     activationApiState = ActivationKeyApiState.Idle,
     onViewmodelAction = { /* ... */ },
     onNavigation = { /* ... */ }
    )
   }
  }
  composeTestRule.onNodeWithText("Enter Key")
  composeTestRule.onNodeWithTag("Enter Key").performTextInput("afafa")
  composeTestRule.waitForIdle()
  composeTestRule.onNodeWithText("Continue").performClick()
  // Assert
  composeTestRule.waitUntil(timeoutMillis = 5000) { navigationTriggered }

  assert(navigationTriggered)
  assert(capturedNavigation == EnterActivationKeyNavigation.ActivationFormNav)
 }

 @Test
 fun submit_invalidKey_showsErrorAfterApiCall() {
  // 1. Set up the UI
  val apiState = ActivationKeyState()
  composeTestRule.setContent {
   MyAppTheme {
    ActivationKeyRoot(
     activationKeyState = apiState,
     activationApiState = ActivationKeyApiState.Idle,
     onViewmodelAction = { /* ... */ },
     onNavigation = { /* ... */ }
    )
   }
  }
  // 2. ALL ComposeTestRule actions/assertions go OUTSIDE the setContent lambda!
  val invalidKey = "INVALID-KEY-123"
  composeTestRule.onNodeWithText("Enter Key")
  composeTestRule.onNodeWithTag("Enter Key").performTextInput("afafa")
  composeTestRule.waitForIdle()
  composeTestRule.onNodeWithText("Continue").performClick()

  // Wait for error UI to be shown (replace with waitUntil if needed for async)
  composeTestRule.waitUntil(timeoutMillis = 5000) {
   composeTestRule.onAllNodesWithText("Please enter valid key").fetchSemanticsNodes().isNotEmpty()
  }

  // Assert that the error message is displayed
  composeTestRule.onNodeWithText("Please enter valid key").assertIsDisplayed()

 }
}

