package com.noteactive.app.features.login.presentation.screens.robots

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.AndroidComposeTestRule
import androidx.test.ext.junit.rules.ActivityScenarioRule
import com.noteactive.app.features.MainActivity

/**
 * Robot Pattern Implementation for ActivationDetailScreen
 * 
 * This robot provides a fluent API for testing the ActivationDetailScreen.
 * It handles all interactions with the activation details display and
 * navigation to the next screen in the login flow.
 * 
 * Key Features:
 * - Verification of activation details display
 * - Form field interaction testing
 * - Navigation validation
 * - Back button handling
 * - API call verification
 * - Accessibility testing
 * 
 * Usage Example:
 * ```
 * activationDetailRobot
 *     .verifyScreenIsDisplayed()
 *     .verifyActivationDetailsAreShown()
 *     .clickContinueButton()
 *     .verifyNavigationToLoginScreen()
 * ```
 */
class ActivationDetailRobot(
    private val composeTestRule: AndroidComposeTestRule<ActivityScenarioRule<MainActivity>, MainActivity>
) {

    /**
     * Verify that the ActivationDetailScreen is displayed
     */
    fun verifyScreenIsDisplayed(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithText("Activation Details")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that activation details are properly displayed
     */
    fun verifyActivationDetailsAreShown(): ActivationDetailRobot {
        // Verify common activation detail fields
        composeTestRule
            .onNodeWithText("First Name")
            .assertIsDisplayed()
        
        composeTestRule
            .onNodeWithText("Last Name")
            .assertIsDisplayed()
        
        composeTestRule
            .onNodeWithText("Email")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify specific activation detail values
     */
    fun verifyActivationDetailValues(
        firstName: String? = null,
        lastName: String? = null,
        email: String? = null
    ): ActivationDetailRobot {
        firstName?.let {
            composeTestRule
                .onNodeWithText(it)
                .assertIsDisplayed()
        }
        
        lastName?.let {
            composeTestRule
                .onNodeWithText(it)
                .assertIsDisplayed()
        }
        
        email?.let {
            composeTestRule
                .onNodeWithText(it)
                .assertIsDisplayed()
        }
        
        return this
    }

    /**
     * Click the continue button to proceed to login screen
     */
    fun clickContinueButton(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithText("Continue")
            .performClick()
        
        return this
    }

    /**
     * Click the back button to return to previous screen
     */
    fun clickBackButton(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithContentDescription("Back")
            .performClick()
        
        return this
    }

    /**
     * Verify navigation to LoginScreen
     */
    fun verifyNavigationToLoginScreen(): ActivationDetailRobot {
        composeTestRule.waitForIdle()
        
        // Wait for navigation to complete
        composeTestRule
            .onNodeWithText("Select Customer")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify navigation back to EnterActivationKeyScreen
     */
    fun verifyNavigationToActivationKey(): ActivationDetailRobot {
        composeTestRule.waitForIdle()
        
        composeTestRule
            .onNodeWithText("Enter Activation Key")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify that text fields are disabled (read-only)
     */
    fun verifyFieldsAreReadOnly(): ActivationDetailRobot {
        // Verify that activation detail fields are not editable
        composeTestRule
            .onAllNodesWithTag("activation_detail_field")
            .assertAll(isNotEnabled())
        
        return this
    }

    /**
     * Verify the toolbar title and back button
     */
    fun verifyToolbar(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithText("Activation Details")
            .assertIsDisplayed()
        
        composeTestRule
            .onNodeWithContentDescription("Back")
            .assertIsDisplayed()
            .assertHasClickAction()
        
        return this
    }

    /**
     * Verify accessibility labels and descriptions
     */
    fun verifyAccessibilityLabels(): ActivationDetailRobot {
        // Verify toolbar accessibility
        composeTestRule
            .onNodeWithContentDescription("Back")
            .assertHasClickAction()
        
        // Verify continue button accessibility
        composeTestRule
            .onNodeWithText("Continue")
            .assertHasClickAction()
        
        return this
    }

    /**
     * Test keyboard navigation functionality
     */
    fun testKeyboardNavigation(): ActivationDetailRobot {
        // Test focus navigation
        composeTestRule
            .onNodeWithText("Continue")
            .requestFocus()
            .assertIsFocused()
        
        return this
    }

    /**
     * Verify loading state during facility API call
     */
    fun verifyLoadingState(): ActivationDetailRobot {
        // Check for loading indicator
        composeTestRule
            .onNodeWithContentDescription("Loading")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Verify error handling for facility API failure
     */
    fun verifyFacilityApiError(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithText("Failed to load facilities")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test scrolling behavior for long activation details
     */
    fun testScrolling(): ActivationDetailRobot {
        // Verify the screen is scrollable
        composeTestRule
            .onNodeWithTag("activation_details_column")
            .performScrollToIndex(5) // Scroll to see more fields
        
        return this
    }

    /**
     * Verify all required activation detail fields are present
     */
    fun verifyAllRequiredFields(): ActivationDetailRobot {
        val requiredFields = listOf(
            "First Name",
            "Last Name", 
            "Email",
            "Phone",
            "Organization"
        )
        
        requiredFields.forEach { field ->
            composeTestRule
                .onNodeWithText(field)
                .assertIsDisplayed()
        }
        
        return this
    }

    /**
     * Verify that the continue button is enabled
     */
    fun verifyContinueButtonIsEnabled(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsEnabled()
        
        return this
    }

    /**
     * Verify that the continue button is disabled
     */
    fun verifyContinueButtonIsDisabled(): ActivationDetailRobot {
        composeTestRule
            .onNodeWithText("Continue")
            .assertIsNotEnabled()
        
        return this
    }

    /**
     * Wait for facility data to load
     */
    fun waitForFacilityDataLoad(): ActivationDetailRobot {
        composeTestRule.waitForIdle()
        
        // Wait for facility API call to complete
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            try {
                composeTestRule
                    .onNodeWithText("Continue")
                    .assertIsEnabled()
                true
            } catch (e: AssertionError) {
                false
            }
        }
        
        return this
    }

    /**
     * Verify screen orientation handling
     */
    fun verifyOrientationHandling(): ActivationDetailRobot {
        // Test that the screen handles orientation changes properly
        // Implementation would depend on your test setup
        
        return this
    }

    /**
     * Test rapid button clicking (debouncing)
     */
    fun testRapidButtonClicking(): ActivationDetailRobot {
        // Click continue button multiple times rapidly
        repeat(5) {
            composeTestRule
                .onNodeWithText("Continue")
                .performClick()
        }
        
        // Verify only one navigation occurs
        composeTestRule.waitForIdle()
        
        return this
    }

    /**
     * Verify data persistence across screen rotations
     */
    fun verifyDataPersistence(): ActivationDetailRobot {
        // This would test that activation details persist
        // across configuration changes
        
        return this
    }

    /**
     * Verify proper handling of empty or null activation data
     */
    fun verifyEmptyDataHandling(): ActivationDetailRobot {
        // Test how the screen handles missing activation data
        composeTestRule
            .onNodeWithText("No activation data available")
            .assertIsDisplayed()
        
        return this
    }

    /**
     * Test deep link navigation to this screen
     */
    fun verifyDeepLinkNavigation(): ActivationDetailRobot {
        // Test that the screen can be reached via deep link
        // Implementation depends on your navigation setup
        
        return this
    }

    /**
     * Verify proper cleanup when leaving the screen
     */
    fun verifyScreenCleanup(): ActivationDetailRobot {
        // Test that resources are properly cleaned up
        // when navigating away from this screen
        
        return this
    }
}
