package com.noteactive.app.features.login.presentation.screens.enter_activation_key

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyNavigation
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyViewModelAction
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyApiState
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyState
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

//@RunWith(AndroidJUnit4::class)
//class EnterActivationKeyScreenUITest {
//
//    @get:Rule
//    val composeTestRule = createComposeRule()
//
//    // Test Data
//    private val initialState = ActivationKeyState(
//        text = "",
//        isLoading = false,
//        isError = false,
//        isButtonEnabled = false
//    )
//
//    private val stateWithText = ActivationKeyState(
//        text = "test-key-123",
//        isLoading = false,
//        isError = false,
//        isButtonEnabled = true
//    )
//
//    private val loadingState = ActivationKeyState(
//        text = "test-key-123",
//        isLoading = true,
//        isError = false,
//        isButtonEnabled = false
//    )
//
//    private val errorState = ActivationKeyState(
//        text = "invalid-key",
//        isLoading = false,
//        isError = true,
//        isButtonEnabled = true
//    )
//
//    private val mockUserInfo = ActivationKeyResponseUserInfo(
//        id = "test_001",
//        firstName = "John",
//        lastName = "Doe",
//        email = "<EMAIL>",
//        contactNumber = "1234567890",
//        companyName = "Test Company",
//        activationKey = "TEST-KEY-123",
//        address = "123 Test Street",
//        androidId = "android-test-id",
//        serverUrl = "https://test.com",
//        userDate = "2024-01-01",
//        facilities = "Test Facility",
//        loginFacilities = "Login Facility",
//        customerId = "customer-123",
//        assetId = "asset-123",
//        supportServerId = "support-123",
//        serverUrlOther = "https://other.test.com",
//        version = "1.0.0",
//        versionV2 = "2.0.0",
//        isUpdated = "true",
//        dateUpdated = "2024-01-01",
//        oktaEnable = "false",
//        isAllCustomerData = "true",
//        userId = "user-123",
//        environment = "test"
//    )
//
//    @Test
//    fun enterActivationKeyScreen_displaysAllUIElements() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = initialState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Verify all UI elements are displayed
//        composeTestRule.onNodeWithText("Login").assertIsDisplayed()
//        composeTestRule.onNodeWithText("Enter Key").assertIsDisplayed()
//        composeTestRule.onNodeWithText("Continue").assertIsDisplayed()
//
//        // Verify app logo/image is present
//        composeTestRule.onNode(hasContentDescription(null)).assertExists()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_continueButtonDisabledInitially() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = initialState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert
//        composeTestRule.onNodeWithText("Continue")
//            .assertIsDisplayed()
//            .assertIsNotEnabled()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_continueButtonEnabledWhenTextEntered() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = stateWithText,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert
//        composeTestRule.onNodeWithText("Continue")
//            .assertIsDisplayed()
//            .assertIsEnabled()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_textInputTriggersOnTextChangedAction() {
//        // Arrange
//        var capturedAction: EnterActivationKeyViewModelAction? = null
//        val testInput = "test-activation-key"
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = initialState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = { action ->
//                        capturedAction = action
//                    },
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Act
//        composeTestRule.onNodeWithText("Enter Key")
//            .performTextInput(testInput)
//
//        // Assert
//        assert(capturedAction is EnterActivationKeyViewModelAction.OnTextChanged)
//        assert((capturedAction as EnterActivationKeyViewModelAction.OnTextChanged).value == testInput)
//    }
//
//    @Test
//    fun enterActivationKeyScreen_continueButtonTriggersOnSubmitAction() {
//        // Arrange
//        var capturedAction: EnterActivationKeyViewModelAction? = null
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = stateWithText,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = { action ->
//                        capturedAction = action
//                    },
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Act
//        composeTestRule.onNodeWithText("Continue")
//            .performClick()
//
//        // Assert
//        assert(capturedAction is EnterActivationKeyViewModelAction.OnSubmit)
//    }
//
//    @Test
//    fun enterActivationKeyScreen_showsErrorStateCorrectly() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = errorState,
//                    activationApiState = ActivationKeyApiState.Error("Invalid activation key"),
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert
//        composeTestRule.onNodeWithText("Please enter valid key").assertIsDisplayed()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_successfulApiCallTriggersNavigation() {
//        // Arrange
//        var navigationTriggered = false
//        var capturedNavigation: EnterActivationKeyNavigation? = null
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = stateWithText,
//                    activationApiState = ActivationKeyApiState.Success(mockUserInfo),
//                    onViewmodelAction = {},
//                    onNavigation = { navigation ->
//                        navigationTriggered = true
//                        capturedNavigation = navigation
//                    }
//                )
//            }
//        }
//
//        // Assert
//        composeTestRule.waitForIdle()
//        assert(navigationTriggered)
//        assert(capturedNavigation == EnterActivationKeyNavigation.ActivationFormNav)
//    }
//
//    @Test
//    fun enterActivationKeyScreen_completeUserFlow() {
//        // Arrange
//        var currentState by mutableStateOf(initialState)
//        var capturedActions = mutableListOf<EnterActivationKeyViewModelAction>()
//        var navigationTriggered = false
//        val testActivationKey = "valid-key-123"
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = currentState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = { action ->
//                        capturedActions.add(action)
//                        when (action) {
//                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
//                                currentState = currentState.copy(
//                                    text = action.value,
//                                    isButtonEnabled = action.value.isNotBlank()
//                                )
//                            }
//                            is EnterActivationKeyViewModelAction.OnSubmit -> {
//                                // Simulate loading state
//                                currentState = currentState.copy(isLoading = true)
//                            }
//                            else -> {}
//                        }
//                    },
//                    onNavigation = { navigation ->
//                        when (navigation) {
//                            EnterActivationKeyNavigation.ActivationFormNav -> {
//                                navigationTriggered = true
//                            }
//                        }
//                    }
//                )
//            }
//        }
//
//        // Act & Assert - Step 1: Verify initial state
//        composeTestRule.onNodeWithText("Continue").assertIsNotEnabled()
//
//        // Act & Assert - Step 2: Enter activation key
//        composeTestRule.onNodeWithText("Enter Key")
//            .performTextInput(testActivationKey)
//        composeTestRule.waitForIdle()
//
//        // Verify text input action was captured
//        assert(capturedActions.any { it is EnterActivationKeyViewModelAction.OnTextChanged })
//        assert(currentState.text == testActivationKey)
//        assert(currentState.isButtonEnabled)
//
//        // Act & Assert - Step 3: Click continue button
//        composeTestRule.onNodeWithText("Continue")
//            .assertIsEnabled()
//            .performClick()
//        composeTestRule.waitForIdle()
//
//        // Verify submit action was captured
//        assert(capturedActions.any { it is EnterActivationKeyViewModelAction.OnSubmit })
//    }
//
//    @Test
//    fun enterActivationKeyScreen_clearTextDisablesContinueButton() {
//        // Arrange
//        var currentState by mutableStateOf(stateWithText)
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = currentState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = { action ->
//                        when (action) {
//                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
//                                currentState = currentState.copy(
//                                    text = action.value,
//                                    isButtonEnabled = action.value.isNotBlank()
//                                )
//                            }
//                            else -> {}
//                        }
//                    },
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Act - Clear the text field
//        composeTestRule.onNodeWithText("Enter Key")
//            .performTextClearance()
//        composeTestRule.waitForIdle()
//
//        // Assert
//        composeTestRule.onNodeWithText("Continue").assertIsNotEnabled()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_handlesApiErrorState() {
//        // Arrange
//        var capturedAction: EnterActivationKeyViewModelAction? = null
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = errorState,
//                    activationApiState = ActivationKeyApiState.Error("Network error occurred"),
//                    onViewmodelAction = { action ->
//                        capturedAction = action
//                    },
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Error state should trigger OnError action via LaunchedEffect
//        composeTestRule.waitForIdle()
//        // Note: This test verifies the LaunchedEffect behavior for error handling
//    }
//
//    @Test
//    fun enterActivationKeyScreen_loadingStateDisablesButton() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = loadingState,
//                    activationApiState = ActivationKeyApiState.Loading,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Button should be disabled during loading
//        composeTestRule.onNodeWithText("Continue")
//            .assertIsDisplayed()
//            .assertIsNotEnabled()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_textFieldHasCorrectProperties() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = initialState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Text field properties
//        composeTestRule.onNodeWithText("Enter Key")
//            .assertIsDisplayed()
//            .assertHasClickAction()
//            .assertIsEnabled()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_buttonHasCorrectProperties() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = stateWithText,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Button properties
//        composeTestRule.onNodeWithText("Continue")
//            .assertIsDisplayed()
//            .assertHasClickAction()
//            .assertIsEnabled()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_titleDisplaysCorrectly() {
//        // Arrange & Act
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = initialState,
//                    activationApiState = ActivationKeyApiState.Idle,
//                    onViewmodelAction = {},
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Title is displayed
//        composeTestRule.onNodeWithText("Login")
//            .assertIsDisplayed()
//    }
//
//    @Test
//    fun enterActivationKeyScreen_errorRecoveryOnTextChange() {
//        // Arrange
//        var currentState by mutableStateOf(errorState)
//
//        composeTestRule.setContent {
//            MyAppTheme {
//                ActivationKeyRoot(
//                    activationKeyState = currentState,
//                    activationApiState = ActivationKeyApiState.Error("Invalid key"),
//                    onViewmodelAction = { action ->
//                        when (action) {
//                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
//                                currentState = currentState.copy(
//                                    text = action.value,
//                                    isButtonEnabled = action.value.isNotBlank(),
//                                    isError = false // Clear error on text change
//                                )
//                            }
//                            else -> {}
//                        }
//                    },
//                    onNavigation = {}
//                )
//            }
//        }
//
//        // Assert - Initially in error state
//        composeTestRule.onNodeWithText("Please enter valid key").assertIsDisplayed()
//
//        // Act - Modify text to recover from error
//        composeTestRule.onNodeWithText("Enter Key")
//            .performTextClearance()
//        composeTestRule.onNodeWithText("Enter Key")
//            .performTextInput("valid-key")
//        composeTestRule.waitForIdle()
//
//        // Assert - Error should be cleared
//        assert(!currentState.isError)
//        assert(currentState.text == "valid-key")
//        assert(currentState.isButtonEnabled)
//    }
//}
