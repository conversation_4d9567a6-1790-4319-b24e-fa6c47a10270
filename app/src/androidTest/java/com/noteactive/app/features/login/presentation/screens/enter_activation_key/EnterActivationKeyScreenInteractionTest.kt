package com.noteactive.app.features.login.presentation.screens.enter_activation_key

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyNavigation
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.action.EnterActivationKeyViewModelAction
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyApiState
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.state.ActivationKeyState
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class EnterActivationKeyScreenInteractionTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private val mockUserInfo = ActivationKeyResponseUserInfo(
        id = "interaction_test_001",
        firstName = "Interaction",
        lastName = "Test",
        email = "<EMAIL>",
        activationKey = "INTERACTION-KEY"
    )

    @Test
    fun enterActivationKeyScreen_textInputUpdatesState() {
        // Arrange
        var currentState by mutableStateOf(ActivationKeyState())
        val testInputs = listOf("A", "AB", "ABC", "ABCD", "ABCDE")

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act & Assert - Test incremental text input
        testInputs.forEach { input ->
            composeTestRule.onNodeWithText("Enter Key")
                .performTextClearance()
            composeTestRule.onNodeWithText("Enter Key")
                .performTextInput(input)
            composeTestRule.waitForIdle()

            assert(currentState.text == input) { "State should update to: $input" }
            assert(currentState.isButtonEnabled) { "Button should be enabled for: $input" }
        }
    }

    @Test
    fun enterActivationKeyScreen_buttonClicksOnlyWhenEnabled() {
        // Arrange
        var submitClickCount = 0
        var currentState by mutableStateOf(ActivationKeyState(text = "", isButtonEnabled = false))

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnSubmit -> {
                                submitClickCount++
                            }
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act - Try to click disabled button
        composeTestRule.onNodeWithText("Continue").performClick()
        composeTestRule.waitForIdle()

        // Assert - Should not register click when disabled
        assert(submitClickCount == 0) { "Disabled button should not register clicks" }

        // Act - Enable button and click
        composeTestRule.onNodeWithText("Enter Key").performTextInput("test")
        composeTestRule.waitForIdle()
        composeTestRule.onNodeWithText("Continue").performClick()
        composeTestRule.waitForIdle()

        // Assert - Should register click when enabled
        assert(submitClickCount == 1) { "Enabled button should register clicks" }
    }

    @Test
    fun enterActivationKeyScreen_multipleRapidTextChanges() {
        // Arrange
        var actionCount = 0
        var currentState by mutableStateOf(ActivationKeyState())

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                actionCount++
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act - Perform rapid text changes
        val textField = composeTestRule.onNodeWithText("Enter Key")
        repeat(5) { index ->
            textField.performTextClearance()
            textField.performTextInput("test$index")
        }
        composeTestRule.waitForIdle()

        // Assert - Should handle all text changes
        assert(actionCount >= 5) { "Should handle multiple rapid text changes" }
        assert(currentState.text == "test4") { "Should have final text value" }
        assert(currentState.isButtonEnabled) { "Button should be enabled" }
    }

    @Test
    fun enterActivationKeyScreen_specialCharacterInput() {
        // Arrange
        var currentState by mutableStateOf(ActivationKeyState())
        val specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act - Input special characters
        composeTestRule.onNodeWithText("Enter Key")
            .performTextInput(specialChars)
        composeTestRule.waitForIdle()

        // Assert - Should handle special characters
        assert(currentState.text == specialChars) { "Should accept special characters" }
        assert(currentState.isButtonEnabled) { "Button should be enabled with special chars" }
    }

    @Test
    fun enterActivationKeyScreen_longTextInput() {
        // Arrange
        var currentState by mutableStateOf(ActivationKeyState())
        val longText = "A".repeat(1000) // Very long text

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act - Input very long text
        composeTestRule.onNodeWithText("Enter Key")
            .performTextInput(longText)
        composeTestRule.waitForIdle()

        // Assert - Should handle long text
        assert(currentState.text == longText) { "Should accept long text input" }
        assert(currentState.isButtonEnabled) { "Button should be enabled with long text" }
    }

    @Test
    fun enterActivationKeyScreen_whitespaceHandling() {
        // Arrange
        var currentState by mutableStateOf(ActivationKeyState())
        val whitespaceInputs = listOf(" ", "  ", "   ", "\t", "\n")

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act & Assert - Test whitespace inputs
        whitespaceInputs.forEach { input ->
            composeTestRule.onNodeWithText("Enter Key")
                .performTextClearance()
            composeTestRule.onNodeWithText("Enter Key")
                .performTextInput(input)
            composeTestRule.waitForIdle()

            assert(currentState.text == input) { "Should store whitespace: '$input'" }
            assert(!currentState.isButtonEnabled) { "Button should be disabled for whitespace: '$input'" }
        }
    }

    @Test
    fun enterActivationKeyScreen_textSelectionAndReplacement() {
        // Arrange
        var currentState by mutableStateOf(ActivationKeyState())

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act - Input initial text
        composeTestRule.onNodeWithText("Enter Key")
            .performTextInput("initial-text")
        composeTestRule.waitForIdle()

        // Act - Replace with new text
        composeTestRule.onNodeWithText("Enter Key")
            .performTextClearance()
        composeTestRule.onNodeWithText("Enter Key")
            .performTextInput("replaced-text")
        composeTestRule.waitForIdle()

        // Assert - Should have replaced text
        assert(currentState.text == "replaced-text") { "Should replace text correctly" }
        assert(currentState.isButtonEnabled) { "Button should remain enabled" }
    }

    @Test
    fun enterActivationKeyScreen_submitButtonMultipleClicks() {
        // Arrange
        var submitCount = 0
        var currentState by mutableStateOf(
            ActivationKeyState(text = "test-key", isButtonEnabled = true)
        )

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Idle,
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnSubmit -> {
                                submitCount++
                                // Simulate loading state to prevent multiple submissions
                                currentState = currentState.copy(
                                    isLoading = true,
                                    isButtonEnabled = false
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Act - Click button multiple times rapidly
        val continueButton = composeTestRule.onNodeWithText("Continue")
        continueButton.performClick()
        continueButton.performClick()
        continueButton.performClick()
        composeTestRule.waitForIdle()

        // Assert - Should handle multiple clicks gracefully
        assert(submitCount >= 1) { "Should register at least one click" }
        // After first click, button should be disabled due to loading state
    }

    @Test
    fun enterActivationKeyScreen_errorStateInteraction() {
        // Arrange
        var currentState by mutableStateOf(
            ActivationKeyState(text = "invalid-key", isError = true, isButtonEnabled = true)
        )
        var errorActionTriggered = false

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = currentState,
                    activationApiState = ActivationKeyApiState.Error("Invalid key"),
                    onViewmodelAction = { action ->
                        when (action) {
                            is EnterActivationKeyViewModelAction.OnError -> {
                                errorActionTriggered = true
                                currentState = currentState.copy(
                                    isError = true,
                                    isButtonEnabled = false
                                )
                            }
                            is EnterActivationKeyViewModelAction.OnTextChanged -> {
                                currentState = currentState.copy(
                                    text = action.value,
                                    isError = false,
                                    isButtonEnabled = action.value.isNotBlank()
                                )
                            }
                            else -> {}
                        }
                    },
                    onNavigation = {}
                )
            }
        }

        // Assert - Error message should be visible
        composeTestRule.onNodeWithText("Please enter valid key").assertIsDisplayed()

        // Act - Modify text to clear error
        composeTestRule.onNodeWithText("Enter Key")
            .performTextInput("X") // Add character to trigger text change
        composeTestRule.waitForIdle()

        // Assert - Error should be cleared
        assert(!currentState.isError) { "Error should be cleared on text change" }
        assert(currentState.isButtonEnabled) { "Button should be enabled after error cleared" }
    }

    @Test
    fun enterActivationKeyScreen_successStateTriggersNavigation() {
        // Arrange
        var navigationTriggered = false
        var capturedNavigation: EnterActivationKeyNavigation? = null

        composeTestRule.setContent {
            MyAppTheme {
                ActivationKeyRoot(
                    activationKeyState = ActivationKeyState(text = "success-key", isButtonEnabled = true),
                    activationApiState = ActivationKeyApiState.Success(mockUserInfo),
                    onViewmodelAction = {},
                    onNavigation = { navigation ->
                        navigationTriggered = true
                        capturedNavigation = navigation
                    }
                )
            }
        }

        // Assert - Success state should trigger navigation automatically
        composeTestRule.waitForIdle()
        assert(navigationTriggered) { "Success state should trigger navigation" }
        assert(capturedNavigation == EnterActivationKeyNavigation.ActivationFormNav) {
            "Should trigger ActivationFormNav navigation"
        }
    }
}
