package com.noteactive.app.features.login.presentation.screens

import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.features.MainActivity
import com.noteactive.app.features.login.presentation.screens.data.LoginFlowTestData
import com.noteactive.app.features.login.presentation.screens.robots.ActivationDetailRobot
import com.noteactive.app.features.login.presentation.screens.robots.EnterActivationKeyRobot
import com.noteactive.app.features.login.presentation.screens.robots.LoginScreenRobot
import com.noteactive.app.features.login.presentation.screens.robots.SelectFacilityRobot
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Real API Integration Tests for Login Feature Flow
 * 
 * This test class focuses specifically on testing the login flow with actual
 * API calls to your backend services. It validates that the UI correctly
 * handles real network responses, loading states, and error conditions.
 * 
 * Key Features:
 * - Tests with actual backend API endpoints
 * - Validates real network response handling
 * - Tests actual loading states and timeouts
 * - Verifies real error responses from backend
 * - Validates data transformation from API to UI
 * - Tests network failure scenarios
 * 
 * Prerequisites:
 * - Backend services must be running and accessible
 * - Valid test activation keys must exist in backend
 * - Network connectivity required
 * - Test environment properly configured
 * 
 * Test Environment Setup:
 * - Ensure your backend is running on test/staging environment
 * - Configure proper API endpoints in your app
 * - Set up valid test data in your backend
 * - Configure network timeouts appropriately
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class LoginFlowRealApiIntegrationTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    private lateinit var enterActivationKeyRobot: EnterActivationKeyRobot
    private lateinit var activationDetailRobot: ActivationDetailRobot
    private lateinit var loginScreenRobot: LoginScreenRobot
    private lateinit var selectFacilityRobot: SelectFacilityRobot

    @Before
    fun setup() {
        hiltRule.inject()
        
        enterActivationKeyRobot = EnterActivationKeyRobot(composeTestRule)
        activationDetailRobot = ActivationDetailRobot(composeTestRule)
        loginScreenRobot = LoginScreenRobot(composeTestRule)
        selectFacilityRobot = SelectFacilityRobot(composeTestRule)
    }

    /**
     * Test Case: Real API Activation Key Validation
     * 
     * Scenario: Test activation key validation with actual backend
     * 
     * Steps:
     * 1. Enter valid activation key that exists in backend
     * 2. Verify real API call is made
     * 3. Validate actual API response data
     * 4. Verify UI updates with real data
     * 
     * Expected Result: Real activation data is retrieved and displayed
     */
    @Test
    fun realApi_validActivationKey_retrievesAndDisplaysRealData() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .verifyRealApiCallTriggered()
            .waitForApiSuccess()
            .verifyNavigationToActivationDetail()

        // Verify real activation details are displayed
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .verifyActivationDetailsAreShown()
            .verifyAllRequiredFields()  // Validate real API response fields
    }

    /**
     * Test Case: Real API Error Handling
     * 
     * Scenario: Test error handling with actual backend error responses
     * 
     * Steps:
     * 1. Enter invalid activation key
     * 2. Verify real API error response
     * 3. Validate error message from backend
     * 4. Test error recovery with valid key
     * 
     * Expected Result: Real API errors are handled gracefully
     */
    @Test
    fun realApi_invalidActivationKey_handlesRealApiError() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.InvalidActivationKeys.DEFINITELY_INVALID)
            .clickContinueButton()
            .waitForApiError()
            .verifyErrorIsDisplayed()
            .verifyNoNavigationOccurs()

        // Test recovery with valid key
        enterActivationKeyRobot
            .clearActivationKey()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .waitForApiSuccess()
            .verifyNavigationToActivationDetail()
    }

    /**
     * Test Case: Real Facility Data Loading
     * 
     * Scenario: Test facility loading with actual backend data
     * 
     * Steps:
     * 1. Complete activation flow
     * 2. Navigate to facility selection
     * 3. Verify real facility data loads
     * 4. Test facility search with real data
     * 5. Select actual facility from backend
     * 
     * Expected Result: Real facility data is loaded and selectable
     */
    @Test
    fun realApi_facilityDataLoading_loadsAndDisplaysRealFacilities() {
        // Complete activation flow
        completeActivationFlow()

        // Navigate to login and then facility selection
        activationDetailRobot
            .clickContinueButton()

        loginScreenRobot
            .verifyScreenIsDisplayed()
            .selectFirstAvailableCustomer()
            .enterPin(LoginFlowTestData.ValidPins.NUMERIC_PIN)
            .clickLoginButton()

        // Test real facility data
        selectFacilityRobot
            .verifyScreenIsDisplayed()
            .verifyFacilityListIsPopulated()
            .testRealFacilitySearch()
            .selectFirstAvailableFacility()
            .verifyNavigationToMainApp()
    }

    /**
     * Test Case: Network Timeout Handling
     * 
     * Scenario: Test handling of network timeouts with real API
     * 
     * Steps:
     * 1. Configure short timeout
     * 2. Make API call that may timeout
     * 3. Verify timeout handling
     * 4. Test retry mechanism
     * 
     * Expected Result: Network timeouts are handled gracefully
     */
    @Test
    fun realApi_networkTimeout_handlesTimeoutGracefully() {
        // This test would require network simulation or very short timeouts
        // Implementation depends on your network layer configuration
        
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            
        // Wait for either success or timeout
        composeTestRule.waitForIdle()
        
        // Verify appropriate handling (either success or timeout error)
        // This depends on your actual network configuration
    }

    /**
     * Test Case: Real Customer Data Validation
     * 
     * Scenario: Test customer data loading and validation from real API
     * 
     * Steps:
     * 1. Complete activation and facility loading
     * 2. Navigate to login screen
     * 3. Verify real customer data loads
     * 4. Validate customer data format
     * 5. Test customer selection
     * 
     * Expected Result: Real customer data is properly loaded and validated
     */
    @Test
    fun realApi_customerDataValidation_loadsAndValidatesRealCustomers() {
        // Complete activation flow
        completeActivationFlow()

        activationDetailRobot
            .clickContinueButton()

        // Test real customer data
        loginScreenRobot
            .verifyScreenIsDisplayed()
            .verifyCustomerListIsPopulated()
            .validateRealCustomerData()
            .selectFirstAvailableCustomer()
            .verifyCustomerSelectionPersists()
    }

    /**
     * Test Case: API Response Performance
     * 
     * Scenario: Test API response times meet performance requirements
     * 
     * Steps:
     * 1. Measure activation key API response time
     * 2. Measure facility loading time
     * 3. Measure customer loading time
     * 4. Verify all responses meet performance benchmarks
     * 
     * Expected Result: All API calls complete within acceptable time limits
     */
    @Test
    fun realApi_performanceBenchmarks_meetRequirements() {
        val startTime = System.currentTimeMillis()
        
        // Test activation key API performance
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .waitForApiSuccess()
        
        val activationTime = System.currentTimeMillis() - startTime
        
        // Verify activation API meets performance requirement
        assert(activationTime <= LoginFlowTestData.PerformanceTestData.MAX_RESPONSE_TIME_MS) {
            "Activation API took ${activationTime}ms, expected <= ${LoginFlowTestData.PerformanceTestData.MAX_RESPONSE_TIME_MS}ms"
        }

        // Continue with facility loading performance test
        val facilityStartTime = System.currentTimeMillis()
        
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .waitForFacilityDataLoad()
        
        val facilityTime = System.currentTimeMillis() - facilityStartTime
        
        // Verify facility API meets performance requirement
        assert(facilityTime <= LoginFlowTestData.PerformanceTestData.MAX_RESPONSE_TIME_MS) {
            "Facility API took ${facilityTime}ms, expected <= ${LoginFlowTestData.PerformanceTestData.MAX_RESPONSE_TIME_MS}ms"
        }
    }

    /**
     * Test Case: Real Data Validation
     * 
     * Scenario: Validate that real API responses contain expected data structures
     * 
     * Steps:
     * 1. Make API calls and capture responses
     * 2. Validate response data structure
     * 3. Verify required fields are present
     * 4. Test data type validation
     * 
     * Expected Result: All API responses contain valid, expected data
     */
    @Test
    fun realApi_dataValidation_validatesRealApiResponses() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .waitForApiSuccess()

        // Validate real activation detail data structure
        activationDetailRobot
            .verifyScreenIsDisplayed()
            .validateRealActivationDetailData()
            .verifyRequiredFieldsHaveValues()
            .clickContinueButton()

        // Validate real customer and facility data
        loginScreenRobot
            .verifyScreenIsDisplayed()
            .validateRealCustomerData()
    }

    /**
     * Helper method to complete the activation flow
     */
    private fun completeActivationFlow() {
        enterActivationKeyRobot
            .verifyScreenIsDisplayed()
            .enterActivationKey(LoginFlowTestData.ValidActivationKeys.DEMO_KEY)
            .clickContinueButton()
            .waitForApiSuccess()
    }
}
