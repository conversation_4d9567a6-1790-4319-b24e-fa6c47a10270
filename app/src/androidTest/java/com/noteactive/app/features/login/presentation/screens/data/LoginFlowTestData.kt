package com.noteactive.app.features.login.presentation.screens.data

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.Facility
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetailInfo
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.Facility

/**
 * Test Data Provider for Login Flow End-to-End Tests with Real API Calls
 *
 * This object provides real test data for testing with actual API endpoints.
 * It includes valid activation keys that work with your backend, real customer
 * data, and actual facility information returned from the API.
 *
 * Data Categories:
 * - Real Activation Keys (working with actual API)
 * - Expected API Response Validation
 * - Real Customer Data from API
 * - Actual Facility Information
 * - Network Error Scenarios
 * - Edge Cases for Real Data
 *
 * Note: These tests will make actual network calls to your backend services.
 * Ensure test environment is properly configured with valid endpoints.
 */
object LoginFlowTestData {

    /**
     * Real activation keys that work with your actual API
     * These should be valid keys in your test/staging environment
     */
    object ValidActivationKeys {
        const val DEMO_KEY = "hpdemo"  // Known working key from your codebase
        const val TEST_KEY = "testkey123"  // Add more real keys as needed
        const val STAGING_KEY = "staging-key-001"  // Staging environment key
        const val QA_KEY = "qa-test-key"  // QA environment key

        // Add more real activation keys that exist in your backend
        val ALL_VALID_KEYS = listOf(DEMO_KEY, TEST_KEY, STAGING_KEY, QA_KEY)
    }

    /**
     * Invalid activation keys for testing error scenarios with real API
     */
    object InvalidActivationKeys {
        const val EMPTY = ""
        const val WHITESPACE_ONLY = "   "
        const val DEFINITELY_INVALID = "this-key-does-not-exist-in-backend"
        const val TOO_SHORT = "ab"
        const val MALFORMED = "invalid@#$%key"
        const val EXPIRED_KEY = "expired-test-key"  // If you have expired keys to test

        val ALL_INVALID_KEYS = listOf(
            EMPTY, WHITESPACE_ONLY, DEFINITELY_INVALID,
            TOO_SHORT, MALFORMED, EXPIRED_KEY
        )
    }

    /**
     * Expected API response validation patterns
     * These help validate that real API responses contain expected data
     */
    object ApiResponseValidation {
        // Expected fields in activation response
        val REQUIRED_USER_FIELDS = listOf(
            "id", "firstName", "lastName", "email", "activationKey"
        )

        // Expected fields in facility response
        val REQUIRED_FACILITY_FIELDS = listOf(
            "facility", "isSelected"
        )

        // Validation patterns for real data
        fun isValidEmail(email: String): Boolean {
            return email.contains("@") && email.contains(".")
        }

        fun isValidUserId(id: String): Boolean {
            return id.isNotEmpty() && id.length >= 3
        }

        fun isValidFacilityName(name: String): Boolean {
            return name.isNotEmpty() && name.length >= 2
        }
    }

    /**
     * Real activation detail field expectations
     * These represent the actual fields returned by your API
     */
    object ActivationDetailFields {
        // Expected field titles that should appear in the UI
        val EXPECTED_FIELD_TITLES = listOf(
            "First Name",
            "Last Name",
            "Email",
            "Phone",
            "Organization",
            "Department",
            "Role"
        )

        // Validation for activation detail data from real API
        fun validateActivationDetailData(details: List<ActivationDetailInfo>): Boolean {
            return details.isNotEmpty() &&
                   details.any { it.title == "First Name" } &&
                   details.any { it.title == "Last Name" } &&
                   details.any { it.title == "Email" }
        }

        // Check if required fields have non-empty values
        fun hasRequiredFieldValues(details: List<ActivationDetailInfo>): Boolean {
            val firstName = details.find { it.title == "First Name" }?.text
            val lastName = details.find { it.title == "Last Name" }?.text
            val email = details.find { it.title == "Email" }?.text

            return !firstName.isNullOrEmpty() &&
                   !lastName.isNullOrEmpty() &&
                   !email.isNullOrEmpty() &&
                   ApiResponseValidation.isValidEmail(email)
        }
    }

    /**
     * Real customer data expectations from API
     * These will be populated from actual API responses
     */
    object CustomerData {
        // Customer selection validation
        fun validateCustomerData(customers: List<String>): Boolean {
            return customers.isNotEmpty() &&
                   customers.all { it.isNotEmpty() && it.length >= 2 }
        }

        // Expected customer data patterns from your API
        val EXPECTED_CUSTOMER_PATTERNS = listOf(
            ".*Customer.*",  // Contains "Customer"
            ".*Corp.*",      // Contains "Corp"
            ".*Inc.*",       // Contains "Inc"
            ".*Ltd.*"        // Contains "Ltd"
        )

        // Validate customer name format
        fun isValidCustomerName(name: String): Boolean {
            return name.isNotEmpty() &&
                   name.length >= 2 &&
                   name.length <= 100 &&
                   name.trim() == name  // No leading/trailing whitespace
        }
    }

    /**
     * Real facility data expectations from API
     * These will validate actual facility data returned from your backend
     */
    object FacilityData {
        // Validate facility list from real API
        fun validateFacilityData(facilities: List<Facility>): Boolean {
            return facilities.isNotEmpty() &&
                   facilities.all { ApiResponseValidation.isValidFacilityName(it.facility) }
        }

        // Expected facility name patterns from your API
        val EXPECTED_FACILITY_PATTERNS = listOf(
            ".*Facility.*",   // Contains "Facility"
            ".*Center.*",     // Contains "Center"
            ".*Building.*",   // Contains "Building"
            ".*Campus.*",     // Contains "Campus"
            ".*Site.*"        // Contains "Site"
        )

        // Validate individual facility
        fun isValidFacility(facility: Facility): Boolean {
            return facility.facility.isNotEmpty() &&
                   facility.facility.length >= 2 &&
                   facility.facility.length <= 200
        }

        // Search validation for real facility data
        fun validateSearchResults(searchTerm: String, results: List<Facility>): Boolean {
            return if (searchTerm.isEmpty()) {
                true  // Empty search should return all facilities
            } else {
                results.all {
                    it.facility.contains(searchTerm, ignoreCase = true)
                }
            }
        }
    }

    /**
     * Valid PIN combinations for testing
     */
    object ValidPins {
        const val NUMERIC_PIN = "1234"
        const val ALPHANUMERIC_PIN = "abc123"
        const val SPECIAL_CHAR_PIN = "12@#"
        const val LONG_PIN = "123456789"
        const val SINGLE_CHAR = "1"
    }

    /**
     * Invalid PIN combinations for testing
     */
    object InvalidPins {
        const val EMPTY = ""
        const val WHITESPACE_ONLY = "   "
        const val TOO_LONG = "a".repeat(50)
    }

    /**
     * Error messages for testing error scenarios
     */
    object ErrorMessages {
        const val INVALID_ACTIVATION_KEY = "Invalid activation key"
        const val NETWORK_ERROR = "Network error"
        const val SERVER_ERROR = "Server error"
        const val INVALID_PIN = "Invalid PIN"
        const val NO_CUSTOMER_SELECTED = "Please select a customer first"
        const val FAILED_TO_LOAD_FACILITIES = "Failed to load facilities"
        const val NO_FACILITIES_FOUND = "No facilities found"
        const val NO_FACILITIES_AVAILABLE = "No facilities available"
    }

    /**
     * Search terms for testing facility search
     */
    object SearchTerms {
        const val VALID_SEARCH = "Test"
        const val CASE_INSENSITIVE = "test"
        const val PARTIAL_MATCH = "Fac"
        const val NO_RESULTS = "xyz123"
        const val SPECIAL_CHARS = "@#$"
        const val NUMERIC = "123"
        const val EMPTY = ""
        const val WHITESPACE = "   "
    }

    /**
     * Real API test scenarios with actual backend integration
     */
    object TestScenarios {
        data class RealApiLoginFlowScenario(
            val name: String,
            val activationKey: String,
            val shouldSucceed: Boolean,
            val expectedError: String? = null,
            val validateApiResponse: Boolean = true,
            val timeoutMs: Long = 10000L
        )

        val happyPathScenario = RealApiLoginFlowScenario(
            name = "Happy Path with Real API",
            activationKey = ValidActivationKeys.DEMO_KEY,
            shouldSucceed = true,
            validateApiResponse = true
        )

        val invalidActivationKeyScenario = RealApiLoginFlowScenario(
            name = "Invalid Activation Key - Real API Error",
            activationKey = InvalidActivationKeys.DEFINITELY_INVALID,
            shouldSucceed = false,
            expectedError = ErrorMessages.INVALID_ACTIVATION_KEY,
            validateApiResponse = false
        )

        val emptyActivationKeyScenario = RealApiLoginFlowScenario(
            name = "Empty Activation Key",
            activationKey = InvalidActivationKeys.EMPTY,
            shouldSucceed = false,
            expectedError = ErrorMessages.INVALID_ACTIVATION_KEY,
            validateApiResponse = false
        )

        val networkTimeoutScenario = RealApiLoginFlowScenario(
            name = "Network Timeout Scenario",
            activationKey = ValidActivationKeys.DEMO_KEY,
            shouldSucceed = false,
            expectedError = ErrorMessages.NETWORK_ERROR,
            timeoutMs = 1000L  // Short timeout to simulate network issues
        )

        val allRealApiScenarios = listOf(
            happyPathScenario,
            invalidActivationKeyScenario,
            emptyActivationKeyScenario,
            networkTimeoutScenario
        )
    }

    /**
     * Network simulation data
     */
    object NetworkSimulation {
        const val NETWORK_DELAY_MS = 2000L
        const val TIMEOUT_MS = 5000L
        const val RETRY_ATTEMPTS = 3
    }

    /**
     * Accessibility test data
     */
    object AccessibilityTestData {
        val contentDescriptions = mapOf(
            "activation_key_field" to "Enter your activation key",
            "continue_button" to "Continue to next step",
            "back_button" to "Go back to previous screen",
            "customer_selection" to "Select customer",
            "pin_field" to "Enter your PIN",
            "login_button" to "Login to application",
            "facility_search" to "Search for facility",
            "facility_item" to "Select this facility"
        )

        val semanticLabels = mapOf(
            "screen_title" to "Current screen title",
            "error_message" to "Error message",
            "loading_indicator" to "Loading content",
            "success_message" to "Success message"
        )
    }

    /**
     * Performance test data
     */
    object PerformanceTestData {
        const val MAX_RESPONSE_TIME_MS = 3000L
        const val MAX_NAVIGATION_TIME_MS = 1000L
        const val MAX_SEARCH_TIME_MS = 500L
        const val LARGE_LIST_SIZE = 1000
    }
}
