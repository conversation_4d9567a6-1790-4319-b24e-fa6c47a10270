package com.noteactive.app.features.login.presentation.screens.data

import com.noteactive.app.core.data.model_entity_with_dao.response_entity_dao.facility.Facility
import com.noteactive.app.features.login.data.model.entity.response.activation_response.ActivationKeyResponseUserInfo
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.state.ActivationDetailInfo
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.Facility

/**
 * Test Data Provider for Login Flow End-to-End Tests
 * 
 * This object provides mock data and test scenarios for all login flow screens.
 * It includes valid and invalid test cases, edge cases, and error scenarios
 * to ensure comprehensive test coverage.
 * 
 * Data Categories:
 * - Activation Keys (valid/invalid)
 * - User Information
 * - Customer Data
 * - Facility Lists
 * - Error Scenarios
 * - Edge Cases
 */
object LoginFlowTestData {

    /**
     * Valid activation keys for testing successful flows
     */
    object ValidActivationKeys {
        const val DEMO_KEY = "hpdemo"
        const val TEST_KEY = "testkey123"
        const val ALPHA_NUMERIC = "ABC123DEF456"
        const val WITH_SPECIAL_CHARS = "test-key_2024"
        const val LONG_KEY = "very-long-activation-key-for-testing-purposes-2024"
    }

    /**
     * Invalid activation keys for testing error scenarios
     */
    object InvalidActivationKeys {
        const val EMPTY = ""
        const val WHITESPACE_ONLY = "   "
        const val INVALID_FORMAT = "invalid-key"
        const val TOO_SHORT = "ab"
        const val SPECIAL_ONLY = "!@#$%"
        const val NUMERIC_ONLY = "123456"
    }

    /**
     * Mock user information for activation responses
     */
    object MockUserInfo {
        val validUser = ActivationKeyResponseUserInfo(
            id = "test_user_001",
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            activationKey = ValidActivationKeys.DEMO_KEY
        )

        val userWithLongNames = ActivationKeyResponseUserInfo(
            id = "test_user_002",
            firstName = "Christopher Alexander",
            lastName = "Montgomery-Wellington",
            email = "<EMAIL>",
            activationKey = ValidActivationKeys.TEST_KEY
        )

        val userWithSpecialChars = ActivationKeyResponseUserInfo(
            id = "test_user_003",
            firstName = "José",
            lastName = "García-López",
            email = "<EMAIL>",
            activationKey = ValidActivationKeys.ALPHA_NUMERIC
        )
    }

    /**
     * Mock activation detail information
     */
    object MockActivationDetails {
        val standardDetails = listOf(
            ActivationDetailInfo(
                title = "First Name",
                text = "John",
                type = "text"
            ),
            ActivationDetailInfo(
                title = "Last Name", 
                text = "Doe",
                type = "text"
            ),
            ActivationDetailInfo(
                title = "Email",
                text = "<EMAIL>",
                type = "email"
            ),
            ActivationDetailInfo(
                title = "Phone",
                text = "******-123-4567",
                type = "phone"
            ),
            ActivationDetailInfo(
                title = "Organization",
                text = "Test Organization",
                type = "text"
            )
        )

        val detailsWithEmptyFields = listOf(
            ActivationDetailInfo(
                title = "First Name",
                text = "",
                type = "text"
            ),
            ActivationDetailInfo(
                title = "Last Name",
                text = "Doe",
                type = "text"
            ),
            ActivationDetailInfo(
                title = "Email",
                text = "",
                type = "email"
            )
        )
    }

    /**
     * Mock customer data for login screen
     */
    object MockCustomers {
        val customers = listOf(
            "Test Customer 1",
            "Test Customer 2", 
            "Demo Customer",
            "Alpha Customer",
            "Beta Customer",
            "Customer with Very Long Name That Should Be Handled Properly"
        )

        val customersWithSpecialChars = listOf(
            "Customer & Co.",
            "Customer @ Location",
            "Customer #1",
            "Customer (Main)",
            "Customer - Branch"
        )

        val emptyCustomerList = emptyList<String>()
    }

    /**
     * Mock facility data for facility selection
     */
    object MockFacilities {
        val facilities = listOf(
            Facility("Facility 1", false),
            Facility("Facility 2", false),
            Facility("Test Facility", false),
            Facility("Demo Facility", false),
            Facility("Main Facility", false),
            Facility("Branch Facility", false),
            Facility("Emergency Facility", false),
            Facility("Backup Facility", false)
        )

        val facilitiesWithSpecialChars = listOf(
            Facility("Facility @ Location", false),
            Facility("Facility & Associates", false),
            Facility("Facility #1", false),
            Facility("Facility (Main Branch)", false),
            Facility("Facility - North", false)
        )

        val largeFacilityList = (1..100).map { 
            Facility("Facility $it", false) 
        }

        val emptyFacilityList = emptyList<Facility>()

        val facilitiesWithLongNames = listOf(
            Facility("Very Long Facility Name That Should Be Handled Properly In The UI", false),
            Facility("Another Extremely Long Facility Name For Testing Purposes", false),
            Facility("Short", false)
        )
    }

    /**
     * Valid PIN combinations for testing
     */
    object ValidPins {
        const val NUMERIC_PIN = "1234"
        const val ALPHANUMERIC_PIN = "abc123"
        const val SPECIAL_CHAR_PIN = "12@#"
        const val LONG_PIN = "123456789"
        const val SINGLE_CHAR = "1"
    }

    /**
     * Invalid PIN combinations for testing
     */
    object InvalidPins {
        const val EMPTY = ""
        const val WHITESPACE_ONLY = "   "
        const val TOO_LONG = "a".repeat(50)
    }

    /**
     * Error messages for testing error scenarios
     */
    object ErrorMessages {
        const val INVALID_ACTIVATION_KEY = "Invalid activation key"
        const val NETWORK_ERROR = "Network error"
        const val SERVER_ERROR = "Server error"
        const val INVALID_PIN = "Invalid PIN"
        const val NO_CUSTOMER_SELECTED = "Please select a customer first"
        const val FAILED_TO_LOAD_FACILITIES = "Failed to load facilities"
        const val NO_FACILITIES_FOUND = "No facilities found"
        const val NO_FACILITIES_AVAILABLE = "No facilities available"
    }

    /**
     * Search terms for testing facility search
     */
    object SearchTerms {
        const val VALID_SEARCH = "Test"
        const val CASE_INSENSITIVE = "test"
        const val PARTIAL_MATCH = "Fac"
        const val NO_RESULTS = "xyz123"
        const val SPECIAL_CHARS = "@#$"
        const val NUMERIC = "123"
        const val EMPTY = ""
        const val WHITESPACE = "   "
    }

    /**
     * Test scenarios for comprehensive coverage
     */
    object TestScenarios {
        data class LoginFlowScenario(
            val name: String,
            val activationKey: String,
            val customerName: String,
            val pin: String,
            val facilityName: String,
            val shouldSucceed: Boolean,
            val expectedError: String? = null
        )

        val happyPathScenario = LoginFlowScenario(
            name = "Happy Path",
            activationKey = ValidActivationKeys.DEMO_KEY,
            customerName = MockCustomers.customers[0],
            pin = ValidPins.NUMERIC_PIN,
            facilityName = MockFacilities.facilities[0].facility,
            shouldSucceed = true
        )

        val invalidActivationKeyScenario = LoginFlowScenario(
            name = "Invalid Activation Key",
            activationKey = InvalidActivationKeys.INVALID_FORMAT,
            customerName = MockCustomers.customers[0],
            pin = ValidPins.NUMERIC_PIN,
            facilityName = MockFacilities.facilities[0].facility,
            shouldSucceed = false,
            expectedError = ErrorMessages.INVALID_ACTIVATION_KEY
        )

        val invalidPinScenario = LoginFlowScenario(
            name = "Invalid PIN",
            activationKey = ValidActivationKeys.DEMO_KEY,
            customerName = MockCustomers.customers[0],
            pin = InvalidPins.EMPTY,
            facilityName = MockFacilities.facilities[0].facility,
            shouldSucceed = false,
            expectedError = ErrorMessages.INVALID_PIN
        )

        val noCustomerSelectedScenario = LoginFlowScenario(
            name = "No Customer Selected",
            activationKey = ValidActivationKeys.DEMO_KEY,
            customerName = "",
            pin = ValidPins.NUMERIC_PIN,
            facilityName = MockFacilities.facilities[0].facility,
            shouldSucceed = false,
            expectedError = ErrorMessages.NO_CUSTOMER_SELECTED
        )

        val allScenarios = listOf(
            happyPathScenario,
            invalidActivationKeyScenario,
            invalidPinScenario,
            noCustomerSelectedScenario
        )
    }

    /**
     * Network simulation data
     */
    object NetworkSimulation {
        const val NETWORK_DELAY_MS = 2000L
        const val TIMEOUT_MS = 5000L
        const val RETRY_ATTEMPTS = 3
    }

    /**
     * Accessibility test data
     */
    object AccessibilityTestData {
        val contentDescriptions = mapOf(
            "activation_key_field" to "Enter your activation key",
            "continue_button" to "Continue to next step",
            "back_button" to "Go back to previous screen",
            "customer_selection" to "Select customer",
            "pin_field" to "Enter your PIN",
            "login_button" to "Login to application",
            "facility_search" to "Search for facility",
            "facility_item" to "Select this facility"
        )

        val semanticLabels = mapOf(
            "screen_title" to "Current screen title",
            "error_message" to "Error message",
            "loading_indicator" to "Loading content",
            "success_message" to "Success message"
        )
    }

    /**
     * Performance test data
     */
    object PerformanceTestData {
        const val MAX_RESPONSE_TIME_MS = 3000L
        const val MAX_NAVIGATION_TIME_MS = 1000L
        const val MAX_SEARCH_TIME_MS = 500L
        const val LARGE_LIST_SIZE = 1000
    }
}
