package com.noteactive.app.features.login.presentation.screens.data

object LoginFlowTestData {


    object ValidActivationKeys {
        const val DEMO_KEY = "hpdemo"  // Known working key from your codebase
        const val DEMO_PIN = "7777"  // Known working key from your codebase
    }

    object InvalidActivationKeys {
        const val DEFINITELY_INVALID = "this-key-does-not-exist-in-backend"
    }


    object ActivationDetailFields {
        // Expected field titles that should appear in the UI
        val EXPECTED_FIELD_TITLES = listOf(
            "First Name",
            "Last Name",
            "Email",
            "Phone",
            "Organization",
            "Department",
            "Role"
        )

    }
}
