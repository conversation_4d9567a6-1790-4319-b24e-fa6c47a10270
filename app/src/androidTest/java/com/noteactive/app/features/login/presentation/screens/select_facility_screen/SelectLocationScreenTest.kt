package com.noteactive.app.features.login.presentation.screens.select_facility_screen

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextInput
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.noteactive.app.core.presentation.designsystem.theme.MyAppTheme
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityNavigationAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.action.SelectFacilityViewModelAction
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.Facility
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.SelectFacilityState
import com.noteactive.app.features.login.presentation.screens.select_facility_screen.state.getFacility
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class SelectLocationScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private lateinit var defaultFacilities: List<Facility>
    private lateinit var initialState: MutableState<SelectFacilityState>

    @Before
    fun setup() {
        defaultFacilities = getFacility()
        initialState = mutableStateOf(SelectFacilityState(
            allFacilityList = defaultFacilities,
            filteredList = defaultFacilities,
            searchText = ""
        ))
    }

    private fun setScreen(
        state: SelectFacilityState = initialState.value,
        onAction: (SelectFacilityViewModelAction) -> Unit = {},
        onNav: (SelectFacilityNavigationAction) -> Unit = {}
    ) {
        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = state,
                    action = onAction,
                    selectFacilityNavigation = onNav
                )
            }
        }
    }

    private fun performSearch(text: String) {
        composeTestRule.onNodeWithText("Search Facility").performTextInput(text)
        composeTestRule.waitForIdle()
    }

    @Test
    fun testSelectFacilityScreenDisplaysCorrectly() {
        setScreen()

        composeTestRule.onNodeWithText("Select Facility").assertIsDisplayed()
        composeTestRule.onNodeWithText("Search Facility").assertIsDisplayed()
        composeTestRule.onNodeWithText("Facility 1").assertIsDisplayed()
        composeTestRule.onNodeWithText("Facility 2").assertIsDisplayed()
    }

    @Test
    fun testSearchFieldAcceptsInput() {
        var capturedAction: SelectFacilityViewModelAction? = null
        var currentState by mutableStateOf(SelectFacilityState(
            allFacilityList = defaultFacilities,
            filteredList = defaultFacilities,
            searchText = ""
        ))

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        capturedAction = action
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(searchText = action.searchText)
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        performSearch("Facility 1")

        assert(capturedAction is SelectFacilityViewModelAction.OnSearchFacilityAction)
        assert(currentState.searchText == "Facility 1")
    }

    @Test
    fun testSearchFilteringWorksCorrectly() {
        var currentState by mutableStateOf(SelectFacilityState(
            allFacilityList = defaultFacilities,
            filteredList = defaultFacilities,
            searchText = ""
        ))

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        performSearch("Facility 1")
        composeTestRule.onNodeWithText("Facility 1").assertIsDisplayed()
        composeTestRule.onNodeWithText("Facility 10").assertIsDisplayed()
        assert(currentState.filteredList.isNotEmpty())
        composeTestRule.waitForIdle()
        composeTestRule.onNodeWithText("Facility 2").assertDoesNotExist()
    }

    @Test
    fun testFacilitySelectionTriggersAction() {
        var capturedAction: SelectFacilityViewModelAction? = null
        var capturedNavigation: SelectFacilityNavigationAction? = null

        setScreen(
            onAction = { capturedAction = it },
            onNav = { capturedNavigation = it }
        )

        composeTestRule.onNodeWithText("Facility 1").performClick()
        composeTestRule.waitForIdle()

        val action = capturedAction as? SelectFacilityViewModelAction.OnFacilitySelectAction
        val navigation = capturedNavigation as? SelectFacilityNavigationAction.NavigateBackWithFacility

        assert(action != null && action.facility.facilityName == "Facility 1")
        assert(navigation != null)
    }

    @Test
    fun testSelectedFacilityGetsHighlighted() {
        val facilities = listOf(
            Facility("Facility 1", isSelected = true),
            Facility("Facility 2", isSelected = false),
            Facility("Facility 3", isSelected = false)
        )

        setScreen(
            state = SelectFacilityState(
                allFacilityList = facilities,
                filteredList = facilities,
                searchText = ""
            )
        )

        facilities.forEach {
            composeTestRule.onNodeWithText(it.facilityName).assertIsDisplayed()
        }
    }

    @Test
    fun testEmptySearchShowsAllFacilities() {
        var currentState by mutableStateOf(
            initialState.value.copy(filteredList = emptyList(), searchText = "test")
        )

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            val filtered = if (action.searchText.isEmpty())
                                currentState.allFacilityList
                            else currentState.allFacilityList.filter {
                                it.facilityName.contains(action.searchText, ignoreCase = true)
                            }

                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = filtered
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        performSearch("")

        listOf("Facility 1", "Facility 2", "Facility 3").forEach {
            composeTestRule.onNodeWithText(it).assertIsDisplayed()
        }
    }

    @Test
    fun testSearchIsCaseInsensitive() {
        var currentState by mutableStateOf(SelectFacilityState(
            allFacilityList = defaultFacilities,
            filteredList = defaultFacilities,
            searchText = ""
        ))

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Search with lowercase
        performSearch("facility 1")

        // Should still find "Facility 1" despite case difference
        composeTestRule.onNodeWithText("Facility 1").assertIsDisplayed()
        composeTestRule.onNodeWithText("Facility 10").assertIsDisplayed()
        composeTestRule.onNodeWithText("Facility 2").assertDoesNotExist()
    }

    @Test
    fun testNoResultsWhenSearchDoesNotMatch() {
        var currentState by mutableStateOf(SelectFacilityState(
            allFacilityList = defaultFacilities,
            filteredList = defaultFacilities,
            searchText = ""
        ))

        composeTestRule.setContent {
            MyAppTheme {
                com.noteactive.app.features.login.presentation.screens.select_customer_screen.SelectFacilityScreen(
                    selectFacilityState = currentState,
                    action = { action ->
                        if (action is SelectFacilityViewModelAction.OnSearchFacilityAction) {
                            currentState = currentState.copy(
                                searchText = action.searchText,
                                filteredList = currentState.allFacilityList.filter {
                                    it.facilityName.contains(action.searchText, ignoreCase = true)
                                }
                            )
                        }
                    },
                    selectFacilityNavigation = { }
                )
            }
        }

        // Search for something that doesn't exist
        performSearch("NonExistentFacility")

        // No facilities should be displayed
        composeTestRule.onNodeWithText("Facility 1").assertDoesNotExist()
        composeTestRule.onNodeWithText("Facility 2").assertDoesNotExist()
        assert(currentState.filteredList.isEmpty())
    }
}
