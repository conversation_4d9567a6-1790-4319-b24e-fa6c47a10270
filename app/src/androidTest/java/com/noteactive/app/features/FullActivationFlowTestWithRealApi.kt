package com.noteactive.app.features

import androidx.activity.viewModels  // For ViewModel access
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.test.junit4.ComposeTestRule
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.test.ext.junit.runners.AndroidJUnit4
import app.cash.turbine.test  // For flow testing
import com.noteactive.app.features.login.presentation.screens.activation_detail_screen.ActivationDetailsViewModel
import com.noteactive.app.features.login.presentation.screens.enter_activation_key.EnterActivationKeyViewModel
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.runner.RunWith

// ... other imports as before

@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class FullActivationFlowRealApiTest {

    // ... existing rules

    private lateinit var enterActivationKeyViewModel: EnterActivationKeyViewModel
    private lateinit var activationDetailsViewModel: ActivationDetailsViewModel

    @Before
    fun setup() {
        hiltRule.inject()

        composeTestRule.setContent {
            navController = rememberNavController()
            val lifecycleOwner = LocalLifecycleOwner.current

            NavHost(
                navController = navController,
                startDestination = Screens.AuthGraph.route
            ) {
                authNavGraph(navController = navController, startDestination = Screens.EnterActivationKeyScreen.route)
            }

            // Retrieve ViewModels (assuming they are scoped to their screens)
            enterActivationKeyViewModel = hiltViewModel()  // For EnterActivationKey
            activationDetailsViewModel = hiltViewModel()  // For ActivationDetail
        }
    }

    // ... authNavGraph function as before
}


// EnterActivationKeyRobot
class EnterActivationKeyRobot(
    private val composeTestRule: ComposeTestRule,
    private val viewModel: EnterActivationKeyViewModel  // Pass ViewModel to Robot
) {
    // ... existing methods

    // New: Capture and assert API response from ViewModel
    suspend fun captureAndAssertApiSuccess(expectedResult: String): EnterActivationKeyRobot {  // Example: assert specific data
        viewModel.apiState.test {
            val state = awaitItem()  // Collect flow
            assert(state is ActivationKeyApiState.Success)
            val successState = state as ActivationKeyApiState.Success
            assert(successState.data.result.first() == expectedResult)  // Assert specific API data; customize
            Log.d("E2ETest", "Captured API Response: ${successState.data}")  // Log for debugging
            cancelAndIgnoreRemainingEvents()
        }
        return this
    }

    // New: Assert error response
    suspend fun assertApiError(expectedMessage: String): EnterActivationKeyRobot {
        viewModel.apiState.test {
            val state = awaitItem()
            assert(state is ActivationKeyApiState.Error)
            assert((state as ActivationKeyApiState.Error).message == expectedMessage)
            cancelAndIgnoreRemainingEvents()
        }
        return this
    }
}

// ActivationDetailRobot
class ActivationDetailRobot(
    private val composeTestRule: ComposeTestRule,
    private val viewModel: ActivationDetailsViewModel
) {
    // ... existing methods

    // New: Capture and assert facility API success
    suspend fun captureAndAssertFacilitySuccess(expectedData: String): ActivationDetailRobot {
        viewModel.facilityApiState.test {
            val state = awaitItem()
            assert(state is FacilityApiState.Success)
            val successState = state as FacilityApiState.Success
            assert(successState.data == expectedData)  // Customize to your data structure
            Log.d("E2ETest", "Captured Facility Response: $successState")
            cancelAndIgnoreRemainingEvents()
        }
        return this
    }
}

// LoginScreenRobot remains UI-only unless it has a ViewModel
