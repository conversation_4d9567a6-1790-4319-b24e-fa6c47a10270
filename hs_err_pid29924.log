#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1266816 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=29924, tid=17752
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.14+7) (build 17.0.14+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.14+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx1024m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 7 7840HS w/ Radeon 780M Graphics     , 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sun Jun 29 13:05:38 2025 India Standard Time elapsed time: 184.933643 seconds (0d 0h 3m 4s)

---------------  T H R E A D  ---------------

Current thread (0x000001600c3df730):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=17752, stack(0x0000000ecf300000,0x0000000ecf400000)]


Current CompileTask:
C2: 184933 19908 %     4       org.jetbrains.kotlin.gradle.internal.kapt.incremental.ClasspathEntryData::writeObject @ 441 (1063 bytes)

Stack: [0x0000000ecf300000,0x0000000ecf400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x681069]
V  [jvm.dll+0x838cca]
V  [jvm.dll+0x83a78e]
V  [jvm.dll+0x83adf3]
V  [jvm.dll+0x24826f]
V  [jvm.dll+0xac9f4]
V  [jvm.dll+0xad03c]
V  [jvm.dll+0x2af8ef]
V  [jvm.dll+0x5874f7]
V  [jvm.dll+0x2230d2]
V  [jvm.dll+0x2234cf]
V  [jvm.dll+0x21c5e0]
V  [jvm.dll+0x219ae1]
V  [jvm.dll+0x1a599d]
V  [jvm.dll+0x22994d]
V  [jvm.dll+0x227adc]
V  [jvm.dll+0x7edb77]
V  [jvm.dll+0x7e7f6c]
V  [jvm.dll+0x67ff37]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016008f9f4a0, length=193, elements={
0x0000015fa30289e0, 0x0000015fbf9cd6a0, 0x0000015fbfa51f10, 0x0000015fbfa69310,
0x0000015fbfa69be0, 0x0000015fbfa6b020, 0x0000015fbfa6d470, 0x0000015fbfa72770,
0x0000015fbfa74c00, 0x0000015fbfa80a20, 0x0000015fbfb57a30, 0x0000015fbfcc0a00,
0x0000016006a18050, 0x000001600548da50, 0x000001600548cc20, 0x000001600513f470,
0x0000016006ee84a0, 0x0000016006eea300, 0x0000016006ee7a80, 0x0000016006eea810,
0x0000016006ee8ec0, 0x00000160065d3e20, 0x000001600cbd5cb0, 0x000001600cbd66d0,
0x000001600cbd70f0, 0x0000016008502db0, 0x00000160085032c0, 0x00000160085037d0,
0x0000016012b41920, 0x0000016012b3e170, 0x0000016012b3f0a0, 0x0000016012b3e680,
0x0000016012b3f5b0, 0x0000016012b3fac0, 0x0000016012b41410, 0x0000016012b3ffd0,
0x0000016013546310, 0x0000016013546820, 0x0000016013547240, 0x0000016013546d30,
0x00000160135458f0, 0x0000016013545e00, 0x000001600d655340, 0x0000016012b40f00,
0x0000016012b404e0, 0x000001600cbd7600, 0x000001600cbd61c0, 0x000001600cbd6be0,
0x0000016009771930, 0x0000016009771420, 0x000001600976f5c0, 0x00000160126b6570,
0x00000160126b74a0, 0x00000160126b79b0, 0x00000160126b7ec0, 0x00000160126b88e0,
0x00000160126b83d0, 0x00000160126b5130, 0x00000160126b5640, 0x00000160126b5b50,
0x00000160126b6a80, 0x00000160126b6f90, 0x00000160143079c0, 0x0000016014306a90,
0x0000016014306580, 0x00000160143074b0, 0x0000016014f6da20, 0x0000016014f6bbc0,
0x0000016014f6d000, 0x0000016014f6c0d0, 0x0000016014f6c5e0, 0x0000016014f6b1a0,
0x0000016014f6df30, 0x0000016014f6caf0, 0x0000016014f6ac90, 0x0000016014f6d510,
0x0000016014f6a780, 0x000001600bb509b0, 0x000001600bb504a0, 0x000001600bb50ec0,
0x000001600c9b9480, 0x000001600c9b9990, 0x000001600c9b8550, 0x0000016008743d90,
0x0000016008744cc0, 0x00000160087451d0, 0x00000160087456e0, 0x00000160087442a0,
0x00000160087447b0, 0x000001600a999030, 0x000001600a996cc0, 0x000001600a999f60,
0x000001600a998610, 0x000001600a998100, 0x000001600a999540, 0x000001600a998b20,
0x000001600a99a470, 0x000001600a999a50, 0x000001600d039f60, 0x0000016014306fa0,
0x0000016014307ed0, 0x0000016008762cd0, 0x0000016008763c00, 0x00000160087636f0,
0x0000016008764110, 0x000001600976fad0, 0x000001600976e180, 0x000001600976e690,
0x000001600976ffe0, 0x00000160097704f0, 0x0000016009770a00, 0x000001600976f0b0,
0x0000016009770f10, 0x000001600976eba0, 0x000001600d657bc0, 0x000001600d655850,
0x000001600d654920, 0x000001600d656c90, 0x000001600d655d60, 0x000001600d6580d0,
0x0000016005addca0, 0x0000016005ade6c0, 0x0000016005ade1b0, 0x0000016013073330,
0x0000016013072400, 0x0000016013072910, 0x00000160130714d0, 0x0000016013073840,
0x0000016013072e20, 0x00000160125f6be0, 0x00000160125f5cb0, 0x000001600adaaf90,
0x000001600adaa570, 0x000001600adab4a0, 0x000001600adaaa80, 0x0000016014f6b6b0,
0x0000016014d145e0, 0x0000016014d11d60, 0x0000016014d12270, 0x0000016014d13bc0,
0x0000016014d136b0, 0x0000016014d12780, 0x0000016012b409f0, 0x0000016015cebb70,
0x0000016015cec590, 0x000001600d03a980, 0x000001600d03b8b0, 0x000001600d03ae90,
0x000001600d03a470, 0x0000016009162d80, 0x0000016009161e50, 0x0000016009162360,
0x0000016009163290, 0x0000016009162870, 0x0000016009161940, 0x000001600d03b3a0,
0x0000016013538ec0, 0x000001601353b740, 0x000001601353a300, 0x000001601353a810,
0x000001601353b230, 0x00000160135398e0, 0x00000160135389b0, 0x000001601353c160,
0x000001601353bc50, 0x00000160135393d0, 0x0000016013539df0, 0x000001601353ad20,
0x0000016008463920, 0x000001600a9971d0, 0x000001600c3df730, 0x000001600c3de740,
0x00000160125f57a0, 0x0000016014d12c90, 0x0000016014d131a0, 0x000001600a9976e0,
0x000001600a997bf0, 0x0000016012e0dfb0, 0x0000016012e0d080, 0x0000016012e0cb70,
0x0000016012e0daa0, 0x0000016012e0d590, 0x0000016012e0fe10, 0x0000016012e0e4c0,
0x0000016012e0c660, 0x0000016012e0e9d0, 0x0000016012e0eee0, 0x0000016012e0f3f0,
0x0000016012e0f900, 0x000001600d3a6a80, 0x000001600c0430a0, 0x000001600c0435f0,
0x000001600d3a6f90
}

Java Threads: ( => current thread )
  0x0000015fa30289e0 JavaThread "main" [_thread_blocked, id=28272, stack(0x0000000eccb00000,0x0000000eccc00000)]
  0x0000015fbf9cd6a0 JavaThread "Reference Handler" daemon [_thread_blocked, id=11560, stack(0x0000000ecd200000,0x0000000ecd300000)]
  0x0000015fbfa51f10 JavaThread "Finalizer" daemon [_thread_blocked, id=19544, stack(0x0000000ecd300000,0x0000000ecd400000)]
  0x0000015fbfa69310 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=17040, stack(0x0000000ecd400000,0x0000000ecd500000)]
  0x0000015fbfa69be0 JavaThread "Attach Listener" daemon [_thread_blocked, id=30204, stack(0x0000000ecd500000,0x0000000ecd600000)]
  0x0000015fbfa6b020 JavaThread "Service Thread" daemon [_thread_blocked, id=30624, stack(0x0000000ecd600000,0x0000000ecd700000)]
  0x0000015fbfa6d470 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=31336, stack(0x0000000ecd700000,0x0000000ecd800000)]
  0x0000015fbfa72770 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=19384, stack(0x0000000ecd800000,0x0000000ecd900000)]
  0x0000015fbfa74c00 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=4920, stack(0x0000000ecd900000,0x0000000ecda00000)]
  0x0000015fbfa80a20 JavaThread "Sweeper thread" daemon [_thread_blocked, id=31928, stack(0x0000000ecda00000,0x0000000ecdb00000)]
  0x0000015fbfb57a30 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=48724, stack(0x0000000ecdb00000,0x0000000ecdc00000)]
  0x0000015fbfcc0a00 JavaThread "Notification Thread" daemon [_thread_blocked, id=18896, stack(0x0000000ecdf00000,0x0000000ece000000)]
  0x0000016006a18050 JavaThread "Daemon health stats" [_thread_blocked, id=36588, stack(0x0000000ecec00000,0x0000000eced00000)]
  0x000001600548da50 JavaThread "Incoming local TCP Connector on port 64865" [_thread_in_native, id=42800, stack(0x0000000eced00000,0x0000000ecee00000)]
  0x000001600548cc20 JavaThread "Daemon periodic checks" [_thread_blocked, id=3696, stack(0x0000000ecee00000,0x0000000ecef00000)]
  0x000001600513f470 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=25020, stack(0x0000000ecf700000,0x0000000ecf800000)]
  0x0000016006ee84a0 JavaThread "File lock request listener" [_thread_in_native, id=48052, stack(0x0000000ecf800000,0x0000000ecf900000)]
  0x0000016006eea300 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)" [_thread_blocked, id=25488, stack(0x0000000ecf900000,0x0000000ecfa00000)]
  0x0000016006ee7a80 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)" [_thread_blocked, id=37492, stack(0x0000000ecfb00000,0x0000000ecfc00000)]
  0x0000016006eea810 JavaThread "File watcher server" daemon [_thread_in_native, id=23644, stack(0x0000000ed0200000,0x0000000ed0300000)]
  0x0000016006ee8ec0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=23656, stack(0x0000000ed0300000,0x0000000ed0400000)]
  0x00000160065d3e20 JavaThread "Memory manager" [_thread_blocked, id=39988, stack(0x0000000ed4700000,0x0000000ed4800000)]
  0x000001600cbd5cb0 JavaThread "Daemon Thread 2" [_thread_blocked, id=34124, stack(0x0000000ecc800000,0x0000000ecc900000)]
  0x000001600cbd66d0 JavaThread "Daemon worker Thread 2" [_thread_blocked, id=48232, stack(0x0000000ecdc00000,0x0000000ecdd00000)]
  0x000001600cbd70f0 JavaThread "included builds" [_thread_blocked, id=37488, stack(0x0000000ed3700000,0x0000000ed3800000)]
  0x0000016008502db0 JavaThread "VFS cleanup Thread 2" [_thread_blocked, id=47012, stack(0x0000000ecea00000,0x0000000eceb00000)]
  0x00000160085032c0 JavaThread "Handler for socket connection from /127.0.0.1:64865 to /127.0.0.1:49365" [_thread_in_native, id=46372, stack(0x0000000ecc900000,0x0000000ecca00000)]
  0x00000160085037d0 JavaThread "Cancel handler" [_thread_blocked, id=39268, stack(0x0000000ecca00000,0x0000000eccb00000)]
  0x0000016012b41920 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:64865 to /127.0.0.1:49365" [_thread_blocked, id=44704, stack(0x0000000ecef00000,0x0000000ecf000000)]
  0x0000016012b3e170 JavaThread "Stdin handler" [_thread_blocked, id=47400, stack(0x0000000ecf000000,0x0000000ecf100000)]
  0x0000016012b3f0a0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=47648, stack(0x0000000ecf100000,0x0000000ecf200000)]
  0x0000016012b3e680 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\AndroidStudioProjects\NoteActiveNewApplication\.gradle\8.11.1\fileHashes)" [_thread_blocked, id=32616, stack(0x0000000ecf200000,0x0000000ecf300000)]
  0x0000016012b3f5b0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\AndroidStudioProjects\NoteActiveNewApplication\.gradle\buildOutputCleanup)" [_thread_blocked, id=8272, stack(0x0000000ecf500000,0x0000000ecf600000)]
  0x0000016012b3fac0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\AndroidStudioProjects\NoteActiveNewApplication\.gradle\8.11.1\checksums)" [_thread_blocked, id=41960, stack(0x0000000ecf600000,0x0000000ecf700000)]
  0x0000016012b41410 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)" [_thread_blocked, id=44724, stack(0x0000000ecfa00000,0x0000000ecfb00000)]
  0x0000016012b3ffd0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)" [_thread_blocked, id=37868, stack(0x0000000ecfc00000,0x0000000ecfd00000)]
  0x0000016013546310 JavaThread "Unconstrained build operations" [_thread_blocked, id=39832, stack(0x0000000ed0500000,0x0000000ed0600000)]
  0x0000016013546820 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=23852, stack(0x0000000ed0600000,0x0000000ed0700000)]
  0x0000016013547240 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=13460, stack(0x0000000ed0700000,0x0000000ed0800000)]
  0x0000016013546d30 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=30372, stack(0x0000000ed0900000,0x0000000ed0a00000)]
  0x00000160135458f0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=33448, stack(0x0000000ed0a00000,0x0000000ed0b00000)]
  0x0000016013545e00 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=2080, stack(0x0000000ed0b00000,0x0000000ed0c00000)]
  0x000001600d655340 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=26604, stack(0x0000000ed0c00000,0x0000000ed0d00000)]
  0x0000016012b40f00 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=10928, stack(0x0000000ed0d00000,0x0000000ed0e00000)]
  0x0000016012b404e0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=15912, stack(0x0000000ed0e00000,0x0000000ed0f00000)]
  0x000001600cbd7600 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=10924, stack(0x0000000ed0f00000,0x0000000ed1000000)]
  0x000001600cbd61c0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=10920, stack(0x0000000ed1000000,0x0000000ed1100000)]
  0x000001600cbd6be0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=47980, stack(0x0000000ed1100000,0x0000000ed1200000)]
  0x0000016009771930 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=41044, stack(0x0000000ed1200000,0x0000000ed1300000)]
  0x0000016009771420 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=8564, stack(0x0000000ed1300000,0x0000000ed1400000)]
  0x000001600976f5c0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=48544, stack(0x0000000ed1400000,0x0000000ed1500000)]
  0x00000160126b6570 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=17980, stack(0x0000000ed1500000,0x0000000ed1600000)]
  0x00000160126b74a0 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=34496, stack(0x0000000ed1600000,0x0000000ed1700000)]
  0x00000160126b79b0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=21168, stack(0x0000000ed1700000,0x0000000ed1800000)]
  0x00000160126b7ec0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=48188, stack(0x0000000ed1800000,0x0000000ed1900000)]
  0x00000160126b88e0 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=19784, stack(0x0000000ed1900000,0x0000000ed1a00000)]
  0x00000160126b83d0 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=45456, stack(0x0000000ed1a00000,0x0000000ed1b00000)]
  0x00000160126b5130 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=19324, stack(0x0000000ed1b00000,0x0000000ed1c00000)]
  0x00000160126b5640 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=23976, stack(0x0000000ed1c00000,0x0000000ed1d00000)]
  0x00000160126b5b50 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=21672, stack(0x0000000ed1d00000,0x0000000ed1e00000)]
  0x00000160126b6a80 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=43784, stack(0x0000000ed1e00000,0x0000000ed1f00000)]
  0x00000160126b6f90 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=21140, stack(0x0000000ed1f00000,0x0000000ed2000000)]
  0x00000160143079c0 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=3076, stack(0x0000000ed2000000,0x0000000ed2100000)]
  0x0000016014306a90 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=34792, stack(0x0000000ed2100000,0x0000000ed2200000)]
  0x0000016014306580 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=46396, stack(0x0000000ed2200000,0x0000000ed2300000)]
  0x00000160143074b0 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=38436, stack(0x0000000ed2300000,0x0000000ed2400000)]
  0x0000016014f6da20 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=45976, stack(0x0000000ed2700000,0x0000000ed2800000)]
  0x0000016014f6bbc0 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=33456, stack(0x0000000ed2800000,0x0000000ed2900000)]
  0x0000016014f6d000 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=23356, stack(0x0000000ed2900000,0x0000000ed2a00000)]
  0x0000016014f6c0d0 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=27564, stack(0x0000000ed2a00000,0x0000000ed2b00000)]
  0x0000016014f6c5e0 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=6296, stack(0x0000000ed2b00000,0x0000000ed2c00000)]
  0x0000016014f6b1a0 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=15732, stack(0x0000000ed2c00000,0x0000000ed2d00000)]
  0x0000016014f6df30 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=40004, stack(0x0000000ed2d00000,0x0000000ed2e00000)]
  0x0000016014f6caf0 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=5548, stack(0x0000000ed2e00000,0x0000000ed2f00000)]
  0x0000016014f6ac90 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=28796, stack(0x0000000ed2f00000,0x0000000ed3000000)]
  0x0000016014f6d510 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=27984, stack(0x0000000ed3000000,0x0000000ed3100000)]
  0x0000016014f6a780 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=5380, stack(0x0000000ed3100000,0x0000000ed3200000)]
  0x000001600bb509b0 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=45988, stack(0x0000000ed3200000,0x0000000ed3300000)]
  0x000001600bb504a0 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=27248, stack(0x0000000ed3300000,0x0000000ed3400000)]
  0x000001600bb50ec0 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=43856, stack(0x0000000ed3400000,0x0000000ed3500000)]
  0x000001600c9b9480 JavaThread "build event listener" [_thread_blocked, id=6900, stack(0x0000000ed3500000,0x0000000ed3600000)]
  0x000001600c9b9990 JavaThread "build event listener" [_thread_blocked, id=32840, stack(0x0000000ed3600000,0x0000000ed3700000)]
  0x000001600c9b8550 JavaThread "included builds" [_thread_blocked, id=10496, stack(0x0000000ed3800000,0x0000000ed3900000)]
  0x0000016008743d90 JavaThread "Execution worker" [_thread_blocked, id=27224, stack(0x0000000ed3900000,0x0000000ed3a00000)]
  0x0000016008744cc0 JavaThread "Execution worker Thread 2" [_thread_blocked, id=23676, stack(0x0000000ed3a00000,0x0000000ed3b00000)]
  0x00000160087451d0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=23672, stack(0x0000000ed3b00000,0x0000000ed3c00000)]
  0x00000160087456e0 JavaThread "Execution worker Thread 4" [_thread_blocked, id=23868, stack(0x0000000ed3c00000,0x0000000ed3d00000)]
  0x00000160087442a0 JavaThread "Execution worker Thread 5" [_thread_blocked, id=15312, stack(0x0000000ed3d00000,0x0000000ed3e00000)]
  0x00000160087447b0 JavaThread "Execution worker Thread 6" [_thread_blocked, id=44916, stack(0x0000000ed3e00000,0x0000000ed3f00000)]
  0x000001600a999030 JavaThread "Execution worker Thread 7" [_thread_blocked, id=26240, stack(0x0000000ed3f00000,0x0000000ed4000000)]
  0x000001600a996cc0 JavaThread "Execution worker Thread 8" [_thread_blocked, id=10284, stack(0x0000000ed4000000,0x0000000ed4100000)]
  0x000001600a999f60 JavaThread "Execution worker Thread 9" [_thread_blocked, id=47352, stack(0x0000000ed4100000,0x0000000ed4200000)]
  0x000001600a998610 JavaThread "Execution worker Thread 10" [_thread_blocked, id=2752, stack(0x0000000ed4200000,0x0000000ed4300000)]
  0x000001600a998100 JavaThread "Execution worker Thread 11" [_thread_blocked, id=41084, stack(0x0000000ed4300000,0x0000000ed4400000)]
  0x000001600a999540 JavaThread "Execution worker Thread 12" [_thread_blocked, id=26852, stack(0x0000000ed4400000,0x0000000ed4500000)]
  0x000001600a998b20 JavaThread "Execution worker Thread 13" [_thread_blocked, id=29308, stack(0x0000000ed4500000,0x0000000ed4600000)]
  0x000001600a99a470 JavaThread "Execution worker Thread 14" [_thread_blocked, id=25040, stack(0x0000000ed4600000,0x0000000ed4700000)]
  0x000001600a999a50 JavaThread "Execution worker Thread 15" [_thread_blocked, id=19116, stack(0x0000000ed4800000,0x0000000ed4900000)]
  0x000001600d039f60 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\AndroidStudioProjects\NoteActiveNewApplication\.gradle\8.11.1\executionHistory)" [_thread_blocked, id=27072, stack(0x0000000ed4900000,0x0000000ed4a00000)]
  0x0000016014306fa0 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=25648, stack(0x0000000ed4a00000,0x0000000ed4b00000)]
  0x0000016014307ed0 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=31044, stack(0x0000000ed4b00000,0x0000000ed4c00000)]
  0x0000016008762cd0 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=30040, stack(0x0000000ed4c00000,0x0000000ed4d00000)]
  0x0000016008763c00 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=31636, stack(0x0000000ed4d00000,0x0000000ed4e00000)]
  0x00000160087636f0 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=31092, stack(0x0000000ed4e00000,0x0000000ed4f00000)]
  0x0000016008764110 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=23788, stack(0x0000000ed4f00000,0x0000000ed5000000)]
  0x000001600976fad0 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=30164, stack(0x0000000ed5000000,0x0000000ed5100000)]
  0x000001600976e180 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=1940, stack(0x0000000ed5100000,0x0000000ed5200000)]
  0x000001600976e690 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=31528, stack(0x0000000ed5200000,0x0000000ed5300000)]
  0x000001600976ffe0 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=31436, stack(0x0000000ed5300000,0x0000000ed5400000)]
  0x00000160097704f0 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=13032, stack(0x0000000ed5400000,0x0000000ed5500000)]
  0x0000016009770a00 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=29852, stack(0x0000000ed5500000,0x0000000ed5600000)]
  0x000001600976f0b0 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=45872, stack(0x0000000ed5600000,0x0000000ed5700000)]
  0x0000016009770f10 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=27672, stack(0x0000000ed5700000,0x0000000ed5800000)]
  0x000001600976eba0 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=28656, stack(0x0000000ed5800000,0x0000000ed5900000)]
  0x000001600d657bc0 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=8516, stack(0x0000000ed5900000,0x0000000ed5a00000)]
  0x000001600d655850 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=3144, stack(0x0000000ed5a00000,0x0000000ed5b00000)]
  0x000001600d654920 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=6360, stack(0x0000000ed5b00000,0x0000000ed5c00000)]
  0x000001600d656c90 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=44112, stack(0x0000000ed5c00000,0x0000000ed5d00000)]
  0x000001600d655d60 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=13520, stack(0x0000000ed5d00000,0x0000000ed5e00000)]
  0x000001600d6580d0 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=27216, stack(0x0000000ed5e00000,0x0000000ed5f00000)]
  0x0000016005addca0 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=38540, stack(0x0000000ed5f00000,0x0000000ed6000000)]
  0x0000016005ade6c0 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=30208, stack(0x0000000ed6000000,0x0000000ed6100000)]
  0x0000016005ade1b0 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=40544, stack(0x0000000ed6100000,0x0000000ed6200000)]
  0x0000016013073330 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=25996, stack(0x0000000ed6200000,0x0000000ed6300000)]
  0x0000016013072400 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=35280, stack(0x0000000ed6300000,0x0000000ed6400000)]
  0x0000016013072910 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=28072, stack(0x0000000ed6400000,0x0000000ed6500000)]
  0x00000160130714d0 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=47964, stack(0x0000000ed6500000,0x0000000ed6600000)]
  0x0000016013073840 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=28724, stack(0x0000000ed6600000,0x0000000ed6700000)]
  0x0000016013072e20 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=11792, stack(0x0000000ed6a00000,0x0000000ed6b00000)]
  0x00000160125f6be0 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=7972, stack(0x0000000ed6b00000,0x0000000ed6c00000)]
  0x00000160125f5cb0 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=28092, stack(0x0000000ed6c00000,0x0000000ed6d00000)]
  0x000001600adaaf90 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=48228, stack(0x0000000ed6d00000,0x0000000ed6e00000)]
  0x000001600adaa570 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=4912, stack(0x0000000ed6f00000,0x0000000ed7000000)]
  0x000001600adab4a0 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=21884, stack(0x0000000ed7000000,0x0000000ed7100000)]
  0x000001600adaaa80 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=31264, stack(0x0000000ed7100000,0x0000000ed7200000)]
  0x0000016014f6b6b0 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=45060, stack(0x0000000ed7200000,0x0000000ed7300000)]
  0x0000016014d145e0 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=46944, stack(0x0000000ed7300000,0x0000000ed7400000)]
  0x0000016014d11d60 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=25808, stack(0x0000000ed7400000,0x0000000ed7500000)]
  0x0000016014d12270 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=39540, stack(0x0000000ed7500000,0x0000000ed7600000)]
  0x0000016014d13bc0 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=47688, stack(0x0000000ed7600000,0x0000000ed7700000)]
  0x0000016014d136b0 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=37252, stack(0x0000000ed7700000,0x0000000ed7800000)]
  0x0000016014d12780 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=33848, stack(0x0000000ed7800000,0x0000000ed7900000)]
  0x0000016012b409f0 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=22520, stack(0x0000000ed7900000,0x0000000ed7a00000)]
  0x0000016015cebb70 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=2596, stack(0x0000000ed7a00000,0x0000000ed7b00000)]
  0x0000016015cec590 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=2568, stack(0x0000000ed7b00000,0x0000000ed7c00000)]
  0x000001600d03a980 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=2624, stack(0x0000000ed7c00000,0x0000000ed7d00000)]
  0x000001600d03b8b0 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=49072, stack(0x0000000ed7d00000,0x0000000ed7e00000)]
  0x000001600d03ae90 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=19504, stack(0x0000000ed7e00000,0x0000000ed7f00000)]
  0x000001600d03a470 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=17996, stack(0x0000000ed7f00000,0x0000000ed8000000)]
  0x0000016009162d80 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=17696, stack(0x0000000ed8000000,0x0000000ed8100000)]
  0x0000016009161e50 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=45956, stack(0x0000000ed8100000,0x0000000ed8200000)]
  0x0000016009162360 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=22868, stack(0x0000000ed8200000,0x0000000ed8300000)]
  0x0000016009163290 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=21896, stack(0x0000000ed8300000,0x0000000ed8400000)]
  0x0000016009162870 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=13820, stack(0x0000000ed8400000,0x0000000ed8500000)]
  0x0000016009161940 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=22500, stack(0x0000000ed8500000,0x0000000ed8600000)]
  0x000001600d03b3a0 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=13072, stack(0x0000000ed8600000,0x0000000ed8700000)]
  0x0000016013538ec0 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=13200, stack(0x0000000ed8700000,0x0000000ed8800000)]
  0x000001601353b740 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=9036, stack(0x0000000ed8800000,0x0000000ed8900000)]
  0x000001601353a300 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=16872, stack(0x0000000ed8900000,0x0000000ed8a00000)]
  0x000001601353a810 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=9924, stack(0x0000000ed8a00000,0x0000000ed8b00000)]
  0x000001601353b230 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=9620, stack(0x0000000ed8b00000,0x0000000ed8c00000)]
  0x00000160135398e0 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=12260, stack(0x0000000ed8c00000,0x0000000ed8d00000)]
  0x00000160135389b0 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=9536, stack(0x0000000ed8d00000,0x0000000ed8e00000)]
  0x000001601353c160 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=19056, stack(0x0000000ed8e00000,0x0000000ed8f00000)]
  0x000001601353bc50 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=36384, stack(0x0000000ed8f00000,0x0000000ed9000000)]
  0x00000160135393d0 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=26264, stack(0x0000000ed9000000,0x0000000ed9100000)]
  0x0000016013539df0 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=3576, stack(0x0000000ed9100000,0x0000000ed9200000)]
  0x000001601353ad20 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=11984, stack(0x0000000ed9200000,0x0000000ed9300000)]
  0x0000016008463920 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=28748, stack(0x0000000ed9300000,0x0000000ed9400000)]
  0x000001600a9971d0 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=5164, stack(0x0000000ed9400000,0x0000000ed9500000)]
=>0x000001600c3df730 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=17752, stack(0x0000000ecf300000,0x0000000ecf400000)]
  0x000001600c3de740 Thread [stack: 0x0000000ecf400000,0x0000000ecf500000] [id=25956]
  0x00000160125f57a0 JavaThread "WorkerExecutor Queue" [_thread_in_native, id=48428, stack(0x0000000ecfd00000,0x0000000ecfe00000)]
  0x0000016014d12c90 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=42232, stack(0x0000000ed0400000,0x0000000ed0500000)]
  0x0000016014d131a0 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=23816, stack(0x0000000ed2400000,0x0000000ed2500000)]
  0x000001600a9976e0 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=29520, stack(0x0000000ed2500000,0x0000000ed2600000)]
  0x000001600a997bf0 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=22796, stack(0x0000000ed2600000,0x0000000ed2700000)]
  0x0000016012e0dfb0 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=3596, stack(0x0000000ed9500000,0x0000000ed9600000)]
  0x0000016012e0d080 JavaThread "Unconstrained build operations Thread 121" [_thread_blocked, id=45724, stack(0x0000000ed9600000,0x0000000ed9700000)]
  0x0000016012e0cb70 JavaThread "Unconstrained build operations Thread 122" [_thread_blocked, id=38952, stack(0x0000000ed9700000,0x0000000ed9800000)]
  0x0000016012e0daa0 JavaThread "Unconstrained build operations Thread 123" [_thread_blocked, id=4312, stack(0x0000000ed9800000,0x0000000ed9900000)]
  0x0000016012e0d590 JavaThread "Unconstrained build operations Thread 124" [_thread_blocked, id=40456, stack(0x0000000ed9900000,0x0000000ed9a00000)]
  0x0000016012e0fe10 JavaThread "Unconstrained build operations Thread 125" [_thread_blocked, id=25292, stack(0x0000000ed9a00000,0x0000000ed9b00000)]
  0x0000016012e0e4c0 JavaThread "Unconstrained build operations Thread 126" [_thread_blocked, id=38008, stack(0x0000000ed9b00000,0x0000000ed9c00000)]
  0x0000016012e0c660 JavaThread "Unconstrained build operations Thread 127" [_thread_blocked, id=11652, stack(0x0000000ed9c00000,0x0000000ed9d00000)]
  0x0000016012e0e9d0 JavaThread "Unconstrained build operations Thread 128" [_thread_blocked, id=46640, stack(0x0000000ed9d00000,0x0000000ed9e00000)]
  0x0000016012e0eee0 JavaThread "Unconstrained build operations Thread 129" [_thread_blocked, id=12092, stack(0x0000000ed9e00000,0x0000000ed9f00000)]
  0x0000016012e0f3f0 JavaThread "Unconstrained build operations Thread 130" [_thread_blocked, id=20468, stack(0x0000000ed9f00000,0x0000000eda000000)]
  0x0000016012e0f900 JavaThread "Unconstrained build operations Thread 131" [_thread_blocked, id=20312, stack(0x0000000eda000000,0x0000000eda100000)]
  0x000001600d3a6a80 JavaThread "Unconstrained build operations Thread 132" [_thread_blocked, id=20328, stack(0x0000000eda100000,0x0000000eda200000)]
  0x000001600c0430a0 Thread [stack: 0x0000000eda200000,0x0000000eda300000] [id=2600]
  0x000001600c0435f0 Thread [stack: 0x0000000eda300000,0x0000000eda400000] [id=19692]
  0x000001600d3a6f90 Thread [stack: 0x0000000ecfe00000,0x0000000ecff00000] [id=36724]

Other Threads:
  0x0000015fbf9c8b40 VMThread "VM Thread" [stack: 0x0000000ecd100000,0x0000000ecd200000] [id=32508]
  0x0000015fbfcc0ee0 WatcherThread [stack: 0x0000000ece000000,0x0000000ece100000] [id=12860]
  0x0000015fa3069460 GCTaskThread "GC Thread#0" [stack: 0x0000000eccc00000,0x0000000eccd00000] [id=12616]
  0x00000160050daab0 GCTaskThread "GC Thread#1" [stack: 0x0000000ecdd00000,0x0000000ecde00000] [id=25792]
  0x000001600512c8d0 GCTaskThread "GC Thread#2" [stack: 0x0000000ecde00000,0x0000000ecdf00000] [id=31768]
  0x000001600512cb90 GCTaskThread "GC Thread#3" [stack: 0x0000000ece100000,0x0000000ece200000] [id=11920]
  0x000001600526e680 GCTaskThread "GC Thread#4" [stack: 0x0000000ece200000,0x0000000ece300000] [id=30228]
  0x000001600526e940 GCTaskThread "GC Thread#5" [stack: 0x0000000ece300000,0x0000000ece400000] [id=9940]
  0x000001600526ec00 GCTaskThread "GC Thread#6" [stack: 0x0000000ece400000,0x0000000ece500000] [id=17328]
  0x00000160056abc80 GCTaskThread "GC Thread#7" [stack: 0x0000000ece500000,0x0000000ece600000] [id=16540]
  0x00000160056abf40 GCTaskThread "GC Thread#8" [stack: 0x0000000ece600000,0x0000000ece700000] [id=5732]
  0x00000160056ac610 GCTaskThread "GC Thread#9" [stack: 0x0000000ece700000,0x0000000ece800000] [id=13248]
  0x000001600549cf70 GCTaskThread "GC Thread#10" [stack: 0x0000000ece800000,0x0000000ece900000] [id=13936]
  0x00000160051c6a20 GCTaskThread "GC Thread#11" [stack: 0x0000000ece900000,0x0000000ecea00000] [id=26064]
  0x0000016008b1aff0 GCTaskThread "GC Thread#12" [stack: 0x0000000ecff00000,0x0000000ed0000000] [id=23628]
  0x0000015fa3074430 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000000eccd00000,0x0000000ecce00000] [id=19752]
  0x0000015fa3076780 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000000ecce00000,0x0000000eccf00000] [id=6124]
  0x0000016005c23090 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000000ed0000000,0x0000000ed0100000] [id=23636]
  0x0000016005c238d0 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000000ed0100000,0x0000000ed0200000] [id=23640]
  0x0000015fa30de950 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000000eccf00000,0x0000000ecd000000] [id=32428]
  0x000001600a7deb20 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000000ed6e00000,0x0000000ed6f00000] [id=18460]
  0x00000160086d9500 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000000ed6700000,0x0000000ed6800000] [id=43984]
  0x0000016008605010 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000000ed6800000,0x0000000ed6900000] [id=41096]
  0x0000016009fd9a20 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000000ed6900000,0x0000000ed6a00000] [id=47216]
  0x0000015fbf90ca50 ConcurrentGCThread "G1 Service" [stack: 0x0000000ecd000000,0x0000000ecd100000] [id=13028]

Threads with active compile tasks:
C2 CompilerThread1   184952 19908 %     4       org.jetbrains.kotlin.gradle.internal.kapt.incremental.ClasspathEntryData::writeObject @ 441 (1063 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000015fc0000000-0x0000015fc0bb0000-0x0000015fc0bb0000), size 12255232, SharedBaseAddress: 0x0000015fc0000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000015fc1000000-0x0000016001000000, reserved size: 1073741824
Narrow klass base: 0x0000015fc0000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32038M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 502M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 308224K, used 200511K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 4 survivors (4096K)
 Metaspace       used 109016K, committed 110144K, reserved 1179648K
  class space    used 15074K, committed 15680K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000c0000000, 0x00000000c0100000, 0x00000000c0100000|100%| O|  |TAMS 0x00000000c0100000, 0x00000000c0000000| Untracked 
|   1|0x00000000c0100000, 0x00000000c0200000, 0x00000000c0200000|100%|HS|  |TAMS 0x00000000c0200000, 0x00000000c0100000| Complete 
|   2|0x00000000c0200000, 0x00000000c0300000, 0x00000000c0300000|100%|HC|  |TAMS 0x00000000c0300000, 0x00000000c0200000| Complete 
|   3|0x00000000c0300000, 0x00000000c0400000, 0x00000000c0400000|100%|HC|  |TAMS 0x00000000c0400000, 0x00000000c0300000| Complete 
|   4|0x00000000c0400000, 0x00000000c0500000, 0x00000000c0500000|100%| O|  |TAMS 0x00000000c0500000, 0x00000000c0400000| Untracked 
|   5|0x00000000c0500000, 0x00000000c0600000, 0x00000000c0600000|100%| O|  |TAMS 0x00000000c0600000, 0x00000000c0500000| Untracked 
|   6|0x00000000c0600000, 0x00000000c0700000, 0x00000000c0700000|100%| O|  |TAMS 0x00000000c0700000, 0x00000000c0600000| Untracked 
|   7|0x00000000c0700000, 0x00000000c0800000, 0x00000000c0800000|100%|HS|  |TAMS 0x00000000c0800000, 0x00000000c0700000| Complete 
|   8|0x00000000c0800000, 0x00000000c0900000, 0x00000000c0900000|100%| O|  |TAMS 0x00000000c0900000, 0x00000000c0800000| Untracked 
|   9|0x00000000c0900000, 0x00000000c0a00000, 0x00000000c0a00000|100%| O|  |TAMS 0x00000000c0a00000, 0x00000000c0900000| Untracked 
|  10|0x00000000c0a00000, 0x00000000c0b00000, 0x00000000c0b00000|100%| O|  |TAMS 0x00000000c0b00000, 0x00000000c0a00000| Untracked 
|  11|0x00000000c0b00000, 0x00000000c0c00000, 0x00000000c0c00000|100%| O|  |TAMS 0x00000000c0c00000, 0x00000000c0b00000| Untracked 
|  12|0x00000000c0c00000, 0x00000000c0d00000, 0x00000000c0d00000|100%| O|  |TAMS 0x00000000c0d00000, 0x00000000c0c00000| Untracked 
|  13|0x00000000c0d00000, 0x00000000c0e00000, 0x00000000c0e00000|100%| O|  |TAMS 0x00000000c0e00000, 0x00000000c0d00000| Untracked 
|  14|0x00000000c0e00000, 0x00000000c0f00000, 0x00000000c0f00000|100%| O|  |TAMS 0x00000000c0f00000, 0x00000000c0e00000| Untracked 
|  15|0x00000000c0f00000, 0x00000000c1000000, 0x00000000c1000000|100%| O|  |TAMS 0x00000000c1000000, 0x00000000c0f00000| Untracked 
|  16|0x00000000c1000000, 0x00000000c1100000, 0x00000000c1100000|100%| O|  |TAMS 0x00000000c1100000, 0x00000000c1000000| Untracked 
|  17|0x00000000c1100000, 0x00000000c1200000, 0x00000000c1200000|100%| O|  |TAMS 0x00000000c1200000, 0x00000000c1100000| Untracked 
|  18|0x00000000c1200000, 0x00000000c1300000, 0x00000000c1300000|100%| O|  |TAMS 0x00000000c1300000, 0x00000000c1200000| Untracked 
|  19|0x00000000c1300000, 0x00000000c1400000, 0x00000000c1400000|100%| O|  |TAMS 0x00000000c1400000, 0x00000000c1300000| Untracked 
|  20|0x00000000c1400000, 0x00000000c1500000, 0x00000000c1500000|100%| O|  |TAMS 0x00000000c1500000, 0x00000000c1400000| Untracked 
|  21|0x00000000c1500000, 0x00000000c1600000, 0x00000000c1600000|100%| O|  |TAMS 0x00000000c1600000, 0x00000000c1500000| Untracked 
|  22|0x00000000c1600000, 0x00000000c1700000, 0x00000000c1700000|100%| O|  |TAMS 0x00000000c1700000, 0x00000000c1600000| Untracked 
|  23|0x00000000c1700000, 0x00000000c1800000, 0x00000000c1800000|100%| O|  |TAMS 0x00000000c1800000, 0x00000000c1700000| Untracked 
|  24|0x00000000c1800000, 0x00000000c1900000, 0x00000000c1900000|100%| O|  |TAMS 0x00000000c1900000, 0x00000000c1800000| Untracked 
|  25|0x00000000c1900000, 0x00000000c1a00000, 0x00000000c1a00000|100%| O|  |TAMS 0x00000000c1a00000, 0x00000000c1900000| Untracked 
|  26|0x00000000c1a00000, 0x00000000c1b00000, 0x00000000c1b00000|100%| O|  |TAMS 0x00000000c1b00000, 0x00000000c1a00000| Untracked 
|  27|0x00000000c1b00000, 0x00000000c1c00000, 0x00000000c1c00000|100%| O|  |TAMS 0x00000000c1c00000, 0x00000000c1b00000| Untracked 
|  28|0x00000000c1c00000, 0x00000000c1d00000, 0x00000000c1d00000|100%| O|  |TAMS 0x00000000c1d00000, 0x00000000c1c00000| Untracked 
|  29|0x00000000c1d00000, 0x00000000c1e00000, 0x00000000c1e00000|100%|HS|  |TAMS 0x00000000c1e00000, 0x00000000c1d00000| Complete 
|  30|0x00000000c1e00000, 0x00000000c1f00000, 0x00000000c1f00000|100%| O|  |TAMS 0x00000000c1f00000, 0x00000000c1e00000| Untracked 
|  31|0x00000000c1f00000, 0x00000000c2000000, 0x00000000c2000000|100%| O|  |TAMS 0x00000000c2000000, 0x00000000c1f00000| Untracked 
|  32|0x00000000c2000000, 0x00000000c2100000, 0x00000000c2100000|100%| O|  |TAMS 0x00000000c2100000, 0x00000000c2000000| Untracked 
|  33|0x00000000c2100000, 0x00000000c2200000, 0x00000000c2200000|100%| O|  |TAMS 0x00000000c2200000, 0x00000000c2100000| Untracked 
|  34|0x00000000c2200000, 0x00000000c2300000, 0x00000000c2300000|100%| O|  |TAMS 0x00000000c2300000, 0x00000000c2200000| Untracked 
|  35|0x00000000c2300000, 0x00000000c2400000, 0x00000000c2400000|100%| O|  |TAMS 0x00000000c2400000, 0x00000000c2300000| Untracked 
|  36|0x00000000c2400000, 0x00000000c2500000, 0x00000000c2500000|100%| O|  |TAMS 0x00000000c2500000, 0x00000000c2400000| Untracked 
|  37|0x00000000c2500000, 0x00000000c2600000, 0x00000000c2600000|100%| O|  |TAMS 0x00000000c2600000, 0x00000000c2500000| Untracked 
|  38|0x00000000c2600000, 0x00000000c2700000, 0x00000000c2700000|100%| O|  |TAMS 0x00000000c2700000, 0x00000000c2600000| Untracked 
|  39|0x00000000c2700000, 0x00000000c2800000, 0x00000000c2800000|100%| O|  |TAMS 0x00000000c2800000, 0x00000000c2700000| Untracked 
|  40|0x00000000c2800000, 0x00000000c2900000, 0x00000000c2900000|100%| O|  |TAMS 0x00000000c2900000, 0x00000000c2800000| Untracked 
|  41|0x00000000c2900000, 0x00000000c2a00000, 0x00000000c2a00000|100%| O|  |TAMS 0x00000000c2a00000, 0x00000000c2900000| Untracked 
|  42|0x00000000c2a00000, 0x00000000c2b00000, 0x00000000c2b00000|100%| O|  |TAMS 0x00000000c2b00000, 0x00000000c2a00000| Untracked 
|  43|0x00000000c2b00000, 0x00000000c2c00000, 0x00000000c2c00000|100%| O|  |TAMS 0x00000000c2b00000, 0x00000000c2b00000| Untracked 
|  44|0x00000000c2c00000, 0x00000000c2d00000, 0x00000000c2d00000|100%| O|  |TAMS 0x00000000c2d00000, 0x00000000c2c00000| Untracked 
|  45|0x00000000c2d00000, 0x00000000c2e00000, 0x00000000c2e00000|100%| O|  |TAMS 0x00000000c2e00000, 0x00000000c2d00000| Untracked 
|  46|0x00000000c2e00000, 0x00000000c2f00000, 0x00000000c2f00000|100%| O|  |TAMS 0x00000000c2f00000, 0x00000000c2e00000| Untracked 
|  47|0x00000000c2f00000, 0x00000000c3000000, 0x00000000c3000000|100%| O|  |TAMS 0x00000000c3000000, 0x00000000c2f00000| Untracked 
|  48|0x00000000c3000000, 0x00000000c3100000, 0x00000000c3100000|100%| O|  |TAMS 0x00000000c3100000, 0x00000000c3000000| Untracked 
|  49|0x00000000c3100000, 0x00000000c3200000, 0x00000000c3200000|100%| O|  |TAMS 0x00000000c3200000, 0x00000000c3100000| Untracked 
|  50|0x00000000c3200000, 0x00000000c3300000, 0x00000000c3300000|100%|HS|  |TAMS 0x00000000c3300000, 0x00000000c3200000| Complete 
|  51|0x00000000c3300000, 0x00000000c3400000, 0x00000000c3400000|100%| O|  |TAMS 0x00000000c3400000, 0x00000000c3300000| Untracked 
|  52|0x00000000c3400000, 0x00000000c3500000, 0x00000000c3500000|100%| O|  |TAMS 0x00000000c3400000, 0x00000000c3400000| Untracked 
|  53|0x00000000c3500000, 0x00000000c3600000, 0x00000000c3600000|100%| O|  |TAMS 0x00000000c3600000, 0x00000000c3500000| Untracked 
|  54|0x00000000c3600000, 0x00000000c3700000, 0x00000000c3700000|100%|HS|  |TAMS 0x00000000c3700000, 0x00000000c3600000| Complete 
|  55|0x00000000c3700000, 0x00000000c3800000, 0x00000000c3800000|100%|HC|  |TAMS 0x00000000c3800000, 0x00000000c3700000| Complete 
|  56|0x00000000c3800000, 0x00000000c3900000, 0x00000000c3900000|100%|HS|  |TAMS 0x00000000c3900000, 0x00000000c3800000| Complete 
|  57|0x00000000c3900000, 0x00000000c3a00000, 0x00000000c3a00000|100%|HC|  |TAMS 0x00000000c3a00000, 0x00000000c3900000| Complete 
|  58|0x00000000c3a00000, 0x00000000c3b00000, 0x00000000c3b00000|100%|HS|  |TAMS 0x00000000c3b00000, 0x00000000c3a00000| Complete 
|  59|0x00000000c3b00000, 0x00000000c3c00000, 0x00000000c3c00000|100%|HS|  |TAMS 0x00000000c3c00000, 0x00000000c3b00000| Complete 
|  60|0x00000000c3c00000, 0x00000000c3d00000, 0x00000000c3d00000|100%|HC|  |TAMS 0x00000000c3d00000, 0x00000000c3c00000| Complete 
|  61|0x00000000c3d00000, 0x00000000c3e00000, 0x00000000c3e00000|100%|HC|  |TAMS 0x00000000c3e00000, 0x00000000c3d00000| Complete 
|  62|0x00000000c3e00000, 0x00000000c3f00000, 0x00000000c3f00000|100%|HS|  |TAMS 0x00000000c3f00000, 0x00000000c3e00000| Complete 
|  63|0x00000000c3f00000, 0x00000000c4000000, 0x00000000c4000000|100%| O|  |TAMS 0x00000000c4000000, 0x00000000c3f00000| Untracked 
|  64|0x00000000c4000000, 0x00000000c4100000, 0x00000000c4100000|100%|HS|  |TAMS 0x00000000c4100000, 0x00000000c4000000| Complete 
|  65|0x00000000c4100000, 0x00000000c4200000, 0x00000000c4200000|100%|HS|  |TAMS 0x00000000c4200000, 0x00000000c4100000| Complete 
|  66|0x00000000c4200000, 0x00000000c4300000, 0x00000000c4300000|100%| O|  |TAMS 0x00000000c4300000, 0x00000000c4200000| Untracked 
|  67|0x00000000c4300000, 0x00000000c4400000, 0x00000000c4400000|100%| O|  |TAMS 0x00000000c4400000, 0x00000000c4300000| Untracked 
|  68|0x00000000c4400000, 0x00000000c4500000, 0x00000000c4500000|100%| O|  |TAMS 0x00000000c4500000, 0x00000000c4400000| Untracked 
|  69|0x00000000c4500000, 0x00000000c4600000, 0x00000000c4600000|100%|HS|  |TAMS 0x00000000c4600000, 0x00000000c4500000| Complete 
|  70|0x00000000c4600000, 0x00000000c4700000, 0x00000000c4700000|100%|HS|  |TAMS 0x00000000c4700000, 0x00000000c4600000| Complete 
|  71|0x00000000c4700000, 0x00000000c4800000, 0x00000000c4800000|100%|HC|  |TAMS 0x00000000c4800000, 0x00000000c4700000| Complete 
|  72|0x00000000c4800000, 0x00000000c4900000, 0x00000000c4900000|100%|HC|  |TAMS 0x00000000c4900000, 0x00000000c4800000| Complete 
|  73|0x00000000c4900000, 0x00000000c4a00000, 0x00000000c4a00000|100%| O|  |TAMS 0x00000000c4a00000, 0x00000000c4900000| Untracked 
|  74|0x00000000c4a00000, 0x00000000c4b00000, 0x00000000c4b00000|100%| O|  |TAMS 0x00000000c4b00000, 0x00000000c4a00000| Untracked 
|  75|0x00000000c4b00000, 0x00000000c4c00000, 0x00000000c4c00000|100%| O|  |TAMS 0x00000000c4c00000, 0x00000000c4b00000| Untracked 
|  76|0x00000000c4c00000, 0x00000000c4d00000, 0x00000000c4d00000|100%| O|  |TAMS 0x00000000c4d00000, 0x00000000c4c00000| Untracked 
|  77|0x00000000c4d00000, 0x00000000c4e00000, 0x00000000c4e00000|100%| O|  |TAMS 0x00000000c4e00000, 0x00000000c4d00000| Untracked 
|  78|0x00000000c4e00000, 0x00000000c4f00000, 0x00000000c4f00000|100%| O|  |TAMS 0x00000000c4f00000, 0x00000000c4e00000| Untracked 
|  79|0x00000000c4f00000, 0x00000000c5000000, 0x00000000c5000000|100%| O|  |TAMS 0x00000000c5000000, 0x00000000c4f00000| Untracked 
|  80|0x00000000c5000000, 0x00000000c5100000, 0x00000000c5100000|100%| O|  |TAMS 0x00000000c5100000, 0x00000000c5000000| Untracked 
|  81|0x00000000c5100000, 0x00000000c5200000, 0x00000000c5200000|100%| O|  |TAMS 0x00000000c5200000, 0x00000000c5100000| Untracked 
|  82|0x00000000c5200000, 0x00000000c5300000, 0x00000000c5300000|100%| O|  |TAMS 0x00000000c5300000, 0x00000000c5200000| Untracked 
|  83|0x00000000c5300000, 0x00000000c5400000, 0x00000000c5400000|100%| O|  |TAMS 0x00000000c5400000, 0x00000000c5300000| Untracked 
|  84|0x00000000c5400000, 0x00000000c5500000, 0x00000000c5500000|100%| O|  |TAMS 0x00000000c5500000, 0x00000000c5400000| Untracked 
|  85|0x00000000c5500000, 0x00000000c5600000, 0x00000000c5600000|100%| O|  |TAMS 0x00000000c5600000, 0x00000000c5500000| Untracked 
|  86|0x00000000c5600000, 0x00000000c5700000, 0x00000000c5700000|100%| O|  |TAMS 0x00000000c5700000, 0x00000000c5600000| Untracked 
|  87|0x00000000c5700000, 0x00000000c5800000, 0x00000000c5800000|100%| O|  |TAMS 0x00000000c5800000, 0x00000000c5700000| Untracked 
|  88|0x00000000c5800000, 0x00000000c5900000, 0x00000000c5900000|100%| O|  |TAMS 0x00000000c5900000, 0x00000000c5800000| Untracked 
|  89|0x00000000c5900000, 0x00000000c5a00000, 0x00000000c5a00000|100%| O|  |TAMS 0x00000000c5a00000, 0x00000000c5900000| Untracked 
|  90|0x00000000c5a00000, 0x00000000c5b00000, 0x00000000c5b00000|100%| O|  |TAMS 0x00000000c5aba800, 0x00000000c5a00000| Untracked 
|  91|0x00000000c5b00000, 0x00000000c5c00000, 0x00000000c5c00000|100%| O|  |TAMS 0x00000000c5b00000, 0x00000000c5b00000| Untracked 
|  92|0x00000000c5c00000, 0x00000000c5d00000, 0x00000000c5d00000|100%| O|  |TAMS 0x00000000c5c00000, 0x00000000c5c00000| Untracked 
|  93|0x00000000c5d00000, 0x00000000c5e00000, 0x00000000c5e00000|100%| O|  |TAMS 0x00000000c5d00000, 0x00000000c5d00000| Untracked 
|  94|0x00000000c5e00000, 0x00000000c5f00000, 0x00000000c5f00000|100%| O|  |TAMS 0x00000000c5e00000, 0x00000000c5e00000| Untracked 
|  95|0x00000000c5f00000, 0x00000000c6000000, 0x00000000c6000000|100%| O|  |TAMS 0x00000000c5f00000, 0x00000000c5f00000| Untracked 
|  96|0x00000000c6000000, 0x00000000c6100000, 0x00000000c6100000|100%| O|  |TAMS 0x00000000c6000000, 0x00000000c6000000| Untracked 
|  97|0x00000000c6100000, 0x00000000c6200000, 0x00000000c6200000|100%| O|  |TAMS 0x00000000c6100000, 0x00000000c6100000| Untracked 
|  98|0x00000000c6200000, 0x00000000c6300000, 0x00000000c6300000|100%| O|  |TAMS 0x00000000c6200000, 0x00000000c6200000| Untracked 
|  99|0x00000000c6300000, 0x00000000c6400000, 0x00000000c6400000|100%| O|  |TAMS 0x00000000c6300000, 0x00000000c6300000| Untracked 
| 100|0x00000000c6400000, 0x00000000c6500000, 0x00000000c6500000|100%| O|  |TAMS 0x00000000c6400000, 0x00000000c6400000| Untracked 
| 101|0x00000000c6500000, 0x00000000c6600000, 0x00000000c6600000|100%| O|  |TAMS 0x00000000c6500000, 0x00000000c6500000| Untracked 
| 102|0x00000000c6600000, 0x00000000c6700000, 0x00000000c6700000|100%| O|  |TAMS 0x00000000c6600000, 0x00000000c6600000| Untracked 
| 103|0x00000000c6700000, 0x00000000c6800000, 0x00000000c6800000|100%| O|  |TAMS 0x00000000c6700000, 0x00000000c6700000| Untracked 
| 104|0x00000000c6800000, 0x00000000c6900000, 0x00000000c6900000|100%| O|  |TAMS 0x00000000c6800000, 0x00000000c6800000| Untracked 
| 105|0x00000000c6900000, 0x00000000c6a00000, 0x00000000c6a00000|100%| O|  |TAMS 0x00000000c6900000, 0x00000000c6900000| Untracked 
| 106|0x00000000c6a00000, 0x00000000c6b00000, 0x00000000c6b00000|100%| O|  |TAMS 0x00000000c6a00000, 0x00000000c6a00000| Untracked 
| 107|0x00000000c6b00000, 0x00000000c6c00000, 0x00000000c6c00000|100%| O|  |TAMS 0x00000000c6b00000, 0x00000000c6b00000| Untracked 
| 108|0x00000000c6c00000, 0x00000000c6d00000, 0x00000000c6d00000|100%| O|  |TAMS 0x00000000c6c00000, 0x00000000c6c00000| Untracked 
| 109|0x00000000c6d00000, 0x00000000c6e00000, 0x00000000c6e00000|100%| O|  |TAMS 0x00000000c6d00000, 0x00000000c6d00000| Untracked 
| 110|0x00000000c6e00000, 0x00000000c6f00000, 0x00000000c6f00000|100%| O|  |TAMS 0x00000000c6e00000, 0x00000000c6e00000| Untracked 
| 111|0x00000000c6f00000, 0x00000000c7000000, 0x00000000c7000000|100%| O|  |TAMS 0x00000000c6f00000, 0x00000000c6f00000| Untracked 
| 112|0x00000000c7000000, 0x00000000c7100000, 0x00000000c7100000|100%| O|  |TAMS 0x00000000c7000000, 0x00000000c7000000| Untracked 
| 113|0x00000000c7100000, 0x00000000c7200000, 0x00000000c7200000|100%| O|  |TAMS 0x00000000c7100000, 0x00000000c7100000| Untracked 
| 114|0x00000000c7200000, 0x00000000c7300000, 0x00000000c7300000|100%| O|  |TAMS 0x00000000c7200000, 0x00000000c7200000| Untracked 
| 115|0x00000000c7300000, 0x00000000c7400000, 0x00000000c7400000|100%| O|  |TAMS 0x00000000c7300000, 0x00000000c7300000| Untracked 
| 116|0x00000000c7400000, 0x00000000c7500000, 0x00000000c7500000|100%| O|  |TAMS 0x00000000c7400000, 0x00000000c7400000| Untracked 
| 117|0x00000000c7500000, 0x00000000c7600000, 0x00000000c7600000|100%| O|  |TAMS 0x00000000c7500000, 0x00000000c7500000| Untracked 
| 118|0x00000000c7600000, 0x00000000c7700000, 0x00000000c7700000|100%| O|  |TAMS 0x00000000c7600000, 0x00000000c7600000| Untracked 
| 119|0x00000000c7700000, 0x00000000c7800000, 0x00000000c7800000|100%| O|  |TAMS 0x00000000c7700000, 0x00000000c7700000| Untracked 
| 120|0x00000000c7800000, 0x00000000c7900000, 0x00000000c7900000|100%| O|  |TAMS 0x00000000c7800000, 0x00000000c7800000| Untracked 
| 121|0x00000000c7900000, 0x00000000c7a00000, 0x00000000c7a00000|100%| O|  |TAMS 0x00000000c7900000, 0x00000000c7900000| Untracked 
| 122|0x00000000c7a00000, 0x00000000c7b00000, 0x00000000c7b00000|100%| O|  |TAMS 0x00000000c7a00000, 0x00000000c7a00000| Untracked 
| 123|0x00000000c7b00000, 0x00000000c7c00000, 0x00000000c7c00000|100%| O|  |TAMS 0x00000000c7b00000, 0x00000000c7b00000| Untracked 
| 124|0x00000000c7c00000, 0x00000000c7d00000, 0x00000000c7d00000|100%| O|  |TAMS 0x00000000c7c00000, 0x00000000c7c00000| Untracked 
| 125|0x00000000c7d00000, 0x00000000c7e00000, 0x00000000c7e00000|100%| O|  |TAMS 0x00000000c7d00000, 0x00000000c7d00000| Untracked 
| 126|0x00000000c7e00000, 0x00000000c7f00000, 0x00000000c7f00000|100%| O|  |TAMS 0x00000000c7e00000, 0x00000000c7e00000| Untracked 
| 127|0x00000000c7f00000, 0x00000000c8000000, 0x00000000c8000000|100%| O|  |TAMS 0x00000000c7f00000, 0x00000000c7f00000| Untracked 
| 128|0x00000000c8000000, 0x00000000c8100000, 0x00000000c8100000|100%| O|  |TAMS 0x00000000c8000000, 0x00000000c8000000| Untracked 
| 129|0x00000000c8100000, 0x00000000c8200000, 0x00000000c8200000|100%| O|  |TAMS 0x00000000c8100000, 0x00000000c8100000| Untracked 
| 130|0x00000000c8200000, 0x00000000c8300000, 0x00000000c8300000|100%| O|  |TAMS 0x00000000c8200000, 0x00000000c8200000| Untracked 
| 131|0x00000000c8300000, 0x00000000c8400000, 0x00000000c8400000|100%| O|  |TAMS 0x00000000c8300000, 0x00000000c8300000| Untracked 
| 132|0x00000000c8400000, 0x00000000c8500000, 0x00000000c8500000|100%| O|  |TAMS 0x00000000c8400000, 0x00000000c8400000| Untracked 
| 133|0x00000000c8500000, 0x00000000c8600000, 0x00000000c8600000|100%| O|  |TAMS 0x00000000c8500000, 0x00000000c8500000| Untracked 
| 134|0x00000000c8600000, 0x00000000c8700000, 0x00000000c8700000|100%| O|  |TAMS 0x00000000c8600000, 0x00000000c8600000| Untracked 
| 135|0x00000000c8700000, 0x00000000c8800000, 0x00000000c8800000|100%| O|  |TAMS 0x00000000c8700000, 0x00000000c8700000| Untracked 
| 136|0x00000000c8800000, 0x00000000c8900000, 0x00000000c8900000|100%| O|  |TAMS 0x00000000c8800000, 0x00000000c8800000| Untracked 
| 137|0x00000000c8900000, 0x00000000c8a00000, 0x00000000c8a00000|100%| O|  |TAMS 0x00000000c8900000, 0x00000000c8900000| Untracked 
| 138|0x00000000c8a00000, 0x00000000c8b00000, 0x00000000c8b00000|100%| O|  |TAMS 0x00000000c8a00000, 0x00000000c8a00000| Untracked 
| 139|0x00000000c8b00000, 0x00000000c8c00000, 0x00000000c8c00000|100%| O|  |TAMS 0x00000000c8b00000, 0x00000000c8b00000| Untracked 
| 140|0x00000000c8c00000, 0x00000000c8d00000, 0x00000000c8d00000|100%| O|  |TAMS 0x00000000c8c00000, 0x00000000c8c00000| Untracked 
| 141|0x00000000c8d00000, 0x00000000c8e00000, 0x00000000c8e00000|100%| O|  |TAMS 0x00000000c8d00000, 0x00000000c8d00000| Untracked 
| 142|0x00000000c8e00000, 0x00000000c8f00000, 0x00000000c8f00000|100%| O|  |TAMS 0x00000000c8e00000, 0x00000000c8e00000| Untracked 
| 143|0x00000000c8f00000, 0x00000000c9000000, 0x00000000c9000000|100%| O|  |TAMS 0x00000000c8f00000, 0x00000000c8f00000| Untracked 
| 144|0x00000000c9000000, 0x00000000c9100000, 0x00000000c9100000|100%| O|  |TAMS 0x00000000c9000000, 0x00000000c9000000| Untracked 
| 145|0x00000000c9100000, 0x00000000c9200000, 0x00000000c9200000|100%| O|  |TAMS 0x00000000c9100000, 0x00000000c9100000| Untracked 
| 146|0x00000000c9200000, 0x00000000c9300000, 0x00000000c9300000|100%| O|  |TAMS 0x00000000c9200000, 0x00000000c9200000| Untracked 
| 147|0x00000000c9300000, 0x00000000c9400000, 0x00000000c9400000|100%| O|  |TAMS 0x00000000c9300000, 0x00000000c9300000| Untracked 
| 148|0x00000000c9400000, 0x00000000c9500000, 0x00000000c9500000|100%| O|  |TAMS 0x00000000c9400000, 0x00000000c9400000| Untracked 
| 149|0x00000000c9500000, 0x00000000c9600000, 0x00000000c9600000|100%| O|  |TAMS 0x00000000c9500000, 0x00000000c9500000| Untracked 
| 150|0x00000000c9600000, 0x00000000c9700000, 0x00000000c9700000|100%| O|  |TAMS 0x00000000c9600000, 0x00000000c9600000| Untracked 
| 151|0x00000000c9700000, 0x00000000c9800000, 0x00000000c9800000|100%| O|  |TAMS 0x00000000c9700000, 0x00000000c9700000| Untracked 
| 152|0x00000000c9800000, 0x00000000c9900000, 0x00000000c9900000|100%| O|  |TAMS 0x00000000c9800000, 0x00000000c9800000| Untracked 
| 153|0x00000000c9900000, 0x00000000c9a00000, 0x00000000c9a00000|100%| O|  |TAMS 0x00000000c9900000, 0x00000000c9900000| Untracked 
| 154|0x00000000c9a00000, 0x00000000c9b00000, 0x00000000c9b00000|100%| O|  |TAMS 0x00000000c9a00000, 0x00000000c9a00000| Untracked 
| 155|0x00000000c9b00000, 0x00000000c9c00000, 0x00000000c9c00000|100%| O|  |TAMS 0x00000000c9b00000, 0x00000000c9b00000| Untracked 
| 156|0x00000000c9c00000, 0x00000000c9d00000, 0x00000000c9d00000|100%| O|  |TAMS 0x00000000c9c00000, 0x00000000c9c00000| Untracked 
| 157|0x00000000c9d00000, 0x00000000c9e00000, 0x00000000c9e00000|100%| O|  |TAMS 0x00000000c9d00000, 0x00000000c9d00000| Untracked 
| 158|0x00000000c9e00000, 0x00000000c9f00000, 0x00000000c9f00000|100%| O|  |TAMS 0x00000000c9e00000, 0x00000000c9e00000| Untracked 
| 159|0x00000000c9f00000, 0x00000000ca000000, 0x00000000ca000000|100%| O|  |TAMS 0x00000000c9f00000, 0x00000000c9f00000| Untracked 
| 160|0x00000000ca000000, 0x00000000ca100000, 0x00000000ca100000|100%| O|  |TAMS 0x00000000ca000000, 0x00000000ca000000| Untracked 
| 161|0x00000000ca100000, 0x00000000ca200000, 0x00000000ca200000|100%| O|  |TAMS 0x00000000ca100000, 0x00000000ca100000| Untracked 
| 162|0x00000000ca200000, 0x00000000ca300000, 0x00000000ca300000|100%| O|  |TAMS 0x00000000ca200000, 0x00000000ca200000| Untracked 
| 163|0x00000000ca300000, 0x00000000ca400000, 0x00000000ca400000|100%| O|  |TAMS 0x00000000ca300000, 0x00000000ca300000| Untracked 
| 164|0x00000000ca400000, 0x00000000ca500000, 0x00000000ca500000|100%| O|  |TAMS 0x00000000ca400000, 0x00000000ca400000| Untracked 
| 165|0x00000000ca500000, 0x00000000ca600000, 0x00000000ca600000|100%| O|  |TAMS 0x00000000ca500000, 0x00000000ca500000| Untracked 
| 166|0x00000000ca600000, 0x00000000ca700000, 0x00000000ca700000|100%| O|  |TAMS 0x00000000ca600000, 0x00000000ca600000| Untracked 
| 167|0x00000000ca700000, 0x00000000ca800000, 0x00000000ca800000|100%| O|  |TAMS 0x00000000ca700000, 0x00000000ca700000| Untracked 
| 168|0x00000000ca800000, 0x00000000ca900000, 0x00000000ca900000|100%| O|  |TAMS 0x00000000ca800000, 0x00000000ca800000| Untracked 
| 169|0x00000000ca900000, 0x00000000caa00000, 0x00000000caa00000|100%| O|  |TAMS 0x00000000ca900000, 0x00000000ca900000| Untracked 
| 170|0x00000000caa00000, 0x00000000cab00000, 0x00000000cab00000|100%| O|  |TAMS 0x00000000caa00000, 0x00000000caa00000| Untracked 
| 171|0x00000000cab00000, 0x00000000cac00000, 0x00000000cac00000|100%| O|  |TAMS 0x00000000cab00000, 0x00000000cab00000| Untracked 
| 172|0x00000000cac00000, 0x00000000cad00000, 0x00000000cad00000|100%| O|  |TAMS 0x00000000cac00000, 0x00000000cac00000| Untracked 
| 173|0x00000000cad00000, 0x00000000cae00000, 0x00000000cae00000|100%| O|  |TAMS 0x00000000cad00000, 0x00000000cad00000| Untracked 
| 174|0x00000000cae00000, 0x00000000caf00000, 0x00000000caf00000|100%| O|  |TAMS 0x00000000cae00000, 0x00000000cae00000| Untracked 
| 175|0x00000000caf00000, 0x00000000cb000000, 0x00000000cb000000|100%| O|  |TAMS 0x00000000caf00000, 0x00000000caf00000| Untracked 
| 176|0x00000000cb000000, 0x00000000cb100000, 0x00000000cb100000|100%| O|  |TAMS 0x00000000cb000000, 0x00000000cb000000| Untracked 
| 177|0x00000000cb100000, 0x00000000cb200000, 0x00000000cb200000|100%| O|  |TAMS 0x00000000cb100000, 0x00000000cb100000| Untracked 
| 178|0x00000000cb200000, 0x00000000cb300000, 0x00000000cb300000|100%| O|  |TAMS 0x00000000cb200000, 0x00000000cb200000| Untracked 
| 179|0x00000000cb300000, 0x00000000cb400000, 0x00000000cb400000|100%| O|  |TAMS 0x00000000cb300000, 0x00000000cb300000| Untracked 
| 180|0x00000000cb400000, 0x00000000cb500000, 0x00000000cb500000|100%| O|  |TAMS 0x00000000cb400000, 0x00000000cb400000| Untracked 
| 181|0x00000000cb500000, 0x00000000cb600000, 0x00000000cb600000|100%| O|  |TAMS 0x00000000cb500000, 0x00000000cb500000| Untracked 
| 182|0x00000000cb600000, 0x00000000cb700000, 0x00000000cb700000|100%| O|  |TAMS 0x00000000cb600000, 0x00000000cb600000| Untracked 
| 183|0x00000000cb700000, 0x00000000cb800000, 0x00000000cb800000|100%| O|  |TAMS 0x00000000cb700000, 0x00000000cb700000| Untracked 
| 184|0x00000000cb800000, 0x00000000cb900000, 0x00000000cb900000|100%| O|  |TAMS 0x00000000cb800000, 0x00000000cb800000| Untracked 
| 185|0x00000000cb900000, 0x00000000cba00000, 0x00000000cba00000|100%| O|  |TAMS 0x00000000cb900000, 0x00000000cb900000| Untracked 
| 186|0x00000000cba00000, 0x00000000cbb00000, 0x00000000cbb00000|100%| O|  |TAMS 0x00000000cba00000, 0x00000000cba00000| Untracked 
| 187|0x00000000cbb00000, 0x00000000cbc00000, 0x00000000cbc00000|100%| O|  |TAMS 0x00000000cbb00000, 0x00000000cbb00000| Untracked 
| 188|0x00000000cbc00000, 0x00000000cbd00000, 0x00000000cbd00000|100%| O|  |TAMS 0x00000000cbc00000, 0x00000000cbc00000| Untracked 
| 189|0x00000000cbd00000, 0x00000000cbe00000, 0x00000000cbe00000|100%| O|  |TAMS 0x00000000cbd00000, 0x00000000cbd00000| Untracked 
| 190|0x00000000cbe00000, 0x00000000cbea4c00, 0x00000000cbf00000| 64%| O|  |TAMS 0x00000000cbe00000, 0x00000000cbe00000| Untracked 
| 191|0x00000000cbf00000, 0x00000000cbf00000, 0x00000000cc000000|  0%| F|  |TAMS 0x00000000cbf00000, 0x00000000cbf00000| Untracked 
| 192|0x00000000cc000000, 0x00000000cc000000, 0x00000000cc100000|  0%| F|  |TAMS 0x00000000cc000000, 0x00000000cc000000| Untracked 
| 193|0x00000000cc100000, 0x00000000cc100000, 0x00000000cc200000|  0%| F|  |TAMS 0x00000000cc100000, 0x00000000cc100000| Untracked 
| 194|0x00000000cc200000, 0x00000000cc200000, 0x00000000cc300000|  0%| F|  |TAMS 0x00000000cc200000, 0x00000000cc200000| Untracked 
| 195|0x00000000cc300000, 0x00000000cc300000, 0x00000000cc400000|  0%| F|  |TAMS 0x00000000cc300000, 0x00000000cc300000| Untracked 
| 196|0x00000000cc400000, 0x00000000cc400000, 0x00000000cc500000|  0%| F|  |TAMS 0x00000000cc400000, 0x00000000cc400000| Untracked 
| 197|0x00000000cc500000, 0x00000000cc500000, 0x00000000cc600000|  0%| F|  |TAMS 0x00000000cc500000, 0x00000000cc500000| Untracked 
| 198|0x00000000cc600000, 0x00000000cc600000, 0x00000000cc700000|  0%| F|  |TAMS 0x00000000cc600000, 0x00000000cc600000| Untracked 
| 199|0x00000000cc700000, 0x00000000cc700000, 0x00000000cc800000|  0%| F|  |TAMS 0x00000000cc700000, 0x00000000cc700000| Untracked 
| 200|0x00000000cc800000, 0x00000000cc800000, 0x00000000cc900000|  0%| F|  |TAMS 0x00000000cc800000, 0x00000000cc800000| Untracked 
| 201|0x00000000cc900000, 0x00000000cc900000, 0x00000000cca00000|  0%| F|  |TAMS 0x00000000cc900000, 0x00000000cc900000| Untracked 
| 202|0x00000000cca00000, 0x00000000cca00000, 0x00000000ccb00000|  0%| F|  |TAMS 0x00000000cca00000, 0x00000000cca00000| Untracked 
| 203|0x00000000ccb00000, 0x00000000ccb00000, 0x00000000ccc00000|  0%| F|  |TAMS 0x00000000ccb00000, 0x00000000ccb00000| Untracked 
| 204|0x00000000ccc00000, 0x00000000ccc00000, 0x00000000ccd00000|  0%| F|  |TAMS 0x00000000ccc00000, 0x00000000ccc00000| Untracked 
| 205|0x00000000ccd00000, 0x00000000ccd00000, 0x00000000cce00000|  0%| F|  |TAMS 0x00000000ccd00000, 0x00000000ccd00000| Untracked 
| 206|0x00000000cce00000, 0x00000000cce00000, 0x00000000ccf00000|  0%| F|  |TAMS 0x00000000cce00000, 0x00000000cce00000| Untracked 
| 207|0x00000000ccf00000, 0x00000000ccf00000, 0x00000000cd000000|  0%| F|  |TAMS 0x00000000ccf00000, 0x00000000ccf00000| Untracked 
| 208|0x00000000cd000000, 0x00000000cd000000, 0x00000000cd100000|  0%| F|  |TAMS 0x00000000cd000000, 0x00000000cd000000| Untracked 
| 209|0x00000000cd100000, 0x00000000cd100000, 0x00000000cd200000|  0%| F|  |TAMS 0x00000000cd100000, 0x00000000cd100000| Untracked 
| 210|0x00000000cd200000, 0x00000000cd200000, 0x00000000cd300000|  0%| F|  |TAMS 0x00000000cd200000, 0x00000000cd200000| Untracked 
| 211|0x00000000cd300000, 0x00000000cd300000, 0x00000000cd400000|  0%| F|  |TAMS 0x00000000cd300000, 0x00000000cd300000| Untracked 
| 212|0x00000000cd400000, 0x00000000cd400000, 0x00000000cd500000|  0%| F|  |TAMS 0x00000000cd400000, 0x00000000cd400000| Untracked 
| 213|0x00000000cd500000, 0x00000000cd500000, 0x00000000cd600000|  0%| F|  |TAMS 0x00000000cd500000, 0x00000000cd500000| Untracked 
| 214|0x00000000cd600000, 0x00000000cd600000, 0x00000000cd700000|  0%| F|  |TAMS 0x00000000cd600000, 0x00000000cd600000| Untracked 
| 215|0x00000000cd700000, 0x00000000cd700000, 0x00000000cd800000|  0%| F|  |TAMS 0x00000000cd700000, 0x00000000cd700000| Untracked 
| 216|0x00000000cd800000, 0x00000000cd800000, 0x00000000cd900000|  0%| F|  |TAMS 0x00000000cd800000, 0x00000000cd800000| Untracked 
| 217|0x00000000cd900000, 0x00000000cd900000, 0x00000000cda00000|  0%| F|  |TAMS 0x00000000cd900000, 0x00000000cd900000| Untracked 
| 218|0x00000000cda00000, 0x00000000cda00000, 0x00000000cdb00000|  0%| F|  |TAMS 0x00000000cda00000, 0x00000000cda00000| Untracked 
| 219|0x00000000cdb00000, 0x00000000cdb00000, 0x00000000cdc00000|  0%| F|  |TAMS 0x00000000cdb00000, 0x00000000cdb00000| Untracked 
| 220|0x00000000cdc00000, 0x00000000cdc00000, 0x00000000cdd00000|  0%| F|  |TAMS 0x00000000cdc00000, 0x00000000cdc00000| Untracked 
| 221|0x00000000cdd00000, 0x00000000cdd00000, 0x00000000cde00000|  0%| F|  |TAMS 0x00000000cdd00000, 0x00000000cdd00000| Untracked 
| 222|0x00000000cde00000, 0x00000000cde00000, 0x00000000cdf00000|  0%| F|  |TAMS 0x00000000cde00000, 0x00000000cde00000| Untracked 
| 223|0x00000000cdf00000, 0x00000000cdf00000, 0x00000000ce000000|  0%| F|  |TAMS 0x00000000cdf00000, 0x00000000cdf00000| Untracked 
| 224|0x00000000ce000000, 0x00000000ce000000, 0x00000000ce100000|  0%| F|  |TAMS 0x00000000ce000000, 0x00000000ce000000| Untracked 
| 225|0x00000000ce100000, 0x00000000ce100000, 0x00000000ce200000|  0%| F|  |TAMS 0x00000000ce100000, 0x00000000ce100000| Untracked 
| 226|0x00000000ce200000, 0x00000000ce200000, 0x00000000ce300000|  0%| F|  |TAMS 0x00000000ce200000, 0x00000000ce200000| Untracked 
| 227|0x00000000ce300000, 0x00000000ce300000, 0x00000000ce400000|  0%| F|  |TAMS 0x00000000ce300000, 0x00000000ce300000| Untracked 
| 228|0x00000000ce400000, 0x00000000ce400000, 0x00000000ce500000|  0%| F|  |TAMS 0x00000000ce400000, 0x00000000ce400000| Untracked 
| 229|0x00000000ce500000, 0x00000000ce500000, 0x00000000ce600000|  0%| F|  |TAMS 0x00000000ce500000, 0x00000000ce500000| Untracked 
| 230|0x00000000ce600000, 0x00000000ce600000, 0x00000000ce700000|  0%| F|  |TAMS 0x00000000ce600000, 0x00000000ce600000| Untracked 
| 231|0x00000000ce700000, 0x00000000ce700000, 0x00000000ce800000|  0%| F|  |TAMS 0x00000000ce700000, 0x00000000ce700000| Untracked 
| 232|0x00000000ce800000, 0x00000000ce800000, 0x00000000ce900000|  0%| F|  |TAMS 0x00000000ce800000, 0x00000000ce800000| Untracked 
| 233|0x00000000ce900000, 0x00000000ce900000, 0x00000000cea00000|  0%| F|  |TAMS 0x00000000ce900000, 0x00000000ce900000| Untracked 
| 234|0x00000000cea00000, 0x00000000cea00000, 0x00000000ceb00000|  0%| F|  |TAMS 0x00000000cea00000, 0x00000000cea00000| Untracked 
| 235|0x00000000ceb00000, 0x00000000ceb00000, 0x00000000cec00000|  0%| F|  |TAMS 0x00000000ceb00000, 0x00000000ceb00000| Untracked 
| 236|0x00000000cec00000, 0x00000000cec00000, 0x00000000ced00000|  0%| F|  |TAMS 0x00000000cec00000, 0x00000000cec00000| Untracked 
| 237|0x00000000ced00000, 0x00000000ced00000, 0x00000000cee00000|  0%| F|  |TAMS 0x00000000ced00000, 0x00000000ced00000| Untracked 
| 238|0x00000000cee00000, 0x00000000cee00000, 0x00000000cef00000|  0%| F|  |TAMS 0x00000000cee00000, 0x00000000cee00000| Untracked 
| 239|0x00000000cef00000, 0x00000000cef00000, 0x00000000cf000000|  0%| F|  |TAMS 0x00000000cef00000, 0x00000000cef00000| Untracked 
| 240|0x00000000cf000000, 0x00000000cf000000, 0x00000000cf100000|  0%| F|  |TAMS 0x00000000cf000000, 0x00000000cf000000| Untracked 
| 241|0x00000000cf100000, 0x00000000cf100000, 0x00000000cf200000|  0%| F|  |TAMS 0x00000000cf100000, 0x00000000cf100000| Untracked 
| 242|0x00000000cf200000, 0x00000000cf200000, 0x00000000cf300000|  0%| F|  |TAMS 0x00000000cf200000, 0x00000000cf200000| Untracked 
| 243|0x00000000cf300000, 0x00000000cf300000, 0x00000000cf400000|  0%| F|  |TAMS 0x00000000cf300000, 0x00000000cf300000| Untracked 
| 244|0x00000000cf400000, 0x00000000cf400000, 0x00000000cf500000|  0%| F|  |TAMS 0x00000000cf400000, 0x00000000cf400000| Untracked 
| 245|0x00000000cf500000, 0x00000000cf52b0b0, 0x00000000cf600000| 16%| S|CS|TAMS 0x00000000cf500000, 0x00000000cf500000| Complete 
| 246|0x00000000cf600000, 0x00000000cf700000, 0x00000000cf700000|100%| S|CS|TAMS 0x00000000cf600000, 0x00000000cf600000| Complete 
| 247|0x00000000cf700000, 0x00000000cf800000, 0x00000000cf800000|100%| S|CS|TAMS 0x00000000cf700000, 0x00000000cf700000| Complete 
| 248|0x00000000cf800000, 0x00000000cf900000, 0x00000000cf900000|100%| S|CS|TAMS 0x00000000cf800000, 0x00000000cf800000| Complete 
| 249|0x00000000cf900000, 0x00000000cf900000, 0x00000000cfa00000|  0%| F|  |TAMS 0x00000000cf900000, 0x00000000cf900000| Untracked 
| 250|0x00000000cfa00000, 0x00000000cfa00000, 0x00000000cfb00000|  0%| F|  |TAMS 0x00000000cfa00000, 0x00000000cfa00000| Untracked 
| 251|0x00000000cfb00000, 0x00000000cfb00000, 0x00000000cfc00000|  0%| F|  |TAMS 0x00000000cfb00000, 0x00000000cfb00000| Untracked 
| 252|0x00000000cfc00000, 0x00000000cfc00000, 0x00000000cfd00000|  0%| F|  |TAMS 0x00000000cfc00000, 0x00000000cfc00000| Untracked 
| 253|0x00000000cfd00000, 0x00000000cfd00000, 0x00000000cfe00000|  0%| F|  |TAMS 0x00000000cfd00000, 0x00000000cfd00000| Untracked 
| 254|0x00000000cfe00000, 0x00000000cfe00000, 0x00000000cff00000|  0%| F|  |TAMS 0x00000000cfe00000, 0x00000000cfe00000| Untracked 
| 255|0x00000000cff00000, 0x00000000cff00000, 0x00000000d0000000|  0%| F|  |TAMS 0x00000000cff00000, 0x00000000cff00000| Untracked 
| 256|0x00000000d0000000, 0x00000000d0000000, 0x00000000d0100000|  0%| F|  |TAMS 0x00000000d0000000, 0x00000000d0000000| Untracked 
| 257|0x00000000d0100000, 0x00000000d0100000, 0x00000000d0200000|  0%| F|  |TAMS 0x00000000d0100000, 0x00000000d0100000| Untracked 
| 258|0x00000000d0200000, 0x00000000d0200000, 0x00000000d0300000|  0%| F|  |TAMS 0x00000000d0200000, 0x00000000d0200000| Untracked 
| 259|0x00000000d0300000, 0x00000000d0300000, 0x00000000d0400000|  0%| F|  |TAMS 0x00000000d0300000, 0x00000000d0300000| Untracked 
| 260|0x00000000d0400000, 0x00000000d0400000, 0x00000000d0500000|  0%| F|  |TAMS 0x00000000d0400000, 0x00000000d0400000| Untracked 
| 261|0x00000000d0500000, 0x00000000d0500000, 0x00000000d0600000|  0%| F|  |TAMS 0x00000000d0500000, 0x00000000d0500000| Untracked 
| 262|0x00000000d0600000, 0x00000000d0600000, 0x00000000d0700000|  0%| F|  |TAMS 0x00000000d0600000, 0x00000000d0600000| Untracked 
| 263|0x00000000d0700000, 0x00000000d0700000, 0x00000000d0800000|  0%| F|  |TAMS 0x00000000d0700000, 0x00000000d0700000| Untracked 
| 264|0x00000000d0800000, 0x00000000d0800000, 0x00000000d0900000|  0%| F|  |TAMS 0x00000000d0800000, 0x00000000d0800000| Untracked 
| 265|0x00000000d0900000, 0x00000000d0900000, 0x00000000d0a00000|  0%| F|  |TAMS 0x00000000d0900000, 0x00000000d0900000| Untracked 
| 266|0x00000000d0a00000, 0x00000000d0a00000, 0x00000000d0b00000|  0%| F|  |TAMS 0x00000000d0a00000, 0x00000000d0a00000| Untracked 
| 267|0x00000000d0b00000, 0x00000000d0b00000, 0x00000000d0c00000|  0%| F|  |TAMS 0x00000000d0b00000, 0x00000000d0b00000| Untracked 
| 268|0x00000000d0c00000, 0x00000000d0c00000, 0x00000000d0d00000|  0%| F|  |TAMS 0x00000000d0c00000, 0x00000000d0c00000| Untracked 
| 269|0x00000000d0d00000, 0x00000000d0d00000, 0x00000000d0e00000|  0%| F|  |TAMS 0x00000000d0d00000, 0x00000000d0d00000| Untracked 
| 270|0x00000000d0e00000, 0x00000000d0e00000, 0x00000000d0f00000|  0%| F|  |TAMS 0x00000000d0e00000, 0x00000000d0e00000| Untracked 
| 271|0x00000000d0f00000, 0x00000000d0f00000, 0x00000000d1000000|  0%| F|  |TAMS 0x00000000d0f00000, 0x00000000d0f00000| Untracked 
| 272|0x00000000d1000000, 0x00000000d1000000, 0x00000000d1100000|  0%| F|  |TAMS 0x00000000d1000000, 0x00000000d1000000| Untracked 
| 273|0x00000000d1100000, 0x00000000d1100000, 0x00000000d1200000|  0%| F|  |TAMS 0x00000000d1100000, 0x00000000d1100000| Untracked 
| 274|0x00000000d1200000, 0x00000000d1200000, 0x00000000d1300000|  0%| F|  |TAMS 0x00000000d1200000, 0x00000000d1200000| Untracked 
| 275|0x00000000d1300000, 0x00000000d1300000, 0x00000000d1400000|  0%| F|  |TAMS 0x00000000d1300000, 0x00000000d1300000| Untracked 
| 276|0x00000000d1400000, 0x00000000d1400000, 0x00000000d1500000|  0%| F|  |TAMS 0x00000000d1400000, 0x00000000d1400000| Untracked 
| 277|0x00000000d1500000, 0x00000000d1500000, 0x00000000d1600000|  0%| F|  |TAMS 0x00000000d1500000, 0x00000000d1500000| Untracked 
| 278|0x00000000d1600000, 0x00000000d1600000, 0x00000000d1700000|  0%| F|  |TAMS 0x00000000d1600000, 0x00000000d1600000| Untracked 
| 279|0x00000000d1700000, 0x00000000d1700000, 0x00000000d1800000|  0%| F|  |TAMS 0x00000000d1700000, 0x00000000d1700000| Untracked 
| 280|0x00000000d1800000, 0x00000000d1800000, 0x00000000d1900000|  0%| F|  |TAMS 0x00000000d1800000, 0x00000000d1800000| Untracked 
| 281|0x00000000d1900000, 0x00000000d1900000, 0x00000000d1a00000|  0%| F|  |TAMS 0x00000000d1900000, 0x00000000d1900000| Untracked 
| 282|0x00000000d1a00000, 0x00000000d1a00000, 0x00000000d1b00000|  0%| F|  |TAMS 0x00000000d1a00000, 0x00000000d1a00000| Untracked 
| 283|0x00000000d1b00000, 0x00000000d1b00000, 0x00000000d1c00000|  0%| F|  |TAMS 0x00000000d1b00000, 0x00000000d1b00000| Untracked 
| 284|0x00000000d1c00000, 0x00000000d1c00000, 0x00000000d1d00000|  0%| F|  |TAMS 0x00000000d1c00000, 0x00000000d1c00000| Untracked 
| 285|0x00000000d1d00000, 0x00000000d1d00000, 0x00000000d1e00000|  0%| F|  |TAMS 0x00000000d1d00000, 0x00000000d1d00000| Untracked 
| 286|0x00000000d1e00000, 0x00000000d1e00000, 0x00000000d1f00000|  0%| F|  |TAMS 0x00000000d1e00000, 0x00000000d1e00000| Untracked 
| 287|0x00000000d1f00000, 0x00000000d1f00000, 0x00000000d2000000|  0%| F|  |TAMS 0x00000000d1f00000, 0x00000000d1f00000| Untracked 
| 382|0x00000000d7e00000, 0x00000000d7e00000, 0x00000000d7f00000|  0%| F|  |TAMS 0x00000000d7e00000, 0x00000000d7e00000| Untracked 
| 383|0x00000000d7f00000, 0x00000000d7f00000, 0x00000000d8000000|  0%| F|  |TAMS 0x00000000d7f00000, 0x00000000d7f00000| Untracked 
| 384|0x00000000d8000000, 0x00000000d8000000, 0x00000000d8100000|  0%| F|  |TAMS 0x00000000d8000000, 0x00000000d8000000| Untracked 
| 385|0x00000000d8100000, 0x00000000d8100000, 0x00000000d8200000|  0%| F|  |TAMS 0x00000000d8100000, 0x00000000d8100000| Untracked 
| 386|0x00000000d8200000, 0x00000000d8200000, 0x00000000d8300000|  0%| F|  |TAMS 0x00000000d8200000, 0x00000000d8200000| Untracked 
| 387|0x00000000d8300000, 0x00000000d8300000, 0x00000000d8400000|  0%| F|  |TAMS 0x00000000d8300000, 0x00000000d8300000| Untracked 
| 388|0x00000000d8400000, 0x00000000d8400000, 0x00000000d8500000|  0%| F|  |TAMS 0x00000000d8400000, 0x00000000d8400000| Untracked 
| 389|0x00000000d8500000, 0x00000000d8500000, 0x00000000d8600000|  0%| F|  |TAMS 0x00000000d8500000, 0x00000000d8500000| Untracked 
| 390|0x00000000d8600000, 0x00000000d8600000, 0x00000000d8700000|  0%| F|  |TAMS 0x00000000d8600000, 0x00000000d8600000| Untracked 
| 391|0x00000000d8700000, 0x00000000d8700000, 0x00000000d8800000|  0%| F|  |TAMS 0x00000000d8700000, 0x00000000d8700000| Untracked 
| 392|0x00000000d8800000, 0x00000000d88be880, 0x00000000d8900000| 74%| E|  |TAMS 0x00000000d8800000, 0x00000000d8800000| Complete 
| 393|0x00000000d8900000, 0x00000000d8a00000, 0x00000000d8a00000|100%| E|CS|TAMS 0x00000000d8900000, 0x00000000d8900000| Complete 
| 501|0x00000000df500000, 0x00000000df600000, 0x00000000df600000|100%| E|CS|TAMS 0x00000000df500000, 0x00000000df500000| Complete 

Card table byte_map: [0x0000015fba5e0000,0x0000015fba7e0000] _byte_map_base: 0x0000015fb9fe0000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000015fa3069990, (CMBitMap*) 0x0000015fa30699d0
 Prev Bits: [0x0000015fba9e0000, 0x0000015fbb9e0000)
 Next Bits: [0x0000015fbb9e0000, 0x0000015fbc9e0000)

Polling page: 0x0000015fa0ec0000

Metaspace:

Usage:
  Non-class:     91.74 MB used.
      Class:     14.72 MB used.
       Both:    106.46 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      92.25 MB ( 72%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      15.31 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,     107.56 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  3.57 MB
       Class:  762.00 KB
        Both:  4.31 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 160.12 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1838.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1721.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 6661.
num_chunk_merges: 9.
num_chunk_splits: 4172.
num_chunks_enlarged: 2521.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=13579Kb max_used=13579Kb free=105588Kb
 bounds [0x0000015fb2700000, 0x0000015fb3490000, 0x0000015fb9b60000]
CodeHeap 'profiled nmethods': size=119104Kb used=30440Kb max_used=31619Kb free=88663Kb
 bounds [0x0000015faab60000, 0x0000015faca50000, 0x0000015fb1fb0000]
CodeHeap 'non-nmethods': size=7488Kb used=1802Kb max_used=3622Kb free=5685Kb
 bounds [0x0000015fb1fb0000, 0x0000015fb2390000, 0x0000015fb2700000]
 total_blobs=17093 nmethods=16194 adapters=809
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 183.887 Thread 0x0000015fbfa74c00 19984       3       java.lang.String::format (16 bytes)
Event: 183.887 Thread 0x000001600c3de740 nmethod 19986 0x0000015fac1d7390 code [0x0000015fac1d7560, 0x0000015fac1d78c8]
Event: 183.887 Thread 0x000001600c0430a0 nmethod 19934 0x0000015fb27dc110 code [0x0000015fb27dc340, 0x0000015fb27dd778]
Event: 183.887 Thread 0x000001600c0430a0 19974       4       org.gradle.internal.execution.steps.IdentifyStep::createIdentityContext (38 bytes)
Event: 183.887 Thread 0x0000015fbfa74c00 nmethod 19984 0x0000015faaba4c10 code [0x0000015faaba4e40, 0x0000015faaba55c8]
Event: 183.889 Thread 0x000001600c0430a0 nmethod 19974 0x0000015fb3177610 code [0x0000015fb31777e0, 0x0000015fb3177be8]
Event: 183.889 Thread 0x000001600c0430a0 19932       4       org.gradle.internal.component.local.model.ComponentFileArtifactIdentifier::hashCode (16 bytes)
Event: 183.893 Thread 0x000001600c0430a0 nmethod 19932 0x0000015fb3176a90 code [0x0000015fb3176c20, 0x0000015fb3177158]
Event: 183.893 Thread 0x000001600c0430a0 19940       4       org.gradle.api.internal.file.CompositeFileCollection::visitContents (11 bytes)
Event: 183.894 Thread 0x000001600c0430a0 nmethod 19940 0x0000015fb2b1c010 code [0x0000015fb2b1c1a0, 0x0000015fb2b1c298]
Event: 183.894 Thread 0x000001600c0430a0 19936       4       java.util.HashSet::toArray (18 bytes)
Event: 183.897 Thread 0x000001600c0430a0 nmethod 19936 0x0000015fb2ff4e90 code [0x0000015fb2ff5020, 0x0000015fb2ff5558]
Event: 183.897 Thread 0x000001600c0430a0 19971       4       com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet::iterator (8 bytes)
Event: 183.898 Thread 0x000001600c0430a0 nmethod 19971 0x0000015fb2ff4990 code [0x0000015fb2ff4b20, 0x0000015fb2ff4cf8]
Event: 183.898 Thread 0x000001600c0430a0 19968       4       org.gradle.internal.classloader.ClassLoaderVisitor::visitSpec (1 bytes)
Event: 183.899 Thread 0x000001600c0430a0 nmethod 19968 0x0000015fb2ff4690 code [0x0000015fb2ff4800, 0x0000015fb2ff4878]
Event: 183.899 Thread 0x000001600c0430a0 19906       4       kotlin.collections.ArraysKt___ArraysJvmKt::sort (17 bytes)
Event: 183.899 Thread 0x000001600c0430a0 nmethod 19906 0x0000015fb2ff4290 code [0x0000015fb2ff4420, 0x0000015fb2ff44c8]
Event: 183.910 Thread 0x0000015fbfa72770 nmethod 19894% 0x0000015fb3484b10 code [0x0000015fb3484fa0, 0x0000015fb348bcd8]
Event: 183.917 Thread 0x000001600c0435f0 nmethod 19964 0x0000015fb3273290 code [0x0000015fb32735e0, 0x0000015fb3276140]

GC Heap History (20 events):
Event: 39.988 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 308224K, used 252789K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 132 young (135168K), 12 survivors (12288K)
 Metaspace       used 102327K, committed 103232K, reserved 1179648K
  class space    used 14283K, committed 14720K, reserved 1048576K
}
Event: 39.992 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 308224K, used 135957K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 102327K, committed 103232K, reserved 1179648K
  class space    used 14283K, committed 14720K, reserved 1048576K
}
Event: 40.572 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 308224K, used 253717K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 128 young (131072K), 12 survivors (12288K)
 Metaspace       used 102333K, committed 103232K, reserved 1179648K
  class space    used 14283K, committed 14720K, reserved 1048576K
}
Event: 40.576 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 308224K, used 138560K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 102333K, committed 103232K, reserved 1179648K
  class space    used 14283K, committed 14720K, reserved 1048576K
}
Event: 41.178 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 308224K, used 254272K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 125 young (128000K), 11 survivors (11264K)
 Metaspace       used 102376K, committed 103296K, reserved 1179648K
  class space    used 14285K, committed 14720K, reserved 1048576K
}
Event: 41.182 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 308224K, used 142931K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 102376K, committed 103296K, reserved 1179648K
  class space    used 14285K, committed 14720K, reserved 1048576K
}
Event: 163.337 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 308224K, used 255571K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 120 young (122880K), 9 survivors (9216K)
 Metaspace       used 103877K, committed 104768K, reserved 1179648K
  class space    used 14490K, committed 14912K, reserved 1048576K
}
Event: 163.342 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 308224K, used 147850K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 103877K, committed 104768K, reserved 1179648K
  class space    used 14490K, committed 14912K, reserved 1048576K
}
Event: 164.207 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 308224K, used 255370K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 117 young (119808K), 12 survivors (12288K)
 Metaspace       used 104597K, committed 105600K, reserved 1179648K
  class space    used 14537K, committed 15040K, reserved 1048576K
}
Event: 164.215 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 308224K, used 159744K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 104597K, committed 105600K, reserved 1179648K
  class space    used 14537K, committed 15040K, reserved 1048576K
}
Event: 165.646 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 308224K, used 251904K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 106 young (108544K), 15 survivors (15360K)
 Metaspace       used 106146K, committed 107136K, reserved 1179648K
  class space    used 14734K, committed 15232K, reserved 1048576K
}
Event: 165.651 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 308224K, used 165515K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 106146K, committed 107136K, reserved 1179648K
  class space    used 14734K, committed 15232K, reserved 1048576K
}
Event: 180.892 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 308224K, used 253579K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 93 young (95232K), 6 survivors (6144K)
 Metaspace       used 108510K, committed 109632K, reserved 1179648K
  class space    used 15037K, committed 15616K, reserved 1048576K
}
Event: 180.895 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 308224K, used 168571K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 108510K, committed 109632K, reserved 1179648K
  class space    used 15037K, committed 15616K, reserved 1048576K
}
Event: 181.503 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 308224K, used 256635K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 95 young (97280K), 9 survivors (9216K)
 Metaspace       used 108845K, committed 109952K, reserved 1179648K
  class space    used 15050K, committed 15616K, reserved 1048576K
}
Event: 181.509 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 308224K, used 180046K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 108845K, committed 109952K, reserved 1179648K
  class space    used 15050K, committed 15616K, reserved 1048576K
}
Event: 182.641 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 308224K, used 255822K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 86 young (88064K), 12 survivors (12288K)
 Metaspace       used 108923K, committed 110016K, reserved 1179648K
  class space    used 15060K, committed 15616K, reserved 1048576K
}
Event: 182.647 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 308224K, used 196892K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 108923K, committed 110016K, reserved 1179648K
  class space    used 15060K, committed 15616K, reserved 1048576K
}
Event: 183.743 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 308224K, used 249116K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 63 young (64512K), 11 survivors (11264K)
 Metaspace       used 108978K, committed 110080K, reserved 1179648K
  class space    used 15065K, committed 15616K, reserved 1048576K
}
Event: 183.746 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 308224K, used 198463K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 108978K, committed 110080K, reserved 1179648K
  class space    used 15065K, committed 15616K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.005 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\java.dll
Event: 0.014 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jsvml.dll
Event: 0.040 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\zip.dll
Event: 0.042 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\instrument.dll
Event: 0.046 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\net.dll
Event: 0.047 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\nio.dll
Event: 0.070 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\zip.dll
Event: 4.701 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jimage.dll
Event: 7.619 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\verify.dll
Event: 7.751 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 7.806 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 15.239 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\management.dll
Event: 15.242 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\management_ext.dll
Event: 15.419 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\extnet.dll
Event: 15.590 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.14\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 183.693 Thread 0x00000160125f57a0 Uncommon trap: trap_request=0xffffff54 fr.pc=0x0000015fb31a7b30 relative=0x0000000000000070
Event: 183.693 Thread 0x00000160125f57a0 Uncommon trap: reason=speculate_null_assert action=make_not_entrant pc=0x0000015fb31a7b30 method=org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/opera
Event: 183.693 Thread 0x00000160125f57a0 DEOPT PACKING pc=0x0000015fb31a7b30 sp=0x0000000ecfdfe9b0
Event: 183.693 Thread 0x00000160125f57a0 DEOPT UNPACKING pc=0x0000015fb200a1a3 sp=0x0000000ecfdfe9d8 mode 2
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000015fb3090d60 relative=0x0000000000001020
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000015fb3090d60 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.strategy.StaticVersionComparator.compare(Lorg/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/
Event: 183.713 Thread 0x000001600a998100 DEOPT PACKING pc=0x0000015fb3090d60 sp=0x0000000ed43fb900
Event: 183.713 Thread 0x000001600a998100 DEOPT UNPACKING pc=0x0000015fb200a1a3 sp=0x0000000ed43fb858 mode 2
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000015fb3090d60 relative=0x0000000000001020
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000015fb3090d60 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.strategy.StaticVersionComparator.compare(Lorg/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/
Event: 183.713 Thread 0x000001600a998100 DEOPT PACKING pc=0x0000015fb3090d60 sp=0x0000000ed43fb900
Event: 183.713 Thread 0x000001600a998100 DEOPT UNPACKING pc=0x0000015fb200a1a3 sp=0x0000000ed43fb858 mode 2
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000015fb3090d60 relative=0x0000000000001020
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000015fb3090d60 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.strategy.StaticVersionComparator.compare(Lorg/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/
Event: 183.713 Thread 0x000001600a998100 DEOPT PACKING pc=0x0000015fb3090d60 sp=0x0000000ed43fb870
Event: 183.713 Thread 0x000001600a998100 DEOPT UNPACKING pc=0x0000015fb200a1a3 sp=0x0000000ed43fb7c8 mode 2
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000015fb3090d60 relative=0x0000000000001020
Event: 183.713 Thread 0x000001600a998100 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000015fb3090d60 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.strategy.StaticVersionComparator.compare(Lorg/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/
Event: 183.713 Thread 0x000001600a998100 DEOPT PACKING pc=0x0000015fb3090d60 sp=0x0000000ed43fb870
Event: 183.713 Thread 0x000001600a998100 DEOPT UNPACKING pc=0x0000015fb200a1a3 sp=0x0000000ed43fb7c8 mode 2

Classes loaded (20 events):
Event: 165.845 Loading class java/lang/Process$PipeInputStream
Event: 165.845 Loading class java/lang/Process$PipeInputStream done
Event: 165.847 Loading class java/lang/ProcessBuilder$NullInputStream
Event: 165.847 Loading class java/lang/ProcessBuilder$NullInputStream done
Event: 165.847 Loading class jdk/internal/event/ProcessStartEvent
Event: 165.847 Loading class jdk/internal/event/ProcessStartEvent done
Event: 165.847 Loading class java/util/concurrent/Semaphore$NonfairSync
Event: 165.848 Loading class java/util/concurrent/Semaphore$Sync
Event: 165.848 Loading class java/util/concurrent/Semaphore$Sync done
Event: 165.848 Loading class java/util/concurrent/Semaphore$NonfairSync done
Event: 171.325 Loading class java/lang/Throwable$WrappedPrintWriter
Event: 171.326 Loading class java/lang/Throwable$PrintStreamOrWriter
Event: 171.326 Loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 171.326 Loading class java/lang/Throwable$WrappedPrintWriter done
Event: 172.480 Loading class java/util/logging/LogRecord
Event: 172.480 Loading class java/util/logging/LogRecord done
Event: 172.538 Loading class java/util/Collections$EmptyListIterator
Event: 172.538 Loading class java/util/Collections$EmptyListIterator done
Event: 172.580 Loading class java/net/UrlDeserializedState
Event: 172.580 Loading class java/net/UrlDeserializedState done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 172.581 Thread 0x000001600cbd5cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000cfcfd120}: static [Ljava/net/URL;.<clinit>()V> (0x00000000cfcfd120) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 172.582 Thread 0x000001600cbd5cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000cfb157b8}: static Lorg/gradle/internal/classloader/FilteringClassLoader$Spec;.<clinit>()V> (0x00000000cfb157b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 172.582 Thread 0x000001600cbd5cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000cfb249f8}: static Lorg/gradle/internal/classloader/CachingClassLoader$Spec;.<clinit>()V> (0x00000000cfb249f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 172.684 Thread 0x0000016008502db0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf7df8d8}> (0x00000000cf7df8d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 172.685 Thread 0x0000016008502db0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf7e0400}> (0x00000000cf7e0400) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 172.685 Thread 0x0000016008502db0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf7e0f70}> (0x00000000cf7e0f70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 172.691 Thread 0x0000016008502db0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf7f0d50}> (0x00000000cf7f0d50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.819 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf030f78}> (0x00000000cf030f78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.819 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf032420}> (0x00000000cf032420) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.819 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf0338b8}> (0x00000000cf0338b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.819 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf034d30}> (0x00000000cf034d30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.848 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cdb396c0}> (0x00000000cdb396c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.848 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cdb3adc8}> (0x00000000cdb3adc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 180.848 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cdb3e1e0}> (0x00000000cdb3e1e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 181.213 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf8fbd60}> (0x00000000cf8fbd60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 181.214 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf8fd388}> (0x00000000cf8fd388) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 181.214 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf8fea00}> (0x00000000cf8fea00) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 181.214 Thread 0x000001600cbd66d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000cf7002d8}> (0x00000000cf7002d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 181.296 Thread 0x000001600a998610 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ceea58b8}> (0x00000000ceea58b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 183.719 Thread 0x000001600a998100 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d03e0300}> (0x00000000d03e0300) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 181.129 Executing VM operation: ICBufferFull
Event: 181.129 Executing VM operation: ICBufferFull done
Event: 181.193 Executing VM operation: HandshakeAllThreads
Event: 181.193 Executing VM operation: HandshakeAllThreads done
Event: 181.234 Executing VM operation: ICBufferFull
Event: 181.234 Executing VM operation: ICBufferFull done
Event: 181.376 Executing VM operation: HandshakeAllThreads
Event: 181.376 Executing VM operation: HandshakeAllThreads done
Event: 181.452 Executing VM operation: HandshakeAllThreads
Event: 181.452 Executing VM operation: HandshakeAllThreads done
Event: 181.503 Executing VM operation: G1CollectForAllocation
Event: 181.509 Executing VM operation: G1CollectForAllocation done
Event: 181.925 Executing VM operation: ICBufferFull
Event: 181.925 Executing VM operation: ICBufferFull done
Event: 182.641 Executing VM operation: G1CollectForAllocation
Event: 182.647 Executing VM operation: G1CollectForAllocation done
Event: 183.652 Executing VM operation: Cleanup
Event: 183.652 Executing VM operation: Cleanup done
Event: 183.743 Executing VM operation: G1CollectForAllocation
Event: 183.746 Executing VM operation: G1CollectForAllocation done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 181.470 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac4ff190
Event: 181.470 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac4ff910
Event: 181.470 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac53ba90
Event: 181.470 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac5d1890
Event: 181.470 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac5f8d90
Event: 181.470 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac61a490
Event: 181.471 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac6cd810
Event: 181.471 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac754f10
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac7fef90
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac803690
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac811b10
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac818010
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac81c410
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac841710
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac841f90
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac842410
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac844410
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac845e90
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac84d190
Event: 181.472 Thread 0x0000015fbfa80a20 flushing  nmethod 0x0000015fac908510

Events (20 events):
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016014d131a0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x000001600a9976e0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x000001600a997bf0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0dfb0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0d080
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0cb70
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0daa0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0d590
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0fe10
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0e4c0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0c660
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0e9d0
Event: 183.708 Thread 0x000001600a998100 Thread added: 0x0000016012e0eee0
Event: 183.729 Thread 0x000001600a998100 Thread added: 0x0000016012e0f3f0
Event: 183.730 Thread 0x000001600a998100 Thread added: 0x0000016012e0f900
Event: 183.730 Thread 0x000001600a998100 Thread added: 0x000001600d3a6a80
Event: 183.733 Thread 0x000001600c3de740 Thread added: 0x000001600c0430a0
Event: 183.738 Thread 0x000001600c3de740 Thread added: 0x000001600c0435f0
Event: 183.885 Thread 0x0000016008503ce0 Thread exited: 0x0000016008503ce0
Event: 183.892 Thread 0x00000160125f57a0 Thread added: 0x000001600d3a6f90


Dynamic libraries:
0x00007ff7c5f70000 - 0x00007ff7c5f7e000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\java.exe
0x00007ffde7920000 - 0x00007ffde7b85000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffde5dc0000 - 0x00007ffde5e89000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffde4c80000 - 0x00007ffde5068000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffde53c0000 - 0x00007ffde550b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffddd1d0000 - 0x00007ffddd1e7000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jli.dll
0x00007ffddb8d0000 - 0x00007ffddb8eb000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\VCRUNTIME140.dll
0x00007ffde5e90000 - 0x00007ffde605a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffde5510000 - 0x00007ffde5537000 	C:\WINDOWS\System32\win32u.dll
0x00007ffde56c0000 - 0x00007ffde56eb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdc89b0000 - 0x00007ffdc8c4a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffde5070000 - 0x00007ffde51a7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffde6520000 - 0x00007ffde65c9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffde51b0000 - 0x00007ffde5253000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffde7180000 - 0x00007ffde71b0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffddd9b0000 - 0x00007ffddd9bc000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\vcruntime140_1.dll
0x00007ffdcb0d0000 - 0x00007ffdcb15d000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\msvcp140.dll
0x00007ffd26ac0000 - 0x00007ffd2772c000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\server\jvm.dll
0x00007ffde6200000 - 0x00007ffde62b3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffde7750000 - 0x00007ffde77f6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffde6da0000 - 0x00007ffde6eb5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffde6ec0000 - 0x00007ffde6f34000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffde3660000 - 0x00007ffde36be000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffddd340000 - 0x00007ffddd34b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdd7780000 - 0x00007ffdd77b5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffde3640000 - 0x00007ffde3654000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffde3930000 - 0x00007ffde394b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffddd1c0000 - 0x00007ffddd1ca000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jimage.dll
0x00007ffde1a50000 - 0x00007ffde1c91000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffde5910000 - 0x00007ffde5c95000 	C:\WINDOWS\System32\combase.dll
0x00007ffde5700000 - 0x00007ffde57e1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffdc6f60000 - 0x00007ffdc6f99000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffde5320000 - 0x00007ffde53b9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffddca20000 - 0x00007ffddca2e000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\instrument.dll
0x00007ffdcc100000 - 0x00007ffdcc125000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\java.dll
0x00007ffdb6f70000 - 0x00007ffdb7047000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\jsvml.dll
0x00007ffde65d0000 - 0x00007ffde6d12000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffde5540000 - 0x00007ffde56b4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffde26b0000 - 0x00007ffde2f08000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffde5cc0000 - 0x00007ffde5db1000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffde7810000 - 0x00007ffde787a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffde4980000 - 0x00007ffde49af000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdcc9f0000 - 0x00007ffdcca08000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\zip.dll
0x00007ffdcc180000 - 0x00007ffdcc199000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\net.dll
0x00007ffddee10000 - 0x00007ffddef2e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffde3ec0000 - 0x00007ffde3f2a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffdcc0d0000 - 0x00007ffdcc0e6000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\nio.dll
0x00007ffdcc600000 - 0x00007ffdcc610000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\verify.dll
0x00007ffdca250000 - 0x00007ffdca277000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffd9e160000 - 0x00007ffd9e2a4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffdcc4e0000 - 0x00007ffdcc4e9000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\management.dll
0x00007ffdcc460000 - 0x00007ffdcc46b000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\management_ext.dll
0x00007ffde56f0000 - 0x00007ffde56f8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffde4170000 - 0x00007ffde418b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffde3880000 - 0x00007ffde38ba000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffde3f60000 - 0x00007ffde3f8b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffde4950000 - 0x00007ffde4976000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffde4190000 - 0x00007ffde419c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffde32a0000 - 0x00007ffde32d3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffde7800000 - 0x00007ffde780a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffddf350000 - 0x00007ffddf36f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffddf1b0000 - 0x00007ffddf1d5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffde3330000 - 0x00007ffde3457000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffdcc340000 - 0x00007ffdcc349000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\extnet.dll
0x00007ffdcc210000 - 0x00007ffdcc21e000 	C:\Users\<USER>\.jdks\corretto-17.0.14\bin\sunmscapi.dll
0x00007ffde4a70000 - 0x00007ffde4be7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffde4390000 - 0x00007ffde43c0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffde4340000 - 0x00007ffde437f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffd77f50000 - 0x00007ffd77f58000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffdde950000 - 0x00007ffdde95b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffddf4b0000 - 0x00007ffddf536000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffdbfac0000 - 0x00007ffdbfad8000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffdbfa80000 - 0x00007ffdbfa92000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffdbfa40000 - 0x00007ffdbfa70000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffdbfa10000 - 0x00007ffdbfa30000 	C:\WINDOWS\system32\wshbth.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.14\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Users\<USER>\.jdks\corretto-17.0.14\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx1024m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 526385152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 643825664                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\openlogic-openjdk-jre-8u382-b05-windows-32
PATH=C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Windows;C:\Windows\System32;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Program Files\HP\OMEN-Broadcast\Common;C:\Program Files\Git\cmd;C:\Program Files\HP\HP One Agent;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
USERNAME=karan
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 5 days 0:12 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 116 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv
Processor Information for all 16 processors :
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801

Memory: 4k page, system-wide physical 32038M (6360M free)
TotalPageFile size 130342M (AvailPageFile size 591M)
current process WorkingSet (physical memory assigned to process): 649M, peak: 691M
current process commit charge ("private bytes"): 716M, peak: 751M

vm_info: OpenJDK 64-Bit Server VM (17.0.14+7-LTS) for windows-amd64 JRE (17.0.14+7-LTS), built on Jan  7 2025 20:14:24 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
